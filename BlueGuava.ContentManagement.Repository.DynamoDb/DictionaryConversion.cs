﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace BlueGuava.ContentManagement.Repository.DynamoDb;

public static class DictionaryConversion
{
    public static Dictionary<TKey, TValue>? Convert<TKey, TValue>(this Dictionary<string, TValue> dictionary,
        Func<string, TKey> keySelector) where TK<PERSON> : struct
    {
        return Convert(dictionary, keySelector, v => v);
    }

    public static Dictionary<TKey, TValue>? Convert<TKey, TItem, TValue>(this Dictionary<string, TItem> dictionary,
        Func<string, TKey> keySelector, Func<TItem, TValue> valueSelector) where TKey : struct
    {
        return dictionary?.Where(kvp => kvp.Value != null)
            ?.Select(kvp => new { Key = keySelector(kvp.Key), Value = valueSelector(kvp.Value) })
            ?.Where(kvp => kvp.Value != null)?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    public static Dictionary<string, TValue>? Convert<TKey, TValue>(this Dictionary<TKey, TValue> dictionary)
        where TK<PERSON> : notnull
    {
        return Convert(dictionary, v => v);
    }

    public static Dictionary<string, TValue>? Convert<TKey, TItem, TValue>(this Dictionary<TKey, TItem> dictionary,
        Func<TItem, TValue> valueSelector) where TKey : notnull
    {
        return dictionary?.Where(kvp => kvp.Value != null)
            ?.Select(kvp => new { Key = $"{kvp.Key}", Value = valueSelector(kvp.Value) })
            ?.Where(kvp => kvp.Value != null)?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }
}