﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;

public class ContentDto : ContentRefDto
{
    public const string ContentFileNameIndex = nameof(OriginalFileName) + "-index";
    public const string ExternalIdIndex = nameof(ExternalId) + "-index";
    //public const string ContentTypeIndex = nameof(Type) + "-" + nameof(PublishedDate) + "-index";

    /// <summary>
    /// Primary type of the Content
    /// </summary>
    //[DynamoDBGlobalSecondaryIndexRangeKey]
    public ContentType Type { get; set; }

    public string? Color { get; set; }

    /// <summary>
    /// A <see cref="List{Credit}"/> representing Cast and Crew
    /// </summary>
    public List<Credit>? Credits { get; set; }

    /// <summary>
    /// Former Globalizations
    /// <para>
    /// <example>For example :
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description><see href="https://en.wikipedia.org/wiki/IETF_language_tag">IETF language tag</see></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description><see cref="Localization" /></description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, Localization>? Localizations { get; set; }

    /// <summary>
    /// Availability zones and Ratings, Advisory
    /// <para>
    /// <example>For example:
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description><see href="https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2">CountryCode</see></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description><see cref="Availability" /></description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, Availability>? ExhibitionWindow { get; set; }

    /// <summary>
    /// Contains categorizations of the content.
    /// <para>
    /// <example>For example Genres, Categories, Locations, Albums:
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description><see cref="CategorizationType" /></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description>Contains list of string.</description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
/*    [Obsolete("Use Labels instead")]
    public Dictionary<string, List<string>>? Categorizations { get; set; }
*/

    /// <summary>
    /// Contains labels of the content.
    /// <para>
    /// <example>For example Genres, Categories, Locations, Albums:
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description><see cref="LabelType" /></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description>Contains list of string.</description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, List<string>>? Labels { get; set; }

    /// <summary>
    /// Contains designs for the applications.
    /// <para>
    /// <example>For example DarkDesign, LighDesign:
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description><see cref="DesignTypes" /></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description><see cref="ContentDesign" /></description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, ContentDesign>? Themes { get; set; }

    /// <summary>
    /// Contains files and attached documents.
    /// </summary>
    public List<Asset>? Assets { get; set; }

    /// <summary>
    /// The property represents a <see cref="DateTime"/> when <see cref="Published" /> status changed to <see langword="true"/>.
    /// </summary>
    public DateTime? PublishedDate { get; set; }

    /// <summary>
    /// The property represents a <see cref="DateTime"/> when the content was originally released
    /// </summary>
    public DateTime? ReleaseDate { get; set; }

    /// <value>
    /// <see langword="true" /> if there is already a content in the databse with the same <see cref="OriginalFileName" />
    /// </value>
    public bool Duplicate { get; set; }

    /// <summary>
    /// <see langword="true" /> if the customers are able to download the content
    /// </summary>
    public bool Downloadable { get; set; }

    /// <summary>
    /// <see langword="true" /> AllowMinting
    /// </summary>
    public bool AllowMinting { get; set; }

    public bool AllowEmailNotification { get; set; }
    /// <summary>
    /// <see langword="true" /> AllowRemix
    /// </summary>
    public bool AllowRemix { get; set; }

    /// <summary>
    /// <see langword="true" /> if the customers are able to comment the content
    /// </summary>
    public bool AllowComments { get; set; }

    /// <summary>
    /// <see langword="true" /> if the customers are able to rate the content
    /// </summary>
    public bool AllowUserRating { get; set; }

    /// <summary>
    /// <see langword="true" /> if the customers are able to sideshow the content
    /// </summary>
    public bool AllowSideshow { get; set; }

    public bool AllowLyrics { get; set; }

    public bool AllowUpcoming { get; set; }

    /// <summary>
    /// <see langword="true" /> if the content is visible for the customers
    /// </summary>
    public bool Published { get; set; }

    /// <summary>
    /// Represents a unique id of the user who ingested the content into the database
    /// </summary>
    public Guid OwnerId { get; set; }

    /// <summary>
    /// Represents a <see cref="Guid"/> unique id of the user who last modified the content
    /// </summary>
    public Guid LastModifiedBy { get; set; }

    /// <summary>
    /// Represents a <see cref="DateTime"/> when the content was last modified
    /// </summary>
    public DateTime LastModifiedDate { get; set; }

    /// <summary>
    /// Represents a <see cref="DateTime"/> when the content was created
    /// </summary>
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Contains the subsription tier ids as a <see cref="List{Guid}"/> where the content is available
    /// </summary>
    public List<Guid>? AuthGroupIds { get; set; }

    /// <summary>
    /// Contains reference lists with other entites which are connected with the content.
    /// <para>
    /// <example>For example artists, related lists, partners, items:
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description><see cref="ReferenceListType" /></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description>List of <see cref="ItemRef" /></description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, List<ItemRef>>? ReferenceItems { get; set; }

    /// <summary>
    /// Contains extra properties for the apps.
    /// <para>
    /// <example>For example color, backgroundcolor, effects:
    /// <list type="bullet|number|table">
    /// <listheader>
    ///     <term>key</term>
    ///     <description>the name of the property, eg: <c>Content:Color</c></description>
    /// </listheader>
    ///     <item>
    ///         <term>value</term>
    ///         <description>the value of the property, eg: <c>#0000FF</c></description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, string>? Properties { get; set; }

    /// <value>
    /// The property represents a <see cref="string"/> unique identifier in an external environment
    /// </value>
    public string? ExternalId { get; set; }

    /// <summary>
    /// The property represents a unique <see cref="string"/> identifier to connect with other contents in our DB (eg: Series, Season, Episode, Collection)
    /// </summary>
    public string? ReferenceId { get; set; }

    /// <summary>
    /// The original name of the content, initially generated from <see cref="OriginalFileName" /> at ingest
    /// </summary>
    public string? OriginalTitle { get; set; }

    /// <summary>
    /// Represents a <see cref="string"/> value of the original file name this content was generated for
    /// </summary>
    public string? OriginalFileName { get; set; }

    /// <summary>
    /// Duration metadata from origin file
    /// </summary>
    public int Duration { get; set; }

    /// <summary>
    /// The original language of the content (IETF language tag)
    /// </summary>
    public string? OriginalLanguage { get; set; }

    /// <summary>
    /// Original transcript value
    /// </summary>
    public string? OriginalTranscript { get; set; }

    /// <summary>
    /// Archival <see href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-lifecycle-mgmt.html">policy</see> for S3
    /// </summary>
    public string? ArchivalPolicy { get; set; }

    /// <summary>
    /// Deletion <see href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-lifecycle-mgmt.html">policy</see> for S3
    /// </summary>
    public string? DeletionPolicy { get; set; }

    /// <summary>
    /// Comprehend entity values group by <see cref="EntityType">categories</see>
    /// <para>
    /// <example>For example color, QUANTITY: hundreds, cards
    /// <list type="bullet">
    ///     <item>
    ///         <term>key</term>
    ///         <description><see cref="EntityType" /></description>
    ///     </item>
    ///     <item>
    ///         <term>value</term>
    ///         <description>the value of the comprehend result</description>
    ///     </item>
    /// </list>
    /// </example>
    /// </para>
    /// </summary>
    public Dictionary<string, List<string>>? Entities { get; set; }

    /// <summary>
    /// List of providers and their playback urls where the currrent content is available to watch
    /// </summary>
    public List<WhereToWatch>? WhereToWatch { get; set; }

    /// <summary>
    /// Price of merchandise-type content in reward points
    /// </summary>
    public int? InternalPrice { get; set; }

    /// <summary>
    /// Price of merchandise-type content in cryptocurrency
    /// </summary>
    public double? TokenPrice { get; set; }

    /// <summary>
    /// Name of the cryptocurrency in <see cref="TokenPrice"/>
    /// </summary>
    public string? TokenCurrency { get; set; }

    /// <summary>
    /// Chime and Live processing status
    /// </summary>
    public ProcessingStatus ProcessingStatus { get; set; }

    public bool AllowChat { get; set; }

    public Visibility Visibility { get; set; }

    public PublishingRule PublishingRule { get; set; }

    public ContentNotification Notification { get; set; }

    public List<string> Relations { get; set; }

    public DateTime? AutoPublishDate { get; set; }
    public DateTime? AutoUnPublishDate { get; set; }

    /// <summary>
    /// This property is used for soft delete
    /// </summary>
    public bool IsDeleted { get; set; }

    public bool IsDrmEnabled { get; set; }

    public string? RewardCode { get; set; }

    /// <summary>
    /// Represents a unique id of the user who is the original owner of the content into the database
    /// </summary>
    public Guid? OriginalOwnerId { get; set; }

    public bool IsRewarded { get; set; }

    public string? PollHtml { get; set; }

}