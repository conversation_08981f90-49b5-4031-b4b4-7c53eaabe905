﻿using System.Linq;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;

public class ContentWriteDto : ContentDto
{
    public string? SearchMeta { get; set; }

    internal static ContentWriteDto? FromEntity(Content? entity)
    {
        if (entity == null) return null;
        return new ContentWriteDto
        {
            Id = entity.Id,
            Type = entity.Type,

            AllowUserRating = entity.AllowUserRating ?? false,
            AllowComments = entity.AllowComments ?? false,
            AllowMinting = entity.AllowMinting ?? false,
            AllowEmailNotification = entity.AllowEmailNotification ?? false,
            AllowChat = entity.AllowChat ?? false,
            AllowRemix = entity.AllowRemix ?? false,
            AllowSideshow = entity.AllowSideshow ?? false,
            AllowLyrics = entity.AllowLyrics ?? false,
            AllowUpcoming = entity.AllowUpcoming ?? false,
            Assets = entity.Assets?.TrimItems().NullIfEmpty(),
            AuthGroupIds = entity.AuthGroupIds?.TrimItems()?.NullIfEmpty(),
            Color = entity.Color,
            CreatedDate = entity.CreatedDate,
            Credits = entity.Credits?.TrimItems()?.NullIfEmpty(),
            Downloadable = entity.Downloadable ?? false,
            Duplicate = entity.Duplicate ?? false,
            Duration = entity.Duration ?? 0,
            ExhibitionWindow = entity.ExhibitionWindow?.TrimItems()?.NullIfEmpty(),
            ExternalId = entity.ExternalId,
            LastModifiedBy = entity.LastModifiedBy,
            LastModifiedDate = entity.LastModifiedDate,
            Localizations = entity.Localizations?.TrimItems()?.NullIfEmpty(),
            OriginalFileName = entity.OriginalFileName,
            OriginalTitle = entity.OriginalTitle,
            OwnerId = entity.OwnerId,
            Properties = entity.Properties?.TrimItems()?.NullIfEmpty(),
            Published = entity.Published,
            PublishedDate = entity.PublishedDate,
            ReferenceId = entity.ReferenceId,
            ReleaseDate = entity.ReleaseDate,
            Themes = entity.Themes?.Convert()?.NullIfEmpty(),
            OriginalLanguage = entity.OriginalLanguage,
            OriginalTranscript = entity.OriginalTranscript,
            ArchivalPolicy = entity.ArchivalPolicy,
            DeletionPolicy = entity.DeletionPolicy,
            Entities = entity.Entities?.Convert(v => v.TrimItems()?.NullIfEmpty())?.NullIfEmpty(),
            WhereToWatch = entity.WhereToWatch?.NullIfEmpty(),
            InternalPrice = entity.InternalPrice,
            TokenPrice = entity.TokenPrice,
            TokenCurrency = entity.TokenCurrency,
            ProcessingStatus = entity.ProcessingStatus ?? ProcessingStatus.None,
            Labels = entity.Labels?.Convert(v => v.TrimItems()?.Distinct().ToList().NullIfEmpty())?.NullIfEmpty(),
            Visibility = entity.Visibility ?? Visibility.Private,
            PublishingRule = entity.PublishingRule ?? PublishingRule.Upcoming,
            Notification = entity.Notification ?? ContentNotification.Followed,
            Relations = entity.Relations,
            AutoPublishDate = entity.AutoPublishDate,
            AutoUnPublishDate = entity.AutoUnPublishDate,
            IsDeleted = entity.IsDeleted ?? false,
            IsDrmEnabled = entity.IsDrmEnabled ?? false,
            RewardCode = entity.RewardCode,
            OriginalOwnerId = entity.OriginalOwnerId,
            IsRewarded = entity.IsRewarded ?? false,
            PollHtml = entity.PollHtml,
        };
    }
}