﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.MarkerManagement.Models.Abstraction;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;

public class ContentReadDto : ContentDto
{
    /// <summary>
    /// The property represents a <see langword="DateTime"/> when the last OpenSearch mass re-indexing was triggered
    /// </summary>
    public DateTime? ForceIndexed { get; set; }

    internal Content? ToEntity()
    {
        return new Content
        {
            Id = Id,
            Type = Type,

            AllowUserRating = AllowUserRating,
            AllowComments = AllowComments,
            AllowMinting = AllowMinting,
            AllowEmailNotification = AllowEmailNotification,
            AllowChat = AllowChat,
            AllowRemix = AllowRemix,
            AllowSideshow = AllowSideshow,
            AllowLyrics = AllowLyrics,
            AllowUpcoming = AllowUpcoming,
            Assets = Assets ?? new List<Asset>(),
            AuthGroupIds = AuthGroupIds ?? new List<Guid>(),
            Color = Color,
            CreatedDate = CreatedDate,
            Credits = Credits ?? new List<Credit>(),
            Downloadable = Downloadable,
            Duplicate = Duplicate,
            Duration = Duration,
            ExhibitionWindow = ExhibitionWindow ?? new Dictionary<string, Availability>(),
            ExternalId = ExternalId,
            ForceIndexed = ForceIndexed,
            LastModifiedBy = LastModifiedBy,
            LastModifiedDate = LastModifiedDate,
            Localizations = Localizations ?? new Dictionary<string, Localization>(),
            OriginalFileName = OriginalFileName,
            OriginalTitle = OriginalTitle,
            OwnerId = OwnerId,
            Properties = Properties ?? new Dictionary<string, string>(),
            Published = Published,
            PublishedDate = PublishedDate,
            ReferenceId = ReferenceId,
            ReleaseDate = ReleaseDate,
            Themes = Themes?.Convert(Parse<DesignTypes>) ?? new Dictionary<DesignTypes, ContentDesign>(),
            OriginalLanguage = OriginalLanguage,
            OriginalTranscript = OriginalTranscript,
            ArchivalPolicy = ArchivalPolicy,
            DeletionPolicy = DeletionPolicy,
            Entities = Entities?.Convert(Parse<EntityType>) ?? new Dictionary<EntityType, List<string>>(),
            WhereToWatch = WhereToWatch ?? new List<WhereToWatch>(),
            InternalPrice = InternalPrice,
            TokenPrice = TokenPrice,
            TokenCurrency = TokenCurrency,
            ProcessingStatus = ProcessingStatus,
            Visibility = Visibility,
            Labels = Labels?.Convert(Parse<LabelType>) ?? new Dictionary<LabelType, List<string>>(),
            PublishingRule = PublishingRule,
            Notification = Notification,
            Relations = Relations,
            AutoPublishDate = AutoPublishDate,
            AutoUnPublishDate = AutoUnPublishDate,
            IsDeleted = IsDeleted,
            IsDrmEnabled = IsDrmEnabled,
            RewardCode = RewardCode,
            OriginalOwnerId = OriginalOwnerId,
            IsRewarded = IsRewarded,
            PollHtml = PollHtml,
        };
    }

    private static T Parse<T>(string val) where T : struct
    {
        return (T)Enum.Parse(typeof(T), val);
    }
}