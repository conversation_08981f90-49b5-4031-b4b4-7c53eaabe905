﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;
//using BlueGuava.Extensions.AWS.QDBL;
using BlueGuava.Extensions.Logging;
using BlueGuava.MessageQueuing;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Asset = BlueGuava.ContentManagement.Packages.Entities.V2.Asset;
using Content = BlueGuava.ContentManagement.Packages.Entities.V2.Content;
using BlueGuava.Reporting.Messages.Entities;
using BlueGuava.ContentManagement.Common.Filters;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2
{
    public partial class ContentRepository : IContentRepository
    {
        private readonly ILogger<ContentRepository> logger;
        private readonly IFeatureManager featureManager;
        private readonly IDynamoDBContext dynamoContext;
        //private readonly ILedgerRepository<ContentLedger> ledgerRepository;
        private readonly ICorrelationContextAccessor correlationContextAccessor;
        private readonly IMessageQueue<PlatformMetricMessage> platformMetricMessages;

        public ContentRepository(ILogger<ContentRepository> logger, IFeatureManager featureManager, IDynamoDBContext dynamoContext,
        //ILedgerRepository<ContentLedger> ledgerRepository,
        ICorrelationContextAccessor correlationContextAccessor, IMessageQueue<PlatformMetricMessage> platformMetricMessages)
        {
            this.logger = logger;
            this.featureManager = featureManager;
            this.dynamoContext = dynamoContext;
            //this.ledgerRepository = ledgerRepository;
            this.correlationContextAccessor = correlationContextAccessor;
            this.platformMetricMessages = platformMetricMessages;
        }

        //[TimeCheck("Retrieve-Repository")]
        public async Task<Content?> Retrieve(Guid contentId)
        {
            try
            {
                if (logger.IsEnabled(LogLevel.Debug))
                    logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Retrieve))
                        .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

                var content = await dynamoContext.LoadAsync<ContentReadDto>(contentId);
                return content?.ToEntity();
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Retrieve))
                        .Log(LogLevel.Error, ex, "ContentId: {ContentId}", contentId);
                return null;
            }
        }

        public async Task<Content?> Retrieve(string externalId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Retrieve))
                    .Log(LogLevel.Debug, "ExternalId: {ExternalId}", externalId);

            var response = await RetrieveByExternalId(externalId);
            if (response != null)
            {
                var content = await dynamoContext.LoadAsync<ContentReadDto>(response.Id);
                return content?.ToEntity();
            }

            return null;
        }

        public async Task<Asset?> RetrieveAsset(Guid contentId, Guid assetId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Retrieve))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}, AssetId: {AssetId}", contentId, assetId);

            var content = await dynamoContext.LoadAsync<ContentReadDto>(contentId);
            return content?.Assets?.FirstOrDefault(x => x?.Id == assetId);
        }

        public async Task Create(Content content)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Create))
                    .Log(LogLevel.Debug, "Content: {@Content}", content);

            await dynamoContext.SaveAsync(ContentWriteDto.FromEntity(content));
            //await SaveQDBL(content);
        }

        public async Task Update(Content content)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Update))
                    .Log(LogLevel.Debug, "Content: {@Content}", content);

            await dynamoContext.SaveAsync(ContentWriteDto.FromEntity(content));
            //await UpdateQDBL(content);
            await SendPlatformMetricMessage();
        }

        public async Task Delete(Guid contentId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(Delete))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            await dynamoContext.DeleteAsync<ContentWriteDto>(contentId);
        }

        public async IAsyncEnumerable<Guid> ScanThrough(ContentSearch args)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(ScanThrough))
                    .Log(LogLevel.Debug, ContentSearch.LogFormat, args.GetValues());

            var search = dynamoContext.FromScanAsync<ContentSearchDto>(new ScanOperationConfig
            {
                Select = SelectValues.SpecificAttributes,
                AttributesToGet = new List<string> { nameof(ContentSearchDto.Id) },
                FilterExpression = new Expression
                {
                    ExpressionStatement = "NOT begins_with(#id, :p)",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#id", nameof(ContentSearchDto.Id) }
                    },
                                ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":p", new Primitive("P:") }
                    }
                }
            });

            while (!search.IsDone)
            {
                var result = await search.GetNextSetAsync();
                foreach (var item in result)
                    yield return item.Id;
            }
        }

        public async IAsyncEnumerable<Guid> ScanThroughWithType(int type)
        {
            if (logger.IsEnabled(LogLevel.Debug))
            {
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(ScanThroughWithType))
                      .Log(LogLevel.Debug, $"Type: {type}");
            }

            var scanConfig = new ScanOperationConfig
            {
                Select = SelectValues.SpecificAttributes,
                AttributesToGet = new List<string> { nameof(ContentSearchDto.Id) },
                FilterExpression = new Expression
                {
                    ExpressionStatement = "#type = :typeValue AND NOT begins_with(#id, :p)",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#type", "Type" },
                        { "#id", nameof(ContentSearchDto.Id) }
                    },
                                ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":typeValue", new Primitive(type.ToString(), true) },
                        { ":p", new Primitive("P:") }
                    }
                }
            };

            var search = dynamoContext.FromScanAsync<ContentSearchDto>(scanConfig);

            while (!search.IsDone)
            {
                IReadOnlyCollection<ContentSearchDto>? result;
                try
                {
                    result = await search.GetNextSetAsync();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error fetching results from scan.");
                    yield break;
                }

                if (result == null || result.Count == 0)
                    continue;

                foreach (var item in result)
                {
                    if (item?.Id != null && item.Id != Guid.Empty)
                        yield return item.Id;
                }
            }
        }


        public async Task<bool> DoesExist(Content content)
        {
            if (string.IsNullOrEmpty(content.OriginalFileName)) return false;

            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(DoesExist))
                    .Log(LogLevel.Debug, "Content: {Content}", content.ToJson());

            var config = new QueryOperationConfig
            {
                IndexName = ContentDto.ContentFileNameIndex,
                Select = SelectValues.SpecificAttributes,
                AttributesToGet = new List<string>()
                {
                    nameof(ContentDto.Id),
                    nameof(ContentDto.OriginalFileName),
                },
            };

            config.Filter = new QueryFilter();
            config.Filter.AddCondition(nameof(ContentDto.OriginalFileName), QueryOperator.Equal, content.OriginalFileName.ToLower());

            var response = await dynamoContext.FromQueryAsync<ContentRefDto>(config).GetRemainingAsync();

            return response.Count > 0;
        }

        public async Task<bool> DoesExist(string? externalId)
        {

            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(DoesExist))
                    .Log(LogLevel.Debug, "ExternalId: {ExternalId}", externalId);

            if (string.IsNullOrEmpty(externalId)) return false;

            var response = await RetrieveByExternalId(externalId);

            return response != null;
        }

        private async Task<ContentRefDto?> RetrieveByExternalId(string externalId)
        {
            var config = new QueryOperationConfig
            {
                IndexName = ContentDto.ExternalIdIndex,
                Select = SelectValues.AllAttributes,
            };

            config.Filter = new QueryFilter();
            config.Filter.AddCondition(nameof(ContentDto.ExternalId), QueryOperator.Equal, externalId);

            var response = await dynamoContext.FromQueryAsync<ContentRefDto>(config).GetRemainingAsync();

            return response?.FirstOrDefault();
        }

        public async IAsyncEnumerable<Content?> RetrieveAll()
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrieveAll))
                    .Log(LogLevel.Debug);

            var search = dynamoContext.FromScanAsync<ContentReadDto>(new ScanOperationConfig
            {
                Select = SelectValues.AllAttributes,
                FilterExpression = new Expression
                {
                    ExpressionStatement = "NOT begins_with(#id, :p)",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#id", nameof(ContentReadDto.Id) }
                    },
                                ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":p", new Primitive("P:") }
                    }
                }
            });

            while (!search.IsDone)
            {
                // Use GetNextSetAsync instead of GetRemainingAsync to prevent loading all items at once
                var result = await search.GetNextSetAsync();
                foreach (var item in result)
                    yield return item.ToEntity();
            }
        }

        public async Task BatchSave(List<Content> contents)
        {
            var batch = dynamoContext.CreateBatchWrite<ContentWriteDto>();
            batch.AddPutItems(contents.Select(x => ContentWriteDto.FromEntity(x))!);

            await batch.ExecuteAsync();
        }

        public async IAsyncEnumerable<Guid> RetrieveAllIds()
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrieveAllIds))
                    .Log(LogLevel.Debug);

            var search = dynamoContext.FromScanAsync<ContentReadDto>(new ScanOperationConfig
            {
                Select = SelectValues.SpecificAttributes,
                AttributesToGet = new List<string> { nameof(ContentDto.Id) },
                FilterExpression = new Expression
                {
                    ExpressionStatement = "NOT begins_with(#id, :p)",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#id", nameof(ContentReadDto.Id) }
                    },
                                ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":p", new Primitive("P:") }
                    }
                }
            });


            while (!search.IsDone)
            {
                // Use GetNextSetAsync instead of GetRemainingAsync to prevent loading all items at once
                var result = await search.GetNextSetAsync();
                foreach (var item in result)
                    yield return item.Id;
            }
        }

        public async Task<IEnumerable<Content?>?> BatchGet(List<Guid> args)
        {
            var batch = dynamoContext.CreateBatchGet<ContentReadDto>();

            args.ForEach(id => batch.AddKey(id));

            await batch.ExecuteAsync();

            return batch.Results?.Select(x => x.ToEntity());
        }
        /*
                private async Task SaveQDBL(Content content)
                {
                    if (!await featureManager.IsEnabledAsync("AllowContentQDBL"))
                    {
                        logger.LogDebug("Content QDBL is turned off by FeatureManager");
                        return;
                    }
                    var result = await ledgerRepository.Retrieve(content.Id.ToString());
                    if (result == null) await ledgerRepository.Save(ContentLedger.FromEntity(content));
                }

                private async Task UpdateQDBL(Content content)
                {
                    if (!await featureManager.IsEnabledAsync("AllowContentQDBL"))
                    {
                        logger.LogDebug("Content QDBL is turned off by FeatureManager");
                        return;
                    }
                    //var result = await ledgerRepository.Retrieve(content.Id.ToString());
                    //if (result != null)
                        await ledgerRepository.Update(ContentLedger.FromEntity(content), content.Id.ToString());
                    //else await ledgerRepository.Save(ContentLedger.FromEntity(content));
                }
        */
        private async Task SendPlatformMetricMessage()
        {
            try
            {
                var payload = new List<Property>
                {
                    new() { Name = "EntityType", Value = "Content" },
                    new() { Name = "Type", Value = "ContentDistributionContract" },
                    new() { Name = "Timestamp", Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") }
                };

                var platformMetricsMessage = new PlatformMetricMessage()
                {
                    Properties = payload,
                    RunDate = DateTime.UtcNow,
                    TimeStamp = DateTime.UtcNow
                };

                await platformMetricMessages.Enqueue(platformMetricsMessage);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(SendPlatformMetricMessage))
                    .Log(LogLevel.Error, ex, "Sending {messageType} failed", nameof(PlatformMetricMessage));
            }
        }

        public Task<bool> IsHealthy()
        {
            try
            {
                //var table = dynamoContext.GetTargetTable<PlaySessionDto>();
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Failed to connect to DynamoDB");
                return Task.FromResult(false);
            }
        }
    }
}
