﻿using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.DataModel;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;
using System;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2
{
    public partial class ContentRepository : IContentRepository
    {
        public async Task<IEnumerable<ContentPoll>> RetrievePollsByExternalIdAndReferenceId(string externalId, string referenceId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrievePollsByExternalIdAndReferenceId))
                      .Log(LogLevel.Debug, "ExternalId: {ExternalId}, ReferenceId: {ReferenceId}", externalId, referenceId);

            if (string.IsNullOrWhiteSpace(externalId) || string.IsNullOrWhiteSpace(referenceId))
                return new List<ContentPoll>();

            var cfg = new QueryOperationConfig
            {
                IndexName = ContentPollDto.ExternalIdReferenceIdIndex,
                KeyExpression = new Expression
                {
                    ExpressionStatement = "#pk = :v1 AND #sk = :v2",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#pk", nameof(ContentPollDto.ExternalId) },
                        { "#sk", nameof(ContentPollDto.ReferenceId) }
                    },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":v1", externalId },
                        { ":v2", referenceId }
                    }
                },
                Select = SelectValues.AllAttributes
            };

            var search = dynamoContext.FromQueryAsync<ContentPollDto>(cfg);
            var results = new List<ContentPollDto>();

            while (!search.IsDone)
            {
                IReadOnlyCollection<ContentPollDto>? page;
                try
                {
                    page = await search.GetNextSetAsync();
                }
                catch (Exception ex)
                {
                    logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrievePollsByExternalIdAndReferenceId))
                          .Log(LogLevel.Error, ex, "Query failed");
                    break;
                }

                if (page != null && page.Count > 0)
                    results.AddRange(page);
            }

            return results?.Select(x => x.ToEntity());
        }

        public async Task<IEnumerable<ContentPoll>> RetrievePollsByReferenceId(string referenceId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrievePollsByReferenceId))
                      .Log(LogLevel.Debug, "ReferenceId: {ReferenceId}", referenceId);

            if (string.IsNullOrWhiteSpace(referenceId))
                return new List<ContentPoll>();

            var cfg = new QueryOperationConfig
            {
                IndexName = ContentPollDto.ReferenceIdIndex,
                KeyExpression = new Expression
                {
                    ExpressionStatement = "#rid = :r",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#rid", nameof(ContentPollDto.ReferenceId) }
                    },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":r", referenceId }
                    }
                },
                Select = SelectValues.AllAttributes
            };

            var search = dynamoContext.FromQueryAsync<ContentPollDto>(cfg);
            var results = new List<ContentPollDto>();

            while (!search.IsDone)
            {
                IReadOnlyCollection<ContentPollDto>? page;
                try
                {
                    page = await search.GetNextSetAsync();
                }
                catch (Exception ex)
                {
                    logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrievePollsByReferenceId))
                          .Log(LogLevel.Error, ex, "Query failed");
                    break;
                }

                if (page != null && page.Count > 0)
                    results.AddRange(page);
            }

            return results?.Select(x => x.ToEntity());
        }

        public async Task SavePoll(ContentPoll poll)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(SavePoll))
                      .Log(LogLevel.Debug, "Poll: {@Poll}", poll);

            if (poll == null) return;

            try
            {
                if (string.IsNullOrEmpty(poll.Id))
                    poll.Id = poll.GetId();
                if (poll.CreatedDate == default)
                    poll.CreatedDate = DateTime.UtcNow;

                await dynamoContext.SaveAsync(poll);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(SavePoll))
                      .Log(LogLevel.Error, ex, "Failed to save poll item");
                throw;
            }
        }

        public async Task BatchSavePolls(IEnumerable<ContentPoll> polls)
        {
            var batch = dynamoContext.CreateBatchWrite<ContentPollDto>();
            foreach (var p in polls)
            {
                if (string.IsNullOrEmpty(p.Id)) p.Id = p.GetId();
                if (p.CreatedDate == default) p.CreatedDate = DateTime.UtcNow;
                batch.AddPutItem(ContentPollDto.FromEntity(p));
            }
            await batch.ExecuteAsync();
        }

    }
}
