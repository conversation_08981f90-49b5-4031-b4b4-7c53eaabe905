﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.Model;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;
using BlueGuava.Extensions.AWS.Paging.DynamoDb;
using BlueGuava.Extensions.Logging;
using BlueGuava.Extensions.Paging;
using CorrelationId;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2;

public class PagingContentRepository : IPagingContentRepository
{
    private readonly ILogger<ContentRepository> logger;
    private readonly IAmazonDynamoDB dynamoClient;
    private readonly IDynamoDBContext dynamoContext;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public PagingContentRepository(ILogger<ContentRepository> logger, IAmazonDynamoDB dynamoClient,
        IDynamoDBContext dynamoContext, ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.dynamoClient = dynamoClient;
        this.dynamoContext = dynamoContext;
        this.correlationContextAccessor = correlationContextAccessor;
    }

    public async Task<PagedResult<Content?>> StartPaging(int itemLimit)
    {
        return await ExecuteSearch(PagingToken.Create<object>(null!, itemLimit, true));
    }

    public async Task<PagedResult<Content?>> GetNextPage(string pagingToken)
    {
        return await ExecuteSearch(PagingToken<object>.Parse(pagingToken));
    }

    private async Task<PagedResult<Content?>> ExecuteSearch(PagingToken<object> token)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(PagingContentRepository), nameof(ExecuteSearch))
                .Log(LogLevel.Debug, "ItemLimit: {ItemLimit}", token.ItemLimit);

        var request = new QueryRequest
        {
            TableName = ContentRefDto.TableName,
            IndexName = "Published-LastModifiedDate-index",
            Limit = token.ItemLimit,
            KeyConditionExpression = "#pub = :pub",
            ExpressionAttributeNames = { ["#pub"] = nameof(ContentDto.Published) },
            ExpressionAttributeValues = { [":pub"] = new AttributeValue { N = "1" } },
            ExclusiveStartKey = token.LastKey
        };

        var response = await dynamoClient.QueryAsync(request);
        var items = response.Items.ConvertAll(Translate);
        var next = response.LastEvaluatedKey.Count == 0
            ? null
            : token.Next(response.LastEvaluatedKey).ToString();
        return PagedResult.Create(items, next);
    }

    private Content? Translate(Dictionary<string, AttributeValue> item)
    {
        return dynamoContext.FromDocument<ContentReadDto>(Document.FromAttributeMap(item)).ToEntity();
    }

    public async Task<PagedResult<ContentPoll>> StartPagingByReference(string referenceId, int itemLimit)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(PagingContentRepository), nameof(StartPagingByReference))
                  .Log(LogLevel.Debug, "ReferenceId: {ReferenceId}, ItemLimit: {ItemLimit}", referenceId, itemLimit);

        var token = PagingToken.Create<object>(null!, itemLimit, true);
        return await ExecuteSearchByReference(referenceId, token);
    }

    public async Task<PagedResult<ContentPoll>> GetNextPageByReference(string referenceId, string pagingToken)
    {
        var token = PagingToken<object>.Parse(pagingToken);
        return await ExecuteSearchByReference(referenceId, token);
    }

    private async Task<PagedResult<ContentPoll>> ExecuteSearchByReference(string referenceId, PagingToken<object> token)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(PagingContentRepository), nameof(ExecuteSearchByReference))
                  .Log(LogLevel.Debug, "ReferenceId: {ReferenceId}, ItemLimit: {ItemLimit}", referenceId, token.ItemLimit);

        if (string.IsNullOrWhiteSpace(referenceId))
            return PagedResult.Create(Enumerable.Empty<ContentPoll>(), null);

        var request = new QueryRequest
        {
            TableName = ContentRefDto.TableName,
            IndexName = ContentPollDto.ReferenceIdIndex,
            Limit = token.ItemLimit,
            KeyConditionExpression = "#rid = :r",
            ExpressionAttributeNames = { ["#rid"] = nameof(ContentPollDto.ReferenceId) },
            ExpressionAttributeValues = { [":r"] = new AttributeValue { S = referenceId } },
            ExclusiveStartKey = token.LastKey
        };

        var response = await dynamoClient.QueryAsync(request);
        var items = response.Items.ConvertAll(TranslatePoll);

        var next = response.LastEvaluatedKey.Count == 0
            ? null
            : token.Next(response.LastEvaluatedKey).ToString();

        return PagedResult.Create(items, next);
    }

    private ContentPoll TranslatePoll(Dictionary<string, AttributeValue> item)
    {
        var dto = dynamoContext.FromDocument<ContentPollDto>(Document.FromAttributeMap(item));
        return dto.ToEntity();
    }
}