﻿using System.Linq;

namespace BlueGuava.ContentManagement.Repository.DynamoDb;

internal static class TextSearch
{
    public static string CreateMeta(params string[] parts)
    {
        parts = parts.Where(HasContent).Select(Lowercase).ToArray();
        return parts.Length == 0 ? string.Empty : string.Join(" ", parts);
    }

    private static bool HasContent(string str)
    {
        return !string.IsNullOrEmpty(str);
    }

    private static string Lowercase(string str)
    {
        return str.ToLowerInvariant().Trim();
    }
}