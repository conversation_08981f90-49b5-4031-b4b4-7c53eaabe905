﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace BlueGuava.ContentManagement.Repository.DynamoDb;

public class ItemRef
{
    public static ItemRef New(Guid id, int n)
    {
        return new ItemRef(id, n);
    }

    public ItemRef()
    {
    }

    public ItemRef(Guid id, int n)
    {
        ContentId = id;
        Position = n;
    }

    public Guid ContentId { get; set; }
    public int Position { get; set; }
}

public static class IdListExtensions
{
    public static List<Guid>? ToIdList(this List<ItemRef>? items)
    {
        return items == null
            ? new List<Guid>()
            : items
                ?.OrderBy(i => i.Position)?.Select(i => i.ContentId)?.ToList();
    }

    public static List<ItemRef>? ToItemList(this List<Guid> ids)
    {
        return ids?.Where(id => id != Guid.Empty)?.Select(ItemRef.New)?.ToList();
    }
}