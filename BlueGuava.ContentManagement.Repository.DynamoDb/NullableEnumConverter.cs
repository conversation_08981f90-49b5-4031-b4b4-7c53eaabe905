﻿using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;

namespace BlueGuava.ContentManagement.Repository.DynamoDb;

internal class NullableEnumConverter<TEnum> : IPropertyConverter where TEnum : struct
{
    public object? FromEntry(DynamoDBEntry entry)
    {
        if (entry.AsDynamoDBNull() != null) return default(TEnum?);
        return DynamoDBEntryConversion.V2.ConvertFromEntry<TEnum>(entry);
    }

    public DynamoDBEntry ToEntry(object? value)
    {
        if (value == null) return DynamoDBNull.Null;
        return DynamoDBEntryConversion.V2.ConvertToEntry((TEnum)value);
    }
}