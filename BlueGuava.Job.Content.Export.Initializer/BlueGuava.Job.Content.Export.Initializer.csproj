﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <AWSProjectType>Lambda</AWSProjectType>
        <LangVersion>12</LangVersion>
        <Version>1.0.0</Version>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.2.2" />
        <PackageReference Include="Amazon.Lambda.Core" Version="2.5.0" />
        <PackageReference Include="Amazon.Lambda.Serialization.Json" Version="2.2.4" />
        <PackageReference Include="Amazon.Lambda.SQSEvents" Version="2.2.0" />
        <PackageReference Include="AWSSDK.SimpleNotificationService" Version="3.7.400.106" />
        <PackageReference Include="BlueGuava.Extensions.Configuration.ParameterStore" Version="8.1.2" />

    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Repository.DynamoDb\BlueGuava.ContentManagement.Repository.DynamoDb.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.OpenSearch\BlueGuava.ContentManagement.Service.OpenSearch.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service\BlueGuava.ContentManagement.Service.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Common\BlueGuava.ContentManagement.Common.csproj" />
    </ItemGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>