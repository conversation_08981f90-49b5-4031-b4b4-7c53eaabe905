﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Amazon.DynamoDBv2.DataModel;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2;
using BlueGuava.ContentManagement.Service.OpenSearch;
using BlueGuava.ContentManagement.Service.V2;
using BlueGuava.Extensions.AWS.MessageQueuing.Amazon.DependencyInjection;
using BlueGuava.Extensions.AWS.Repositories.S3;
using BlueGuava.Extensions.Configuration;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.InMemory.DependencyInjection;
using BlueGuava.Job.Content.Export.Initializer.Services;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.Tracewind.Common;
using CorrelationId;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

namespace BlueGuava.Job.Content.Export.Initializer.Infra;

public sealed class ServiceScopeProvider
{
    private static Task<ServiceProvider> providerTask = null;

    public static async Task<IServiceScope> CreateScope()
    {
        if (providerTask == null)
            providerTask = CreateServiceProvider();
        return (await providerTask).CreateScope();
    }

    private static async Task<ServiceProvider> CreateServiceProvider()
    {
        var hostVersion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
        var hostAssemblyName = System.Reflection.Assembly.GetExecutingAssembly().GetName().Name;
        var hostIpAddress = Environment.MachineName;

        var configuration = await GetConfiguration();

        var services = new ServiceCollection()
                .AddSingleton(configuration).Configure<ModuleInfo>(configuration.GetSection("ModuleInfo"))
                .AddDefaultAWSOptions(configuration.GetAWSOptions())
                .AddAmazonSQS(configuration)
                .AddAmazonMessageQueue<JobUpdate>("JobUpdates.fifo")
                .AddTraceLogQueue(configuration, options =>
                {
                    options.HostAssemblyName = hostAssemblyName;
                    options.HostVersion = hostVersion;
                    options.HostIpAddress = hostIpAddress;
                })
                .AddAWSService<Amazon.DynamoDBv2.IAmazonDynamoDB>()
                .AddTransient<IDynamoDBContext, DynamoDBContext>()
                .AddScoped<IJobProcessor, JobProcessor>()
                .AddScoped<IContentService, ContentService>()
                .AddScoped<IContentRepository, ContentRepository>()
                .AddTracewindServiceJobManagement()
                .AddContentOpenSearch(configuration)
                .AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies())
                .AddMemoryCache()
                .AddLockedInMemoryCache()
                .Configure<S3Bucket>(configuration.GetSection("S3Bucket"))
            ;

        services.ConfigureRepositoryS3();
        services.AddCorrelationId();

        services
            .AddSerilog(x =>
            {
                x.ReadFrom.Configuration(configuration)
                    .Enrich.With(new CodeVersionEnricher(configuration))
                    ;
            });

        return services.BuildServiceProvider();
    }

    private static Task<IConfiguration> configTask = null;

    public static async Task<IConfiguration> GetConfiguration()
    {
        if (configTask == null)
            configTask = CreateConfiguration();
        return await configTask;
    }

    private static async Task<IConfiguration> CreateConfiguration()
    {
        return await Task.Run(() =>
        {
            var staticFile = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", false, true)
                .Build();

            var envConfig = new ConfigurationBuilder()
                .AddConfiguration(staticFile)
                .AddInMemoryCollection(new Dictionary<string, string>
                {
                    ["FeatureManagement:AllowOpenSearch"] = "true"
                })
                .AddEnvironmentVariables()
                .Build();
            var ssmConfig = new ConfigurationBuilder()
                .AddConfiguration(envConfig)
                .AddSystemsManager("/bg-jm/")
                .Build();
            return new ConfigurationBuilder()
                .AddConfiguration(ssmConfig)
                .AddParameterStore("BlueGuava:Job:Content:Export:Initializer:Infra")
                .AddVersionConfiguration()
                .Build();
        });
    }
}