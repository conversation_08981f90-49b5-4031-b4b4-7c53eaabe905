﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Logging;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Tracewind.Services;
using BlueGuava.MessageQueuing;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.Job.Content.Export.Initializer.Services;

public class JobProcessor : IJobProcessor
{
    private readonly ILogger<JobProcessor> logger;
    private readonly IMessageQueue<JobUpdate> jobUpdateMessagingService;
    private readonly ITracewindService tracewindservice;
    private readonly IOpenSearchService openSearchService;
    private readonly IS3Repository s3Repository;
    private readonly IOptionsMonitor<S3Bucket> bucketConfig;

    public JobProcessor(ILogger<JobProcessor> logger,
        IMessageQueue<JobUpdate> jobUpdateMessagingService,
        ITracewindService tracewindservice, IS3Repository s3Repository, IOptionsMonitor<S3Bucket> bucketConfig,
        IOpenSearchService openSearchService)
    {
        this.logger = logger;
        this.jobUpdateMessagingService = jobUpdateMessagingService;
        this.tracewindservice = tracewindservice;
        this.s3Repository = s3Repository;
        this.bucketConfig = bucketConfig;
        this.openSearchService = openSearchService;
    }

    public bool CanProcess(ExportJob job)
    {
        if (job.OutputFileExtension == "csv") return true;

        return false;
    }

    public async Task ProcessJob(ExportJob job)
    {
        try
        {
            var contents = new List<BlueGuava.ContentManagement.Packages.Entities.V2.Content>();
            var searchArgs = JsonConvert.DeserializeObject<ContentSearch>(job.SearchParameters);

            var pageIndex = 0;
            var finished = false;
            while (!finished)
            {
                var searchResult = await openSearchService.Search(searchArgs, 20, pageIndex, searchArgs.SortExpr);

                if (searchResult?.Data != null && searchResult.Data.Count() > 0)
                {
                    contents.AddRange(searchResult?.Data);
                    pageIndex++;
                }
                else
                {
                    finished = true;
                }
            }

            var tracewindMessage = "";
            if (contents.Count > 0)
            {
                var exporter = new ExportCSV();
                var csv = exporter.Write(contents);
                var fileKey = $"contentExport_{DateTime.UtcNow.Ticks}.{job.OutputFileExtension}";

                await s3Repository.PutFileAsync(csv, bucketConfig.CurrentValue.FileExport, fileKey,
                    job.OutputFileExtension);

                var publicUrl = s3Repository.GeneratePreSignedUrl(168, bucketConfig.CurrentValue.FileExport, fileKey);

                await jobUpdateMessagingService.Enqueue(new JobUpdate()
                {
                    Id = job.Id,
                    Type = Consts.CURRENT_JOB_TYPE,
                    StatusMessage =
                        $"{job.OutputFileExtension?.ToUpper()} File Created.{Environment.NewLine}Available until: {DateTime.UtcNow.AddHours(168)}",
                    OutputFileLocation = publicUrl,
                    Status = JobStatus.SUCCEED
                });

                tracewindMessage = $"CSV file generated to S3: {publicUrl}";
            }
            else
            {
                tracewindMessage =
                    $"OpenSearch response does not contains contents with the given search criteria. {job.SearchParameters}";

                await jobUpdateMessagingService.Enqueue(new JobUpdate()
                {
                    Id = job.Id,
                    Type = Consts.CURRENT_JOB_TYPE,
                    StatusMessage = tracewindMessage,
                    Status = JobStatus.SUCCEED
                });
            }

            await tracewindservice.ToSuccessTraceAsync(job, Tracewind.Common.Models.ObjectType.Content, 0,
                tracewindMessage);
        }
        catch (Exception ex)
        {
            logger.Standards(null, nameof(JobProcessor), nameof(ProcessJob))
                .Log(LogLevel.Error, ex, "JobRequest: {JobRequest}", job.ToJson());

            await jobUpdateMessagingService.Enqueue(new JobUpdate()
            {
                Id = job.Id,
                Type = Consts.CURRENT_JOB_TYPE,
                StatusMessage = ex.Message,
                Status = JobStatus.FAILED
            });

            await tracewindservice.ToErrorTraceAsync(job, Tracewind.Common.Models.ObjectType.Content, 1, ex);
        }
    }
}