﻿using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Tracewind.Common.Models;
using Microsoft.Extensions.Configuration;
using Serilog.Core;
using Serilog.Events;
using System;

namespace BlueGuava.Job.Content.Export.Initializer;

public class CodeVersionEnricher : ILogEventEnricher
{
    private readonly ModuleInfo _moduleInfo;
    private readonly EnvironmentOptions _environmentOptions;

    public CodeVersionEnricher(IConfiguration configuration)
    {
        _moduleInfo = configuration.GetSection("ModuleInfo").Get<ModuleInfo>();

        var hostVersion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
        var hostAssemblyName = System.Reflection.Assembly.GetExecutingAssembly().GetName().Name;
        var hostIpAddress = Environment.MachineName;

        _environmentOptions = new EnvironmentOptions
        {
            HostIpAddress = hostIpAddress,
            HostVersion = hostVersion,
            HostAssemblyName = hostAssemblyName
        };
    }

    public void Enrich(
        LogEvent logEvent,
        ILogEventPropertyFactory propertyFactory)
    {
        var enrichProperty = propertyFactory
            .CreateProperty(
                "version",
                _moduleInfo.Version + " " + _environmentOptions.HostVersion);

        logEvent.AddOrUpdateProperty(enrichProperty);
    }
}