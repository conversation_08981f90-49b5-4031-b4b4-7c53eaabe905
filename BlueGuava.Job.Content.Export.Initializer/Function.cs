﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Amazon.Lambda.Core;
using Amazon.Lambda.SQSEvents;
using BlueGuava.Job.Content.Export.Initializer.Infra;
using BlueGuava.Job.Content.Export.Initializer.Services;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.Tracewind.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.Json.JsonSerializer))]

namespace BlueGuava.Job.Content.Export.Initializer;

public class Function
{
    public Function()
    {
    }

    public async Task FunctionHandler(SQSEvent sqsEvent, ILambdaContext context)
    {
        using var scope = await ServiceScopeProvider.CreateScope();
        var processor = scope.ServiceProvider.GetService<IJobProcessor>();
        var envOptions = scope.ServiceProvider.GetService<IOptionsMonitor<EnvironmentOptions>>();

        var logger = scope.ServiceProvider.GetService<ILogger<Function>>();
        if (logger == null)
        {
            context.Logger.LogLine("Logger is null");
            throw new Exception("Logger is null");
        }

        logger.LogInformation("Beginning to process {EventCount} records...", sqsEvent.Records.Count);

        var tasks = new List<Task>();
        SQSEvent.SQSMessage record = default;
        try
        {
            context.Logger.LogLine(
                $"{envOptions?.CurrentValue?.HostAssemblyName} - Version: {envOptions?.CurrentValue?.HostVersion}");

            foreach (var msg in sqsEvent.Records)
            {
                record = msg;
                var job = JsonConvert.DeserializeObject<ExportJob>(msg.Body);

                if (processor.CanProcess(job))
                    tasks.Add(processor.ProcessJob(job));
            }

            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Error processing SNS message {@Message}", record);
        }
    }
}