﻿using System;
using System.Linq;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.MessageCenter.Common;
using BlueGuava.NotificationService.Client;

namespace BlueGuava.ContentManagement.Api.Infrastructure;

/// <summary>
/// Sends messages to the notification service
/// </summary>
public interface IMessagingService
{
    /// <summary>
    /// Send content uploaded event to the uploader user
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="fileName"></param>
    /// <param name="contentName"></param>
    /// <param name="customerId"></param>
    /// <returns></returns>
    Task ContentUploaded(Guid contentId, string? contentName, string? fileName, Guid customerId);

    /// <summary>
    /// Send content published event to the owner user
    /// </summary>
    /// <param name="contentId"></param>
    /// <returns></returns>
    Task ContentPublished(Guid contentId);

    /// <summary>
    /// Send event announcement to all users
    /// </summary>
    /// <returns></returns>
    Task EventAnnouncement(string roomArn, Content content);

    /// <summary>
    /// Send event advertisement to a chatroom
    /// </summary>
    /// <param name="roomArn"></param>
    /// <param name="content"></param>
    /// <returns></returns>
    Task EventAdvertisement(string roomArn, Content content);
}

/// <summary>
/// Sends messages to the notification service
/// </summary>
public class MessagingService : IMessagingService
{
    private readonly INotificationService notification;

    /// <summary>
    ///
    /// </summary>
    /// <param name="notification"></param>
    public MessagingService(INotificationService notification)
    {
        this.notification = notification;
    }

    /// <inheritdoc />
    public async Task ContentUploaded(Guid contentId, string? contentName, string? fileName, Guid customerId)
    {
        await notification.Trigger(EventName.ContentUploaded, customerId, new
        {
            objectType = "Content",
            objectId = contentId,
            objectName = contentName,
            fileName = fileName
        });
    }

    /// <inheritdoc />
    public async Task ContentPublished(Guid contentId)
    {
        await notification.Trigger(EventName.ContentPublished, contentId);
    }

    /// <inheritdoc />
    public async Task EventAnnouncement(string roomArn, Content content)
    {
        /*
        await notification.Trigger(EventName.ChatAnnouncement, roomArn, new
        {
            objectType = "megaphone",
            roomArn = roomArn,
            localizedName = ExtractLocalizedName(content),
            eventId = content.Id,
            releaseDate = content.ReleaseDate?.ToString("u")
        });
        */
    }

    /// <inheritdoc />
    public async Task EventAdvertisement(string roomArn, Content content)
    {
        /*
        await notification.Trigger(EventName.ChatAdvertisement, roomArn, new
        {
            objectType = "bannerad",
            roomArn = roomArn,
            localizedName = ExtractLocalizedName(content),
            eventId = content.Id,
            receivedDate = DateTime.UtcNow.ToString("u")
        });
        */
    }

    private static string ExtractLocalizedName(Content? content)
    {
        if (content?.Localizations == null || !content.Localizations.Any())
            return content?.OriginalTitle ?? string.Empty;
        return (content.Localizations.TryGetValue("en-US", out var loc) ? loc.Name : content.OriginalTitle) ??
               string.Empty;
    }
}