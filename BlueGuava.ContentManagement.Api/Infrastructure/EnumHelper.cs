﻿using System;
using System.Linq;
using System.Runtime.Serialization;

namespace BlueGuava.ContentManagement.Api.Infrastructure;

public static class EnumHelper
{
    public static string FormatEnumValues<TEnum>(Func<TEnum, bool>? filter = null) where TEnum : struct
    {
        if (filter == null) filter = _ => true;
        var values = Enum.GetValues(typeof(TEnum)).Cast<TEnum>().Where(filter);
        return "[{" + string.Join("},{", values.Select(v => $"\"{v}\":\"{GetEnumValue(v)}\"")) + "}]";
    }

    private static string? GetEnumValue<TEnum>(TEnum val) where TEnum : struct
    {
        var field = typeof(TEnum).GetField(val.ToString()!);
        if (field == null) throw new InvalidOperationException($"Could not get enum value: {val}");
        var attribute = Attribute.GetCustomAttribute(field, typeof(EnumMemberAttribute));
        return attribute is EnumMemberAttribute enumMember
            ? enumMember.Value
            : ((int)(object)val).ToString()!;
    }
}