﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Metadata;

namespace BlueGuava.ContentManagement.Api.Infrastructure;

[AttributeUsage(AttributeTargets.Parameter, AllowMultiple = false, Inherited = true)]
public sealed class StringBodyAttribute : ModelBinderAttribute
{
    public StringBodyAttribute()
        : base(typeof(StringBodyModelBinder))
    {
        BindingSource = BindingSource.Body;
    }

    public bool Required { get; set; }

    private class StringBodyModelBinder : IModelBinder
    {
        public async Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var body = bindingContext.HttpContext.Request.Body;
            using var reader = new System.IO.StreamReader(body);
            var result = await reader.ReadToEndAsync();
            bindingContext.Result = ModelBindingResult.Success(result);

            var required = IsRequired(bindingContext.ModelMetadata);
            if (required && string.IsNullOrEmpty(result)) // required, but is empty => model error
                bindingContext.ModelState.AddModelError("", "A non-empty request body is required.");
        }

        private static bool IsRequired(ModelMetadata modelMetadata)
        {
            if (!(modelMetadata is DefaultModelMetadata metadata))
                return false;

            var attrib = metadata.Attributes.ParameterAttributes?
                .OfType<StringBodyAttribute>()
                .SingleOrDefault();

            return attrib != null && attrib.Required;
        }
    }
}