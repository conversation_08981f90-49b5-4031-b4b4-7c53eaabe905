using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Services;
using Microsoft.Extensions.Hosting;

namespace BlueGuava.ContentManagement.Api.Infrastructure;

internal class InteropObjectSequenceInitializer : IHostedService
{
    private readonly IObjectSequenceProcessor<Library.Interop.v2.Object> sequenceProcessor;

    public InteropObjectSequenceInitializer(IObjectSequenceProcessor<Library.Interop.v2.Object> sequenceProcessor)
    {
        this.sequenceProcessor = sequenceProcessor;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await sequenceProcessor.InitializeSequence();
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await sequenceProcessor.DisposeSequence();
    }
}