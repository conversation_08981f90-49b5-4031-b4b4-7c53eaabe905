﻿using System;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using <PERSON>;

namespace BlueGuava.ContentManagement.Api;

public static class HttpClientHelper
{
    public static void OnHalfOpen<T>()
    {
        using var loggerFactory = LoggerFactory.Create(loggingBuilder => loggingBuilder
            .SetMinimumLevel(LogLevel.Trace)
            .AddConsole());

        ILogger logger = loggerFactory.CreateLogger<T>();
        logger.LogInformation("Circuit in test mode, one request will be allowed.");
    }

    public static void OnReset<T>()
    {
        using var loggerFactory = LoggerFactory.Create(loggingBuilder => loggingBuilder
            .SetMinimumLevel(LogLevel.Trace)
            .AddConsole());

        ILogger logger = loggerFactory.CreateLogger<T>();
        logger.LogInformation("Circuit closed, requests flow normally.");
    }

    public static void OnBreak<T>(DelegateResult<HttpResponseMessage> arg1, TimeSpan arg2)
    {
        using var loggerFactory = LoggerFactory.Create(loggingBuilder => loggingBuilder
            .SetMinimumLevel(LogLevel.Trace)
            .AddConsole());

        ILogger logger = loggerFactory.CreateLogger<T>();
        logger.LogError(arg1?.Exception?.Message ?? "OnBreak No Exception");
    }
}