{"profiles": {"Brand-Dev": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51122;http://localhost:51123", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Brand-Stg": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51122;http://localhost:51123", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development_Stg"}}, "Nexius-Dev": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51122;http://localhost:51123", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Nexius_Dev"}}, "Imagi-Stg": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51122;http://localhost:51123", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Stage_Imagi"}}, "Nexius-Stg": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51122;http://localhost:51123", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Stage_Nexius"}}, "Nexius-QA": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:51122;http://localhost:51123", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Nexius_QA"}}}}