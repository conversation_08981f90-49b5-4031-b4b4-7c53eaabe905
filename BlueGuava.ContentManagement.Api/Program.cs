﻿using System;
using System.Threading.Tasks;
using Amazon.SecurityToken.Model;
using Amazon.SecurityToken;
using BlueGuava.Extensions.Configuration;
using BlueGuava.Extensions.Telemetry;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api
{
    /// <summary>
    /// Program entry point for the application
    /// </summary>
    public class Program
    {
        /// <summary>
        /// Asynchronous application entry point
        /// </summary>
        /// <param name="args"></param>
        public static async Task Main(string[] args)
        {
            var host = CreateHostBuilder(args).Build();

            // retrieve the logger
            var logger = host.Services.GetService<ILogger<Program>>();
            var env = host.Services.GetService<IWebHostEnvironment>();

            if (env?.EnvironmentName != Environments.Production)
            {
                var configuration = host.Services.GetRequiredService<IConfiguration>();
                var stsClient = configuration.GetAWSOptions().CreateServiceClient<IAmazonSecurityTokenService>();

                // Using async await pattern to avoid blocking.
                var identity = await stsClient.GetCallerIdentityAsync(new GetCallerIdentityRequest());

                logger?.LogInformation("AWS Account={Account} ARN={Arn} UserId={UserId} EnvironmentName={EnvName}",
                    identity.Account, identity.Arn, identity.UserId, env.EnvironmentName);
            }

            // Run the host asynchronously
            await host.RunAsync();
        }

        /// <summary>
        /// Initialize web api host
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration(cb => cb.AddSystemsManager("/bg-jm/"))
                .ConfigureAppConfiguration(cb => cb.AddSystemsManager("/ENT/"))
                .ConfigureAppConfiguration(cb => cb.AddParameterStore("BlueGuava:ContentManagement:Api"))
                .ConfigureAppConfiguration(cb => cb.AddVersionConfiguration())
                .ConfigureAppConfiguration((ctx, builder) =>
                {
                    var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                    if (environmentName != Environments.Production)
                    {
                        builder.AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: true);
                        builder.AddEnvironmentVariables();
                    }
                })
                .ConfigureSerilog()
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                    webBuilder.ConfigureKestrel(serverOptions =>
                    {
                        // High-performance Kestrel settings
                        serverOptions.Limits.MaxConcurrentConnections = 10_000;
                        serverOptions.Limits.MaxConcurrentUpgradedConnections = 10_000;
                        serverOptions.Limits.MaxRequestBodySize = 1 * 1024 * 1024; // 1 MB
                        serverOptions.Limits.MinRequestBodyDataRate = null;
                        serverOptions.Limits.MinResponseDataRate = null;
                        serverOptions.Limits.RequestHeadersTimeout = TimeSpan.FromSeconds(30);
                        serverOptions.Limits.Http2.MaxStreamsPerConnection = 100;
                        serverOptions.Limits.Http2.MaxFrameSize = 16 * 1024; // 16 KB
                        serverOptions.Limits.Http2.MaxRequestHeaderFieldSize = 16 * 1024; // 16 KB
                        serverOptions.AddServerHeader = false; // Remove server header for security reasons
                    });
                });
        }
    }
}
