﻿using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Packages.Entities.Validation;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueGuava.ContentManagement.Api.Validation;

public interface IContentValidator
{
    bool Validate(Content content, ModelStateDictionary modelState, bool forJobs = false);
}

public class ContentValidator : IContentValidator
{
    private readonly ILogger<ContentValidator> logger;
    private readonly ValidationOptions validation;
    private readonly Globalization globalization;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public ContentValidator(ILogger<ContentValidator> logger, IOptionsMonitor<ValidationOptions> validation,
        IOptionsMonitor<Globalization> globalization, ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.validation = validation.CurrentValue;
        this.globalization = globalization.CurrentValue;
        this.correlationContextAccessor = correlationContextAccessor;
    }

    public bool Validate(Content entity, ModelStateDictionary modelState, bool forJobs = false)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentValidator), nameof(Validate))
                .Log(LogLevel.Debug, "Content: {Content}, ForJobs: {ForJobs}", entity.ToJson(), forJobs);

        if (!NeedsElevatedValidation(entity)) return true;

        if (validation.CheckLocalization.IsEnabled(entity.Type, forJobs))
            ValidateLocalizations(entity.Localizations, globalization.Languages, modelState);

        if (validation.CheckImages.IsEnabled(entity.Type, forJobs))
            ValidateAssets(entity.Assets, validation.CheckImages?.ImageTypes, "--", modelState);

        if (validation.CheckStreams.IsEnabled(entity.Type, forJobs) == true)
            ValidateAssets(entity.Assets, validation.CheckStreams?.StreamTypes, null, modelState);

        if (validation.CheckDuration.IsEnabled(entity.Type, forJobs) && entity.Duration <= 0)
            modelState.AddModelError(nameof(entity.Duration), "Duration must be specified");

        return modelState.IsValid;
    }

    private void ValidateLocalizations(Dictionary<string, Packages.Entities.V2.Localization>? localizations,
        List<string>? languages, ModelStateDictionary modelState)
    {
        if (languages == null) return;
        if (localizations == null) return;

        foreach (var lang in languages)
        {
            var path = $"{nameof(Content.Localizations)}.{lang}";
            if (!localizations.TryGetValue(lang, out var entry))
            {
                modelState.AddModelError(path, $"{lang} localization must be specified");
            }
            else
            {
                if (string.IsNullOrEmpty(entry.Name))
                    modelState.AddModelError($"{path}.{nameof(entry.Name)}", "Must have value");
                if (string.IsNullOrEmpty(entry.ShortInfo) && string.IsNullOrEmpty(entry.Description))
                {
                    modelState.AddModelError($"{path}.{nameof(entry.ShortInfo)}",
                        $"Must have value if '{nameof(entry.Description)}' is not specified.");
                    modelState.AddModelError($"{path}.{nameof(entry.Description)}",
                        $"Must have value if '{nameof(entry.ShortInfo)}' is not specified.");
                }
            }
        }
    }

    private void ValidateAssets(List<Asset>? assets, List<List<SubType>>? subtypeList, string? lang,
        ModelStateDictionary modelState)
    {
        assets ??= new List<Asset>();
        subtypeList ??= new List<List<SubType>>();
        foreach (var subList in subtypeList)
        {
            if (subList.Count == 0) continue;
            if (!assets.Any(a =>
                    subList.Contains(a.SubType) && (string.IsNullOrEmpty(lang) || MatchingLocale(a.Locale, lang)) &&
                    !a.IsDeleted && !string.IsNullOrWhiteSpace(a.ObjectUrl + a.PublicUrl)))
            {
                var subtypes = subList.Count == 1
                    ? $"a {subList[0]}"
                    : $"either {string.Join(", ", subList.Take(subList.Count - 1))}, or {subList[^1]}";
                modelState.AddModelError(nameof(Content.Assets),
                    $"Must contain {subtypes} subtyped asset with ObjectUrl or PublicUrl specified.");
            }
        }
    }

    private static bool NeedsElevatedValidation(Content content)
    {
        if (!content.Published) return false;
        if (content.Type == ContentType.Advertisement) return false;
        return true;
    }

    private static bool MatchingLocale(string? assetLocale, string expected)
    {
        if (expected != "--" && assetLocale == expected) return true;
        return string.IsNullOrEmpty(assetLocale) || assetLocale == expected;
    }
}