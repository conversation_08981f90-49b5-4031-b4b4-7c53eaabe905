﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
        <LangVersion>12</LangVersion>
        <StartupObject>BlueGuava.ContentManagement.Api.Program</StartupObject>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.2.2" />
        <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.401.55" />
        <PackageReference Include="AWSSDK.SimpleNotificationService" Version="3.7.400.106" />
        <PackageReference Include="BlueGuava.Authorization.CloudFront" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Collections.Relationship.Json.Newtonsoft" Version="8.1.3" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.Authentication" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.Authorization.Roles" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.ExceptionHandling" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.VersionController" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.Authentication.Swagger" Version="8.1.3" />
        <PackageReference Include="BlueGuava.Extensions.AwsSwaggerInteraction" Version="8.1.3" />
        <PackageReference Include="BlueGuava.Extensions.Configuration.ParameterStore" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.Swagger.ApiVersioning" Version="8.1.2" />
        <PackageReference Include="BlueGuava.HttpRepository" Version="8.1.1" />
        <PackageReference Include="BlueGuava.ItemsProcessing" Version="8.1.1" />
        <PackageReference Include="BlueGuava.JwtToken" Version="8.1.2" />
        <PackageReference Include="BlueGuava.JwtTokenValidation" Version="8.1.2" />
        <PackageReference Include="BlueGuava.MessageQueuing.Worker" Version="8.1.1" />
        <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
        <PackageReference Include="prometheus-net.AspNetCore.HealthChecks" Version="8.2.1" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="7.3.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Common\BlueGuava.ContentManagement.Common.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Delivery\BlueGuava.ContentManagement.Delivery.csproj" />
        <ProjectReference Include="..\NugetPackages\BlueGuava.ContentManagement.Integration\BlueGuava.ContentManagement.Integration.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Repository.DynamoDb\BlueGuava.ContentManagement.Repository.DynamoDb.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.AmazonIVS\BlueGuava.ContentManagement.Service.AmazonIVS.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.Automation\BlueGuava.ContentManagement.Service.Automation.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.Chime\BlueGuava.ContentManagement.Service.Chime.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.MediaLive\BlueGuava.ContentManagement.Service.MediaLive.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.OpenSearch\BlueGuava.ContentManagement.Service.OpenSearch.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service\BlueGuava.ContentManagement.Service.csproj" />
        <ProjectReference Include="..\NugetPackages\BlueGuava.ContentManagement.Messages\BlueGuava.ContentManagement.Messages.csproj" />
    </ItemGroup>

</Project>