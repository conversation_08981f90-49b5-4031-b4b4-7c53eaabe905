{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Error", "System": "Error", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Hosting.Diagnostics": "Error", "AspNetCore.HealthChecks.UI": "Warning", "HealthChecks": "Warning", "Microsoft.AspNetCore.Authentication": "Information", "BlueGuava.Extensions.AWS.MessageQueuing.Amazon.MessageQueue": "Warning", "BlueGuava.Extensions.AWS.MessageQueuing.Amazon.SqsClientFactory": "Warning", "BlueGuava.MessageQueuing.Worker.MessageQueueWorker": "Warning"}}, "Enrich": ["FromLogContext", "With", "WithExceptionDetails", "WithCorrelationId"], "Using": ["Serilog.Sinks.Console"], "WriteTo": {"0": {"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}", "type": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g", "renderMessage": true}}}}, "AWS": {"Region": "eu-north-1", "Profile": "stg-testbrand"}, "FeatureManagement": {"AllowContentQDBL": false, "Allow_DynamicManifestManipulation": true}}