﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.UserLog.Client;
using BlueGuava.ContentManagement.Api.Queuing;
using BlueGuava.ContentManagement.Common.Entities.MediaLive;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Service.MediaLive;
using BlueGuava.Extensions.Logging;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.MessageQueuing;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;

namespace BlueGuava.ContentManagement.Api.Integration;

public class MediaLiveControlService : IMediaLiveControl
{
    private readonly ILogger<MediaLiveControlService> logger;
    private readonly IMediaLiveIntegration mediaLive;
    private readonly IContentService contentService;
    private readonly IMessageQueue<JobRequest> jobRequestQueue;
    private readonly IMessageQueue<MediaLiveControlTask> controlQueue;
    private readonly IUserLogMessagingService userLogMessaging;
    private readonly IMetricsCollector metricsCollector;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public MediaLiveControlService(
        ILogger<MediaLiveControlService> logger,
        IMediaLiveIntegration mediaLive,
        IContentService contentService,
        IMessageQueue<JobRequest> jobRequestQueue,
        IMessageQueue<MediaLiveControlTask> controlQueue,
        IUserLogMessagingService userLogMessaging,
        IMetricsCollector metricsCollector,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.mediaLive = mediaLive;
        this.contentService = contentService;
        this.jobRequestQueue = jobRequestQueue;
        this.controlQueue = controlQueue;
        this.userLogMessaging = userLogMessaging;
        this.metricsCollector = metricsCollector;
        this.correlationContextAccessor = correlationContextAccessor;
    }


    public async Task Execute(MediaLiveCommand command)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(MediaLiveControlService), nameof(Execute))
                .Log(LogLevel.Debug, "Command: {Command}", command.ToJson());

        await (command switch
        {
            CreateChannel cc => CreateChannel(cc),
            StartStreaming ss => StartStreaming(ss),
            StopStreaming ss => StopStreaming(ss),
            DeleteChannel dc => DeleteChannel(dc),
            null => throw new ArgumentNullException(nameof(command)),
            _ => throw new NotSupportedException($"The command {command.GetType().Name} is not supported")
        });
    }


    private async Task CreateChannel(CreateChannel command)
    {
        var channelId = await mediaLive.CreateChannel(command.ContentId, command.ObjectUrl);
        await UpdateContent(command.ContentId, c => c.ExternalId = channelId);

        var startSchedule = command.StartTime.AddMinutes(-10);
        if (startSchedule > DateTime.UtcNow)
            await jobRequestQueue.Enqueue(new MediaLiveStreamJobRequest
            {
                OwnerId = command.CustomerId,
                ReferenceObjectId = command.ContentId.ToString(),
                Scheduled = startSchedule,
                StartTime = command.StartTime,
                Action = StreamAction.Start,
                Name = $"Start Streaming {channelId}",
                NotificationEvents = new[]
                {
                    JobManagement.Common.Entities.Enums.JobStatus.SUCCEED,
                    JobManagement.Common.Entities.Enums.JobStatus.FAILED
                }
            });
        else // closer than 10 minutes; start now
            await controlQueue.EnqueueDelayed(new StartStreaming
            {
                ContentId = command.ContentId,
                StartTime = command.StartTime
            });

        // add a job to stop the channel after the content has played to completion
        await jobRequestQueue.Enqueue(new MediaLiveStreamJobRequest
        {
            OwnerId = command.CustomerId,
            ReferenceObjectId = command.ContentId.ToString(),
            Scheduled = command.StartTime.AddMinutes(5)
                .AddMilliseconds(command.Duration),
            Action = StreamAction.Stop,
            Name = $"Stop Streaming {channelId}",
            NotificationEvents = new[]
            {
                JobManagement.Common.Entities.Enums.JobStatus.SUCCEED,
                JobManagement.Common.Entities.Enums.JobStatus.FAILED
            }
        });
    }

    private async Task StartStreaming(StartStreaming command)
    {
        var url = await mediaLive.StartStreaming(command.ContentId, command.StartTime);

        if (string.IsNullOrEmpty(url))
            await controlQueue.EnqueueDelayed(command); // try again
        else
            await UpdateContent(command.ContentId, c => PublishStream(c, url));
    }

    private async Task StopStreaming(StopStreaming command)
    {
        await mediaLive.StopStreaming(command.ContentId);

        await DisconnectVod(command.ContentId);
        await UpdateContent(command.ContentId, UnpublishStream);

        await controlQueue.EnqueueDelayed(new DeleteChannel
        {
            ContentId = command.ContentId
        });
    }

    private async Task DeleteChannel(DeleteChannel command)
    {
        if (!await mediaLive.DeleteChannel(command.ContentId))
            await controlQueue.EnqueueDelayed(command);
        else
            await UpdateContent(command.ContentId, c => c.ExternalId = null);
    }


    /// <summary>
    /// Performs the <paramref name="update"/> on the content with <paramref name="contentId"/> and saves it,
    /// while also creating an audit log entry, and reporting the change through metrics
    /// </summary>
    private async Task UpdateContent(Guid contentId, Action<Content> update)
    {
        var content = await contentService.Retrieve(contentId);
        if (content == null) return; // no content to update
        var original = content.RecursiveCopy();

        update(content); // mutate the content object
        var updated = await contentService.Save(content, Guid.Empty, false);

        _ = userLogMessaging.EnqueueLogEntry(
            ClaimsPrincipal.Current ?? new ClaimsPrincipal(new ClaimsIdentity()),
            UserLog.Enums.Component.Content,
            UserLog.Enums.Action.Update,
            contentId.ToString(), original, updated);

        metricsCollector.IncrementSuccessCounter(ApiMethod.Update, content.Type);
    }

    /// <summary>
    /// Updates the associated vod content so that it no longer references the live
    /// </summary>
    private async Task DisconnectVod(Guid liveId)
    {
        var live = await contentService.Retrieve(liveId);
        if (live == null) return; // no content to update

        var vodId = live.Properties?.GetValueOrDefault(Consts.CONTENT_BROADCAST_VODID);
        await UpdateContent(Guid.TryParse(vodId, out var id) ? id : Guid.Empty, vod =>
        {
            vod.Properties?.Remove(Consts.CONTENT_BROADCAST_LIVEID);
            vod.Properties?.Remove(Consts.CONTENT_BROADCAST_STARTTIME);
        });
    }

    /// <summary>
    /// Marks the <paramref name="content"/> published,
    /// and adds <paramref name="streamUrl"/> as asset
    /// </summary>
    /// <remarks>
    /// the <see cref="Asset.Id"/> is always the <see cref="Content.Id"/>
    /// </remarks>
    private static void PublishStream(Content content, string streamUrl)
    {
        content.Published = true;
        content.PublishedDate ??= DateTime.UtcNow;
        var asset = content.Assets?.SingleOrDefault(a => a.Id == content.Id); // should always be null
        if (asset == null)
        {
            asset = new Asset { CreatedDate = DateTime.UtcNow };
            content.Assets?.Add(asset);
        }
        asset!.Id = content.Id;
        asset.Type = AssetType.Video;
        asset.SubType = SubType.HLS;
        asset.FileSize = 1;
        asset.IsPublic = true;
        asset.PublicUrl = asset.ObjectUrl = streamUrl;
        asset.FileName = Path.GetFileName(streamUrl);
        asset.Locale = content.OriginalLanguage;
        asset.ModifiedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the <paramref name="content"/> not published,
    /// and removes the stream url asset
    /// </summary>
    private static void UnpublishStream(Content content)
    {
        content.Published = false;
        content.PublishedDate = null;
        content.Assets?.RemoveAll(a => a.Id == content.Id);
        content.Properties?.Remove(Consts.CONTENT_BROADCAST_VODID);
        content.Properties?.Remove(Consts.CONTENT_BROADCAST_STARTTIME);
    }
}