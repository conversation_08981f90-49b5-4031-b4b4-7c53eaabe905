using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using BlueGuava.UserLog.Client;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Api.Infrastructure;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AspNetCore.IpAddress;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;

using MaxMind.GeoIP2;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Microsoft.Extensions.Logging;
using LocalizationModel = BlueGuava.ContentManagement.Api.Models.ContentV2.LocalizationModel;
using LogAction = BlueGuava.UserLog.Enums.Action;
using LogComponent = BlueGuava.UserLog.Enums.Component;

namespace BlueGuava.ContentManagement.Api.Integration;

/// <summary>
/// Content ingest service
/// </summary>
public class ContentIngestService : IContentIngestService
{
    private List<string> nonSupportedAudioVideoTypes = new List<string> { ".mov", ".mkv", ".avi", ".mp3", ".m4a", ".flac", ".ogg" };

    private readonly List<string> nonSupportedImageTypes = new List<string> { ".webp", ".bmp" /*, ".gif"*/, ".ico", ".jpeg", ".jpg", ".png", ".wbmp", ".ktx", ".dng", ".heif", ".heic", ".avif" };

    private readonly string defLang;

    private readonly IAutomationManagementService automationManagementService;
    private readonly IUserLogMessagingService userLogMessagingService;
    private readonly IMessageQueue<UseCaseRequest> useCaseRequestMessagingService;
    private readonly IContentService contentService;
    private readonly IMessagingService messagingService;
    private readonly IMapper mapper;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdate;
    private readonly IOpenStreetMapService openStreetMapService;
    private readonly IHttpContextAccessor httpContextAccessor;
    private readonly IGeoIP2Provider geoIpProvider;
    private readonly IMessageQueue<JobRequest> jobManagerQueue;
    private readonly IFeatureManager featureManager;
    private readonly ILogger<ContentIngestService> logger;

    /// <summary>
    ///
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="automationManagementService"></param>
    /// <param name="userLogMessagingService"></param>
    /// <param name="useCaseRequestMessagingService"></param>
    /// <param name="globalization"></param>
    /// <param name="mapper"></param>
    /// <param name="contentService"></param>
    /// <param name="messagingService"></param>
    /// <param name="relationshipUpdate"></param>
    /// <param name="openStreetMapService"></param>
    /// <param name="httpContextAccessor"></param>
    /// <param name="geoIpProvider"></param>
    /// <param name="jobManagerQueue"></param>
    /// <param name="featureManager"></param>
    public ContentIngestService(
        IAutomationManagementService automationManagementService,
        IUserLogMessagingService userLogMessagingService,
        IMessageQueue<UseCaseRequest> useCaseRequestMessagingService,
        IOptionsMonitor<Globalization> globalization,
        IMapper mapper,
        IContentService contentService,
        IMessagingService messagingService,
        IMessageQueue<RelationshipUpdate> relationshipUpdate,
        IOpenStreetMapService openStreetMapService,
        IHttpContextAccessor httpContextAccessor,
        IGeoIP2Provider geoIpProvider,
        IMessageQueue<JobRequest> jobManagerQueue,
        IFeatureManager featureManager,
        ILogger<ContentIngestService> logger)
    {
        this.automationManagementService = automationManagementService;
        this.userLogMessagingService = userLogMessagingService;
        this.useCaseRequestMessagingService = useCaseRequestMessagingService;
        this.mapper = mapper;
        this.contentService = contentService;
        this.messagingService = messagingService;
        this.relationshipUpdate = relationshipUpdate;
        this.openStreetMapService = openStreetMapService;
        this.httpContextAccessor = httpContextAccessor;
        this.geoIpProvider = geoIpProvider;
        this.jobManagerQueue = jobManagerQueue;
        this.featureManager = featureManager;
        this.logger = logger;

        defLang = globalization?.CurrentValue?.Languages?.Any() ?? false
            ? globalization.CurrentValue.Languages[0]
            : string.Empty;
    }


    /// <summary>
    /// Ingests a content
    /// </summary>
    /// <param name="ingest"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<Content?> Ingest(IngestContentRequestV2 ingest, ClaimsPrincipal user)
    {
        ingest.AddAsset();
        ingest.OriginalTitle = CleanupName(string.IsNullOrEmpty(ingest.OriginalTitle)
            ? ingest.OriginalFileName
            : ingest.OriginalTitle);

        if (await featureManager.IsEnabledAsync("AllowExtractFileName"))
            ExtractLabels(ingest);

        ingest.Localizations ??= new Dictionary<string, ContentManagement.Integration.V2.LocalizationModel>();
        ingest.Localizations[defLang] = new LocalizationModel { Name = ingest.OriginalTitle.CleanGenre() };

        //var url = new Uri(string.IsNullOrEmpty(ingest.ObjectUrl) ? "http://" : ingest.ObjectUrl).LocalPath;

        if (ingest.WorkflowId != null && ingest.WorkflowId != Guid.Empty)
        {
            ingest.Properties ??= new Dictionary<string, string>();
            ingest.Properties.Add(Consts.CONTENT_WORKFLOWID, ingest.WorkflowId.ToString());
        }

        var entity = mapper.Map<Content>(ingest);
        entity.OriginalFileName = ingest.OriginalFileName?.ToLower();

        Content? result = null;

        await SetLocation(entity, ingest.Latitude, ingest.Longitude);

        var current = await contentService.Retrieve(entity.Id);
        if (current == null)
        {
            result = await contentService.Create(entity, user, true);
            if (result == null)
                throw new Exception("Content creation failed");
        }
        else
        {
            //check if we already received the success message from the ingest
            if (entity.Type != ContentType.Remix
                && current.Assets?
                    .FirstOrDefault(x => x.SubType == SubType.Original)?.WorkflowStatus == WorkflowStatus.Succeeded)
                return current;

            result = await contentService.Update(entity, user);
            if (result == null)
                throw new Exception("Content update failed");
        }

        //if the ingest is not finished do not execute other processes
        if (entity.Type != ContentType.Remix
            && entity.Assets?
                .FirstOrDefault(x => x.SubType == SubType.Original)?.WorkflowStatus != WorkflowStatus.Succeeded)
            return result;

        _ = PostProcess(result, ingest.ObjectUrl, user);

        if (Guid.TryParse(entity.ReferenceId, out var referenceId))
            _ = AddToPlaylistContentList(referenceId, entity.Id);

        return result;
    }

    public async Task PostProcess(Content result, string url, ClaimsPrincipal user)
    {
        if (result == null) return;

        var workflowId = Guid.Empty;

        if (result.Properties?.ContainsKey(Consts.CONTENT_WORKFLOWID) == true)
        {
            Guid.TryParse(result.Properties[Consts.CONTENT_WORKFLOWID], out workflowId);
        }
        var originalFileName = result.OriginalFileName;

        var allowMOVDirectTranscoding = await featureManager.IsEnabledAsync("Allow_MOV_Direct_Transcoding");
        if (allowMOVDirectTranscoding)
        {
            if (nonSupportedAudioVideoTypes.Contains(".mov"))
            {
                nonSupportedAudioVideoTypes.Remove(".mov");
            }
        }

        //logger.LogInformation("PostProcess: contentId {ContentId} , url: {Url}, workflowId: {WorkflowId}, originalFileName: {OriginalFileName}", result.Id, url, workflowId, originalFileName);

        if (nonSupportedImageTypes.Contains(Path.GetExtension(url).ToLower()))
        {
            logger.LogInformation("PostProcess: NonSupported Image Type");
            _ = jobManagerQueue.Enqueue(new ImageProcessorJobRequest(user.GetCustomerId(), user.GetEmail())
            {
                ReferenceObjectId = result.Id.ToString(),
                ReferenceObjectName = result.OriginalTitle,
                InputFileLocation = url,
                ProcessingMode = ImageProcessingMode.DefaultImageConvert
            }, 10);
        }
        else if (nonSupportedAudioVideoTypes.Contains(Path.GetExtension(url).ToLower()))
        {
            logger.LogInformation("PostProcess: NonSupported Other Type");

            _ = jobManagerQueue.Enqueue(new MediaProcessorJobRequest(user.GetCustomerId(), user.GetEmail())
            {
                ReferenceObjectId = result.Id.ToString(),
                ReferenceObjectName = result.OriginalTitle,
                InputFileLocation = url,
                MediaProcessingType = MediaProcessingType.DEFAULT_MEDIA_CONVERT
            }, 10);
        }
        else
        {
            logger.LogInformation("PostProcess: Else");

            if (workflowId != Guid.Empty)
            {
                logger.LogInformation("PostProcess: Workflow Requested");
                var useCaseRequest = new UseCaseRequest()
                {
                    WorkflowTemplateId = workflowId.ToString(),
                    ReferenceObjectId = result.Id.ToString(),
                    InputFileLocation = url,
                    Id = Guid.NewGuid().ToString(),
                    OwnerId = user.GetCustomerId(),
                    OwnerEmail = user.GetEmail()
                };
                _ = useCaseRequestMessagingService.Enqueue(useCaseRequest);
            }
        }
        logger.LogInformation("PostProcess: Raised Content Updated Event");
        _ = messagingService.ContentUploaded(result.Id, result.OriginalTitle, originalFileName,
            user.GetCustomerId());

        _ = EnqueueLogEntry(LogAction.Create, null, result, user, "Content ingest");
        logger.LogInformation("PostProcess: Exec Automation");
        //execute automation
        _ = automationManagementService.OnContentCreated(result, user);

    }

    private void ExtractLabels(IngestContentRequestV2 ingest)
    {
        var splitValues = ingest.OriginalTitle.Split("-");
        if (splitValues.Length > 1)
        {
            var labels = splitValues[..^1];
            if (ingest.Labels == null)
            {
                ingest.Labels = new Dictionary<LabelType, List<string>> { { LabelType.Category, labels.ToList() } };
            }
            else
            {
                if (ingest.Labels.TryGetValue(LabelType.Category, out var values))
                {
                    values.AddRange(labels);
                    ingest.Labels.Remove(LabelType.Category);
                    ingest.Labels.Add(LabelType.Category, values);
                }
                else
                {
                    ingest.Labels.Add(LabelType.Category, labels.ToList());
                }
            }
        }

        ingest.OriginalTitle = splitValues.Last();
    }

    private async Task EnqueueLogEntry(LogAction action, Content? currentContent, Content? updatedContent,
        ClaimsPrincipal user, string? notes = null)
    {
        var objectId = $"{currentContent?.Id ?? updatedContent?.Id}";
        var metadata = new Dictionary<string, string>();
        if (!string.IsNullOrEmpty(notes)) metadata["Notes"] = notes;
        var empty = new Content { Id = default, CreatedDate = default, LastModifiedDate = default };
        await userLogMessagingService.EnqueueLogEntry(user, LogComponent.Content, action,
            objectId, currentContent ?? empty, updatedContent ?? empty, metadata);
    }

    private UseCaseRequest ToUseCaseRequest(IngestContentRequestV2 request, ClaimsPrincipal user)
    {
        return new UseCaseRequest()
        {
            WorkflowTemplateId = request.WorkflowId.ToString(),
            ReferenceObjectId = request.ContentId.ToString(),
            InputFileLocation = request.ObjectUrl,
            Id = Guid.NewGuid().ToString(),
            OwnerId = user.GetCustomerId(),
            OwnerEmail = user.GetEmail()
        };
    }

    private static string CleanupName(string fileName)
    {
        if (string.IsNullOrEmpty(fileName)) return fileName;
        //var name = Path.GetFileNameWithoutExtension(fileName);
        var name = Regex.Replace(fileName.Replace("_", " "), "\\s+", " ");
        var cultureInfo = System.Globalization.CultureInfo.InvariantCulture;
        return cultureInfo.TextInfo.ToTitleCase(name);
    }

    /// <summary>
    /// Adds the content to the playlist of the reference content
    /// </summary>
    /// <param name="referenceContentId"></param>
    /// <param name="contentId"></param>
    private async Task AddToPlaylistContentList(Guid referenceContentId, Guid contentId)
    {
        await relationshipUpdate.Enqueue(new SingleRelationshipUpdate
        {
            Action = ActionKind.Add,
            Relation = ContentRelation.Playlist,
            SourceId = referenceContentId.ToString(),
            TargetId = contentId.ToString()
        });
    }

    private async Task SetLocation(Content content, double? latitude, double? longitude)
    {
        try
        {
            var country = "ZZ";
            var city = "Unknown";

            if (latitude == null || longitude == null)
            {
                var httpContext = httpContextAccessor.HttpContext;
                var ipAddress = httpContext?.Request?.GetClientIpAddress() ?? string.Empty;

                if (string.IsNullOrEmpty(ipAddress)) return;
                var response = geoIpProvider.City(ipAddress);
                country = response.Country.IsoCode ?? "ZZ";
                city = response.City.Name ?? "UNKNOWN";
            }
            else
            {
                var response = await openStreetMapService.GetLocation(latitude.Value, longitude.Value);
                country = response.Address.Country ?? "ZZ";
                city = response.Address.City ?? "UNKNOWN";
            }


            content.Labels ??= new Dictionary<LabelType, List<string>>();

            if (!content.Labels.ContainsKey(LabelType.Location))
                content.Labels.Add(LabelType.Location, new List<string>());

            content.Labels[LabelType.Location].Add(country);
            content.Labels[LabelType.Location].Add(city);
        }
        catch
        {
        }
    }
}