﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.UserLog.Client;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.Chime;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Service.Chime.Exceptions;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using BlueGuava.Tracewind.Common.Models;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;
using LogAction = BlueGuava.UserLog.Enums.Action;
using LogComponent = BlueGuava.UserLog.Enums.Component;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Api.Integration;

public class ContentIntegrationService : IContentIntegration
{
    private readonly string defLang;
    private readonly ILogger<ContentIntegrationService> logger;
    private readonly IContentService contentService;
    private readonly IChimeIntegrationService chimeIntegration;
    private readonly IAmazonIvsService ivsService;
    private readonly IUserLogMessagingService userLogMessagingService;
    private readonly IMessageQueue<TraceLogMessage> tracewindService;
    private readonly IMessageQueue<UpdateMessage> contentUpdateQueue;
    private readonly IMetricsCollector metricsCollector;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IFeatureManager featureManager;
    private readonly ModuleInfo moduleInfo;

    public ContentIntegrationService(
        ILogger<ContentIntegrationService> logger,
        IOptionsMonitor<Globalization> globalization,
        IContentService contentService,
        IChimeIntegrationService chimeIntegration,
        IAmazonIvsService ivsService,
        IUserLogMessagingService userLogMessagingService,
        IMessageQueue<TraceLogMessage> tracewindService,
        IMetricsCollector metricsCollector,
        ICorrelationContextAccessor correlationContextAccessor,
        IFeatureManager featureManager,
        IOptions<ModuleInfo> moduleInfo,
        IMessageQueue<UpdateMessage> contentUpdateQueue)
    {
        this.logger = logger;
        this.contentService = contentService;
        this.chimeIntegration = chimeIntegration;
        this.ivsService = ivsService;
        this.userLogMessagingService = userLogMessagingService;
        this.tracewindService = tracewindService;
        this.metricsCollector = metricsCollector;
        this.correlationContextAccessor = correlationContextAccessor;
        this.featureManager = featureManager;
        this.contentUpdateQueue = contentUpdateQueue;
        this.moduleInfo = moduleInfo.Value;
        defLang = globalization?.CurrentValue?.Languages?.Any() ?? false
            ? globalization.CurrentValue.Languages[0]
            : string.Empty;
    }

    public async Task<Content?> CreateChimeContent(Content entity, ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentIntegrationService), nameof(CreateChimeContent))
                .Log(LogLevel.Debug, "Content: {Content}", entity.ToJson());

        if (await featureManager.IsEnabledAsync("AllowContentAutoPublish")) entity.Published = true;

        await SetupContent(entity);

        var meeting = await chimeIntegration.CreateMeeting(entity.Id);
        entity.Properties ??= new Dictionary<string, string>();
        entity.Properties[Consts.CONTENT_SESSIONID] = meeting.MeetingId ?? string.Empty;
        entity.ProcessingStatus = ProcessingStatus.Created;

        var content = await contentService.Create(entity, user);

        _ = EnqueueLogEntry(LogAction.Create, null, content, user, "Create camera capture");
        _ = TracelogAction(meeting, "Success: Create camera capture");
        try
        {
            metricsCollector.IncrementSuccessCounter(ApiMethod.Create, content?.Type ?? ContentType.None);
        }
        catch { }

        return content;
    }

    public async Task<Content?> CreateIVSLiveContent(Content entity,
        ClaimsPrincipal user,
        IVSChannelLatencyMode channelLatencyMode,
        IVSChannelType channelType)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentIntegrationService),
                    nameof(CreateIVSLiveContent))
                .Log(LogLevel.Debug, "Content: {Content}", entity.ToJson());

        if (await featureManager.IsEnabledAsync("AllowContentAutoPublish")) entity.Published = true;

        await SetupContent(entity);

        var channelInfo = await ivsService.CreateLiveStream(entity.Id, true, channelLatencyMode, channelType);
        entity.Properties ??= new Dictionary<string, string>();
        entity.Properties[Consts.CONTENT_CHANNEL_ARN] = channelInfo.ChannelArn ?? string.Empty;
        entity.Properties[Consts.CONTENT_STREAMING_URL] = channelInfo.IngestEndpoint ?? string.Empty;
        entity.Properties[Consts.CONTENT_STREAMING_ARN] = channelInfo.StreamKeyArn ?? string.Empty;
        entity.Properties[Consts.CONTENT_STREAMING_TOKEN] = channelInfo.StreamKey ?? string.Empty;
        entity.Properties.Add(Consts.CONTENT_IVS_CHANNELLATENCYMODE, channelLatencyMode.ToString());
        entity.Properties.Add(Consts.CONTENT_IVS_CHANNELTYPE, channelType.ToString());

        entity.Assets ??= new List<Asset>();
        entity.Assets.Add(new Asset
        {
            Id = entity.Id,
            Type = AssetType.Video,
            SubType = SubType.Original,
            ObjectUrl = channelInfo.IngestEndpoint,
            IsPublic = false,
            Duration = (int)TimeSpan.FromHours(1).TotalMilliseconds,
            WorkflowStatus = WorkflowStatus.Succeeded,
            CreatedDate = DateTime.UtcNow
        });
        entity.Assets.Add(new Asset
        {
            Id = Guid.NewGuid(),
            Type = AssetType.Video,
            SubType = SubType.HLS,
            ObjectUrl = channelInfo.PlaybackUrl,
            PublicUrl = channelInfo.PlaybackUrl,
            IsPublic = true,
            Duration = (int)TimeSpan.FromHours(1).TotalMilliseconds,
            WorkflowStatus = WorkflowStatus.Succeeded,
            CreatedDate = DateTime.UtcNow
        });

        var content = await contentService.Create(entity, user);
        _ = EnqueueLogEntry(LogAction.Create, null, content, user, "Create live stream");
        _ = TracelogAction(content?.Id ?? Guid.Empty, null, "Success: Create live stream");
        try
        {
            metricsCollector.IncrementSuccessCounter(ApiMethod.Create, content?.Type ?? ContentType.None);
        }
        catch { }
        return content;
    }

    public async Task<Content?> CreateIVSChatContent(Content entity, ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentIntegrationService),
                    nameof(CreateIVSChatContent))
                .Log(LogLevel.Debug, "Content: {Content}", entity.ToJson());

        if (await featureManager.IsEnabledAsync("AllowContentAutoPublish")) entity.Published = true;

        await SetupContent(entity);

        var channelInfo = await ivsService.CreateChatRoom(entity.Id);
        entity.Properties ??= new Dictionary<string, string>();
        entity.Properties[Consts.CONTENT_CHATROOM_ARN] = channelInfo.RoomArn ?? string.Empty;

        var content = await contentService.Create(entity, user);
        _ = EnqueueLogEntry(LogAction.Create, null, content, user, "Create live chat");
        _ = TracelogAction(content?.Id ?? Guid.Empty, null, "Success: Create live chat");
        try
        {
            metricsCollector.IncrementSuccessCounter(ApiMethod.Create, content?.Type ?? ContentType.None);
        }
        catch { }

        return content;
    }


    private async Task SetupContent(Content content)
    {
        content.Id = Guid.NewGuid();
        while (await contentService.Retrieve(content.Id) != null)
            content.Id = Guid.NewGuid();

        if (string.IsNullOrEmpty(content.OriginalLanguage))
            content.OriginalLanguage = defLang;

        if (content.Published && content.PublishedDate == null)
            content.PublishedDate = DateTime.UtcNow;

        content.Localizations ??= new Dictionary<string, Packages.Entities.V2.Localization>();
        if (content.Localizations.Count == 0)
            content.Localizations.Add(content.OriginalLanguage, new Packages.Entities.V2.Localization
            {
                Name = content.OriginalTitle.CleanGenre(),
                ShortInfo = content.OriginalTitle.CleanGenre()
            });
    }


    public async Task EnqueueLogEntry(LogAction action, Content? currentContent, Content? updatedContent,
        ClaimsPrincipal user, string? notes = null)
    {
        var objectId = $"{currentContent?.Id ?? updatedContent?.Id}";
        var metadata = new Dictionary<string, string>();
        if (!string.IsNullOrEmpty(notes)) metadata["Notes"] = notes;
        var empty = new Content { Id = Guid.Empty, CreatedDate = default, LastModifiedDate = default };
        await userLogMessagingService.EnqueueLogEntry(user, LogComponent.Content, action,
            objectId, currentContent ?? empty, updatedContent ?? empty, metadata);
    }

    private async Task TracelogAction(MeetingRef meeting, string description)
    {
        var meetingId = Guid.TryParse(meeting.MeetingId, out var id) ? id : (Guid?)null;
        await TracelogAction(meeting.ContentId, meetingId, description);
    }

    public async Task TracelogAction(Guid contentId, Guid? correlationId, string description)
    {
        await tracewindService.Enqueue(new TraceLogMessage
        {
            ObjectId = contentId.ToString(),
            ObjectType = (int)ObjectType.Content,
            CorrelationId = correlationId.ToString() ?? contentId.ToString(),
            Description = description,
            CreatedDate = DateTime.UtcNow,
            IpAddress = Environment.MachineName,
            Source = moduleInfo.Name,
            SourceVersion = moduleInfo.Version
        });
    }

    /// <summary>
    /// Stop a live stream and update the content
    /// </summary>
    /// <param name="content"></param>
    /// <param name="liveContentId"></param>
    /// <param name="user"></param>
    /// <exception cref="ResourceNotFoundException"></exception>
    public async Task StopStream(Content content, Guid liveContentId, ClaimsPrincipal? user = null)
    {
        content.Properties ??= new Dictionary<string, string>();
        var streamId = content.Properties.GetValueOrDefault(Consts.CONTENT_STREAMID);
        if (!string.IsNullOrEmpty(streamId)) await StopStreaming(content, streamId);

        var liveContent = await contentService.Retrieve(liveContentId);
        if (liveContent == null)
            throw new ResourceNotFoundException("Content", liveContentId.ToString()); // content no longer exists

        var oldLiveContent = liveContent.RecursiveCopy();

        liveContent.Properties ??= new Dictionary<string, string>();
        liveContent.Properties[Consts.CONTENT_BROADCAST_STATUS] = "Stopped";

        liveContent = await contentService.Save(liveContent, user?.GetCustomerId() ?? Guid.Empty, true);
        _ = EnqueueLogEntry(LogAction.Update, oldLiveContent, liveContent, user, "Stream Starting");
    }

    private async Task StopStreaming(Content content, string streamId)
    {
        var sinkInfo = await chimeIntegration.StopStreaming(streamId);
        content.Properties ??= new Dictionary<string, string>();
        content.Properties.Remove(Consts.CONTENT_STREAMID);

        var meetingId = Guid.TryParse(sinkInfo.MeetingId, out var id) ? id : (Guid?)null;
        _ = TracelogAction(sinkInfo.ContentId, meetingId, "Success: Stream Stopped");

        //update content via SQS
        await contentUpdateQueue.Enqueue(new UpdatePropertiesMessage()
        {
            Id = content.Id.ToString(),
            ContentId = content.Id.ToString(),
            UpdateStrategy = ListUpdateStrategy.Remove,
            Properties = new Dictionary<string, string>()
            {
                { Consts.CONTENT_STREAMID, string.Empty }
            }
        });
    }
}