﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Api.Infrastructure;
using BlueGuava.ContentManagement.Common.Entities.Chime;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.Exceptions;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using BlueGuava.Rewards.Messaging;
using CorrelationId;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;
using ContentResponse = BlueGuava.ContentManagement.Api.Models.ContentV2.ContentResponse;
using LogAction = BlueGuava.UserLog.Enums.Action;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

[ApiController]
[ApiVersion("4.0")]
[Route("api/v{version:apiVersion}")]
[Authorize]
public class LiveStreamController : ControllerBase
{
    private readonly ILogger<LiveStreamController> logger;
    private readonly IMessageQueue<RewardMessage> rewardQueue;
    private readonly IContentService contentService;
    private readonly IAmazonIvsService ivsService;
    private readonly IContentIntegration integration;
    private readonly IMetricsCollector metricsCollector;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdate;
    private readonly IAutomationManagementService automationManagementService;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IImageProcessorService imageProcessorService;

    private readonly IFeatureManager featureManager;

    //private readonly IChimeIntegrationService chimeIntegration;
    private readonly IMapper mapper;

    public LiveStreamController(
        ILogger<LiveStreamController> logger,
        IContentService contentService,
        IAmazonIvsService ivsService,
        IContentIntegration integration,
        IMetricsCollector metricsCollector,
        IMessageQueue<RelationshipUpdate> relationshipUpdate,
        IAutomationManagementService automationManagementService,
        ICorrelationContextAccessor correlationContextAccessor,
        IImageProcessorService imageProcessorService,
        IFeatureManager featureManager,
        //IChimeIntegrationService chimeIntegration,
        IMessageQueue<RewardMessage> rewardQueue,
        IMapper mapper)
    {
        this.logger = logger;
        this.contentService = contentService;
        this.ivsService = ivsService;
        this.integration = integration;
        this.metricsCollector = metricsCollector;
        this.relationshipUpdate = relationshipUpdate;
        this.correlationContextAccessor = correlationContextAccessor;
        this.automationManagementService = automationManagementService;
        this.imageProcessorService = imageProcessorService;
        this.featureManager = featureManager;
        //this.chimeIntegration = chimeIntegration;
        this.rewardQueue = rewardQueue;
        this.mapper = mapper;
    }

    [HttpPost("Live/Start/{relatedId?}")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> StartLive(Guid? relatedId, CreateLiveRequestV2 request)
    {
        //logger.LogInformation("Request: {@Request}", request);

        try
        {
            request.Type = ContentType.LiveStream;
            request.ReferenceId = relatedId?.ToString();

            if (request.WorkflowId.HasValue && request.WorkflowId != Guid.Empty)
            {
                request.Properties ??= new Dictionary<string, string>();
                request.Properties[Consts.CONTENT_WORKFLOWID] = request.WorkflowId.ToString();
            }

            var entity = mapper.Map<Content>(request);

            var content = await integration.CreateIVSLiveContent(entity, User,
                request?.ChannelLatencyMode ?? IVSChannelLatencyMode.NORMAL,
                request?.ChannelType ?? IVSChannelType.STANDARD);
            if (relatedId.HasValue) await AddToRelatedContent(relatedId.Value, content.Id);
            _ = automationManagementService.OnIVSLiveCreated(content, User);
            if (await AllowAutoImageGenerationFeature())
            {
                var name = entity.Localizations?.FirstOrDefault().Value?.Name;

                if (!string.IsNullOrEmpty(name))
                    await imageProcessorService.GenerateImage(name, content.Id.ToString());
            }

            _ = automationManagementService.OnContentCreated(content, User);

            await rewardQueue.Enqueue(new RewardMessage
            {
                CustomerId = User.GetCustomerId(),
                RewardKey = "CONTENT:LIVE:CREATE"
            });

            return CreatedAtAction(nameof(ContentController.Get), nameof(ContentController)[..^10],
                new { contentId = content.Id }, ContentResponse.FromEntity(content));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Request: {@Request}", request);

            throw;
        }
    }

    private async Task TraceChimeAction(MeetingRef meeting, string description)
    {
        var meetingId = Guid.TryParse(meeting.MeetingId, out var id) ? id : (Guid?)null;
        await integration.TracelogAction(meeting.ContentId, meetingId, description);
    }

    /// <summary> Adds the <paramref name="relatedId"/> as a related content to the entity by <paramref name="contentId"/> </summary>
    private async Task AddToRelatedContent(Guid contentId, Guid relatedId)
    {
        var content = await contentService.Retrieve(contentId);
        if (content == null) return;

        await relationshipUpdate.Enqueue(new SingleRelationshipUpdate()
        {
            Action = ActionKind.Add,
            Relation = ContentRelation.Reference,
            SourceId = contentId.ToString(),
            TargetId = relatedId.ToString()
        });
    }

    private async Task<bool> AllowAutoImageGenerationFeature()
    {
        if (await featureManager.IsEnabledAsync("AllowAutomation_AutoImageGeneration")) return true;
        //logger.LogInformation("Auto Image generation feature is off");
        return false;
    }

    [HttpPost("Live/Shutdown/{contentId}")]
    [ProducesResponseType(201)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> ShutdownLive(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var original = entity.RecursiveCopy();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHANNEL_ARN, out var channelArn);
            if (string.IsNullOrEmpty(channelArn)) return Conflict("No Live Channel");

            var stopped = await ivsService.StopLiveStream(channelArn);
            if (stopped) await ivsService.RemoveLiveStream(channelArn);
            entity.Properties.Remove(Consts.CONTENT_CHANNEL_ARN);
            entity.Properties.Remove(Consts.CONTENT_STREAMING_URL);
            entity.Properties.Remove(Consts.CONTENT_STREAMING_ARN);
            entity.Properties.Remove(Consts.CONTENT_STREAMING_TOKEN);

            var content = await contentService.Update(entity, User);
            _ = integration.EnqueueLogEntry(LogAction.Update, original, content, User, "Delete live channel");
            _ = integration.TracelogAction(contentId, null, "Success: Delete live channel");
            try
            {
                metricsCollector.IncrementSuccessCounter(ApiMethod.Update, entity.Type);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(LiveStreamController), nameof(ShutdownLive))
                    .Log(LogLevel.Error, ex, "IncrementSuccessCounter failed - ContentId: {ContentId}", contentId);
            }

            return Accepted();
        }
        catch (Exception ex)
        {
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPost("Live/Stop/{contentId}")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> StopLive(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var original = entity.RecursiveCopy();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHANNEL_ARN, out var channelArn);
            if (string.IsNullOrEmpty(channelArn)) return Conflict("No Live Channel");

            if (!entity.Properties.ContainsKey(Consts.CONTENT_STREAMING_ARN))
                return Conflict("Not streaming");

            var stopped = await ivsService.StopLiveStream(channelArn);
            if (stopped) await ivsService.RemoveStreamKeys(channelArn);
            entity.Properties.Remove(Consts.CONTENT_STREAMING_ARN);
            entity.Properties.Remove(Consts.CONTENT_STREAMING_TOKEN);

            var content = await contentService.Update(entity, User);
            _ = integration.EnqueueLogEntry(LogAction.Update, original, content, User, "Stop live stream");
            _ = integration.TracelogAction(contentId, null, "Success: Stop live stream");
            try
            {
                metricsCollector.IncrementSuccessCounter(ApiMethod.Update, entity.Type);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(LiveStreamController), nameof(StopLive))
                    .Log(LogLevel.Error, ex, "IncrementSuccessCounter failed - ContentId: {ContentId}", contentId);
            }

            return Accepted();
        }
        catch (Exception ex)
        {
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPost("Live/Restart/{contentId}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> RestartLive(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var original = entity.RecursiveCopy();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHANNEL_ARN, out var channelArn);
            if (string.IsNullOrEmpty(channelArn)) return Conflict("No Live Channel");

            if (entity.Properties.ContainsKey(Consts.CONTENT_STREAMING_ARN))
                return Conflict("Already can stream");

            var streamInfo = await ivsService.CreateStreamKey(channelArn);
            entity.Properties[Consts.CONTENT_STREAMING_ARN] = streamInfo.StreamKeyArn;
            entity.Properties[Consts.CONTENT_STREAMING_TOKEN] = streamInfo.StreamKey;

            var content = await contentService.Update(entity, User);
            _ = integration.EnqueueLogEntry(LogAction.Update, original, content, User, "Restart live stream");
            _ = integration.TracelogAction(contentId, null, "Success: Restart live stream");
            try
            {
                metricsCollector.IncrementSuccessCounter(ApiMethod.Update, entity.Type);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(LiveStreamController), nameof(RestartLive))
                    .Log(LogLevel.Error, ex, "IncrementSuccessCounter failed - ContentId: {ContentId}", contentId);
            }

            return Ok(ContentResponse.FromEntity(content));
        }
        catch (Exception ex)
        {
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPut("Live/Metadata/{contentId}")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> AddMetadata(Guid contentId, [StringBody] string metadata)
    {
        //logger.LogInformation("ContentId: {ContentId}, Metadata: {Metadata}", contentId, metadata);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            entity.Properties ??= new Dictionary<string, string>();
            var channelArn = entity.Properties.GetValueOrDefault(Consts.CONTENT_CHANNEL_ARN);
            if (string.IsNullOrEmpty(channelArn)) return Conflict("No Live Channel");

            var keyArn = entity.Properties.GetValueOrDefault(Consts.CONTENT_STREAMING_ARN);
            if (string.IsNullOrEmpty(keyArn)) return Conflict("The stream cannot broadcast");

            var updated = await ivsService.PutMetadata(channelArn, metadata);
            if (!updated) return Conflict("Could not send metadata");

            _ = integration.TracelogAction(contentId, null, "Success: Metadata sent");
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}, Metadata: {Metadata}", contentId, metadata);
            throw;
        }
    }
}