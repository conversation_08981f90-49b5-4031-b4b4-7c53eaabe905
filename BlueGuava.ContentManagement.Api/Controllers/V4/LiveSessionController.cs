﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using AutoMapper;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common.Entities.Chime;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Service.Chime.Exceptions;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using BlueGuava.NotificationService.Client;
using BlueGuava.Reporting.Messages.Entities;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;
using ContentResponse = BlueGuava.ContentManagement.Api.Models.ContentV2.ContentResponse;
using LogAction = BlueGuava.UserLog.Enums.Action;
using EventName = BlueGuava.MessageCenter.Common.EventName;


namespace BlueGuava.ContentManagement.Api.Controllers.V4;

[ApiController]
[ApiVersion("4.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
public class LiveSessionController : ControllerBase
{
    private readonly ILogger<LiveSessionController> logger;
    private readonly IContentService contentService;
    private readonly IChimeIntegrationService chimeIntegration;
    private readonly IContentIntegration integration;
    private readonly IMessageQueue<JobRequest> jobManagerQueue;
    private readonly IMessageQueue<UpdateMessage> contentUpdateQueue;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdate;
    private readonly IAutomationManagementService automationManagementService;
    private readonly IFeatureManager featureManager;
    private readonly IImageProcessorService imageProcessorService;
    private readonly IMessageQueue<ContentCreationMetricMessage> contentCreationMetricsMessages;
    private readonly IMapper mapper;
    private readonly INotificationService notificationService;
    private readonly ICollectionServiceClient collectionServiceClient;

    public LiveSessionController(
        ILogger<LiveSessionController> logger,
        IContentService contentService,
        IChimeIntegrationService chimeIntegration,
        IContentIntegration integration,
        IMessageQueue<JobRequest> jobManagerQueue,
        IMessageQueue<RelationshipUpdate> relationshipUpdate,
        IAutomationManagementService automationManagementService,
        IFeatureManager featureManager,
        IImageProcessorService imageProcessorService,
        IMessageQueue<ContentCreationMetricMessage> contentCreationMetricsMessages,
        IMapper mapper,
        IMessageQueue<UpdateMessage> contentUpdateQueue,
        INotificationService notificationService,
        ICollectionServiceClient collectionServiceClient)
    {
        this.logger = logger;
        this.contentService = contentService;
        this.chimeIntegration = chimeIntegration;
        this.integration = integration;
        this.jobManagerQueue = jobManagerQueue;
        this.relationshipUpdate = relationshipUpdate;
        this.automationManagementService = automationManagementService;
        this.featureManager = featureManager;
        this.imageProcessorService = imageProcessorService;
        this.contentCreationMetricsMessages = contentCreationMetricsMessages;
        this.mapper = mapper;
        this.contentUpdateQueue = contentUpdateQueue;
        this.notificationService = notificationService;
        this.collectionServiceClient = collectionServiceClient;
    }

    [HttpGet("{sessionId}")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<AttendeeInfo>> Retrieve(string sessionId)
    {
        //logger.LogInformation("MeetingId: {MeetingId}", sessionId);

        try
        {
            var meetingInfo = await chimeIntegration.GetMeeting(sessionId);
            return Ok(meetingInfo);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "MeetingId: {MeetingId}", sessionId);

            if (ex is ResourceNotFoundException) return Conflict(ex.Message);

            throw;
        }
    }

    [HttpPut("{sessionId}/Connect")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<AttendeeInfo>> Connect(string sessionId)
    {
        //logger.LogInformation("MeetingId: {MeetingId}", sessionId);

        try
        {
            var attendee = await chimeIntegration.ConnectAttendee(sessionId, User.GetCustomerId());
            _ = automationManagementService.OnChatConnect(attendee.ContentId, User.GetCustomerId());

            var content = await contentService.Retrieve(attendee.ContentId);

            if (content is null)
            {
                logger.LogWarning(
                    "Content with id {contentId} from meeting with id {sessionId} not found, message can not be sent",
                    attendee.ContentId, sessionId);
            }
            else
            {
                var customerChatRelations = await collectionServiceClient.GetSourceRelations(content.OwnerId.ToString(),
                    "Customer", "PersonalChat", default);

                if (Guid.TryParse(customerChatRelations.FirstOrDefault()?.TargetId, out var personalChatId))
                {
                    var chat = await contentService.Retrieve(personalChatId);

                    var chatArn = chat?.Properties?.FirstOrDefault(p => p.Key == Consts.CONTENT_CHATROOM_ARN).Value;

                    if (string.IsNullOrEmpty(chatArn))
                    {
                        logger.LogWarning("ARN for the room with id {roomId} not found", personalChatId);
                    }
                    else
                    {
                        /*
                        await notificationService.Trigger(EventName.JoinedToSideshow, chatArn, new
                        {
                            userName = User.FindFirstValue(ClaimTypes.Name),
                            contentName = content.OriginalTitle
                        });
                        */
                    }
                }
                else
                {
                    logger.LogWarning(
                        "Customer with id {customerId} does not have personal chat, message can not be sent",
                        content.OwnerId);
                }
            }

            return Ok(attendee);
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "MeetingId: {MeetingId}", sessionId);

            throw;
        }
    }

    [HttpPut("{contentId}/Reconnect")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<AttendeeInfo>> Reconnect(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null) return NotFound();

            content.Properties ??= new Dictionary<string, string>();
            var sessionId = content.Properties.GetValueOrDefault(Consts.CONTENT_SESSIONID);
            if (string.IsNullOrEmpty(sessionId)) return Conflict("No associated meeting");

            var meeting = await chimeIntegration.CreateMeeting(contentId);
            if (meeting.MeetingId != sessionId) // chime tries to return the same sessionId
            {
                content.Properties[Consts.CONTENT_SESSIONID] = meeting.MeetingId;
                await contentService.Update(content, User);

                _ = integration.EnqueueLogEntry(LogAction.Update, content, content, User, "Recreate meeting");
                _ = TraceChimeAction(meeting, "Success: Recreated");
            }

            var attendee = await chimeIntegration.ConnectAttendee(meeting.MeetingId, User.GetCustomerId());
            _ = automationManagementService.OnChatConnect(attendee.ContentId, User.GetCustomerId());

            var customerChatRelations =
                await collectionServiceClient.GetSourceRelations(content.OwnerId.ToString(), "Customer", "PersonalChat",
                    default);

            if (Guid.TryParse(customerChatRelations.FirstOrDefault()?.TargetId, out var personalChatId))
            {
                var chat = await contentService.Retrieve(personalChatId);

                var chatArn = chat?.Properties?.FirstOrDefault(p => p.Key == Consts.CONTENT_CHATROOM_ARN).Value;

                if (string.IsNullOrEmpty(chatArn))
                    logger.LogWarning("ARN for the room with id {roomId} not found", personalChatId);
                else
                    await notificationService.Trigger(EventName.JoinedToSideshow, content.OwnerId, new
                    {
                        userName = User.FindFirstValue(ClaimTypes.Name),
                        contentName = content.OriginalTitle
                    });
            }
            else
            {
                logger.LogWarning("Customer with id {customerId} does not have personal chat, message can not be sent",
                    content.OwnerId);
            }

            return Ok(attendee);
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpDelete("{sessionId}/Disconnect")]
    [ProducesResponseType(204)] // NoContent
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> Disconnect(string sessionId)
    {
        //logger.LogInformation("MeetingId: {MeetingId}", sessionId);

        try
        {
            var attendee = await chimeIntegration.DisconnectAttendee(sessionId, User.GetCustomerId());
            _ = automationManagementService.OnChatDisconnect(attendee.ContentId, User.GetCustomerId());

            return NoContent();
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "MeetingId: {MeetingId}", sessionId);

            throw;
        }
    }

    [HttpPost("{sessionId}/Record")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<CaptureInfo>> Record(string sessionId)
    {
        //logger.LogInformation("MeetingId: {MeetingId}", sessionId);

        try
        {
            var meeting = await chimeIntegration.GetMeeting(sessionId);
            var content = await contentService.Retrieve(meeting.ContentId);
            if (content == null)
            {
                _ = chimeIntegration.DeleteMeeting(meeting.MeetingId);
                return Conflict("The content has been deleted");
            } // content no longer exists

            var captureId = content.Properties.GetValueOrDefault(Consts.CONTENT_CAPTUREID);
            var captureInfo = await chimeIntegration.TryGetRecording(captureId);
            if (captureInfo != null) return Ok(captureId);

            captureInfo = await chimeIntegration.StartRecording(sessionId);

            var newContent = content.RecursiveCopy();
            newContent.Properties[Consts.CONTENT_CAPTUREID] = captureInfo.CaptureId;
            newContent.Properties[Consts.CONTENT_MEDIAPIPELINEID] =
                captureInfo.CaptureId; // because the CONTENT_CAPTUREID will be removed
            newContent = await contentService.Save(newContent, Guid.Empty, true);
            _ = integration.EnqueueLogEntry(LogAction.Update, content, newContent, User, "Capture Starting");
            _ = TraceChimeAction(captureInfo, "Success: Record Started");

            return Ok(captureInfo);
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "MeetingId: {MeetingId}", sessionId);

            throw;
        }
    }

    [HttpDelete("{sessionId}")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> Finish(string? sessionId)
    {
        //logger.LogInformation("MeetingId: {MeetingId}", sessionId);

        if (string.IsNullOrEmpty(sessionId)) throw new ArgumentException("sessionId is required.");

        try
        {
            var meeting = await chimeIntegration.GetMeeting(sessionId);
            var content = await contentService.Retrieve(meeting.ContentId);
            if (content == null)
            {
                _ = chimeIntegration.DeleteMeeting(meeting.MeetingId);
                return Conflict("The content has been deleted");
            } // content no longer exists

            sessionId = content.Properties.GetValueOrDefault(Consts.CONTENT_SESSIONID);
            if (string.IsNullOrEmpty(sessionId)) return NoContent(); // already finished

            var newContent = content.RecursiveCopy();

            var captureId = newContent.Properties.GetValueOrDefault(Consts.CONTENT_CAPTUREID);
            if (!string.IsNullOrEmpty(captureId)) await StopRecording(newContent, captureId);

            var streamId = content.Properties.GetValueOrDefault(Consts.CONTENT_STREAMID);
            if (!string.IsNullOrEmpty(streamId)) await StopStreaming(newContent, streamId);

            _ = chimeIntegration.DeleteMeeting(sessionId);
            newContent.Properties.Remove(Consts.CONTENT_SESSIONID);

            await contentUpdateQueue.Enqueue(new UpdatePropertiesMessage()
            {
                Id = content.Id.ToString(),
                ContentId = content.Id.ToString(),
                UpdateStrategy = ListUpdateStrategy.Remove,
                Properties = new Dictionary<string, string>()
                {
                    { Consts.CONTENT_SESSIONID, string.Empty }
                }
            });

            _ = TraceChimeAction(meeting, "Success: Meeting ended");

            var payload = new List<Property>
            {
                new() { Name = "ContentId", Value = $"{content.Id}" },
                new() { Name = "Duration", Value = content.Duration },
                new() { Name = "Type", Value = "ContentDurationContract" },
                new() { Name = "Timestamp", Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") }
            };

            var contentCreationMetricsMessage = new ContentCreationMetricMessage
            {
                Properties = payload,
                RunDate = DateTime.UtcNow,
                TimeStamp = DateTime.UtcNow
            };

            await contentCreationMetricsMessages.Enqueue(contentCreationMetricsMessage);

            return AcceptedAtAction(nameof(ContentController.Get), nameof(ContentController)[..^10],
                new { contentId = content.Id });
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "MeetingId: {MeetingId}", sessionId);

            throw;
        }
    }

    [HttpPost("[action]/{parentId}")]
    [HttpPost("[action]")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> Create(Guid? parentId, CreateRecordingRequestV2 request)
    {
        //logger.LogInformation("ParentId: {ParentId}, Request: {@Request}", parentId, request);

        try
        {
            if (parentId.HasValue)
            {
                var parent = await contentService.Retrieve(parentId.Value);
                // does not exist or type is not 'Remix' or 'Collection' or 'Playlist'
                if (parent?.Type != ContentType.Remix && parent?.Type != ContentType.Collection &&
                    parent?.Type != ContentType.Playlist && parent?.Type != ContentType.RemixV2)
                    return base.Problem("Content does not exist or is not a Remix and not Collection and not Playlist",
                        nameof(parentId),
                        StatusCodes.Status400BadRequest, "Bad Request", "about:blank");
            }

            request.Type = ContentType.CameraCapture;
            request.ReferenceId = parentId?.ToString();
            request.Properties ??= new Dictionary<string, string>();
            request.Properties.Add(Consts.CONTENT_IVS_VIEWCONFIGURATION, request.ViewConfiguration.ToString());
            request.Properties.Add(Consts.CONTENT_IVS_CHANNELLATENCYMODE, request.ChannelLatencyMode.ToString());
            request.Properties.Add(Consts.CONTENT_IVS_CHANNELTYPE, request.ChannelType.ToString());

            var key = Consts.CONTENT_WORKFLOWID;
            if (request.WorkflowId.HasValue && request.WorkflowId != Guid.Empty)
            {
                request.Properties ??= new Dictionary<string, string>();
                request.Properties[key] = request.WorkflowId.ToString()!;
            }

            var entity = mapper.Map<Content>(request);
            entity.ReferenceId = parentId?.ToString();

            var content = await integration.CreateChimeContent(entity, User);
            if (content == null) return BadRequest("Content could not be created");

            if (parentId.HasValue) await AddToRemixContentList(parentId.Value, content.Id);

            if (await AllowAutoImageGenerationFeature())
            {
                var name = entity.Localizations?.FirstOrDefault().Value?.Name;
                if (!string.IsNullOrEmpty(name))
                    await imageProcessorService.GenerateImage(name, content.Id.ToString());
            }

            //execute automation
            _ = automationManagementService.OnContentCreated(content, User);

            var customerId = User.GetCustomerId().ToString();

            var customerChatRelations =
                await collectionServiceClient.GetSourceRelations(customerId, "Customer", "PersonalChat", default);

            if (Guid.TryParse(customerChatRelations.FirstOrDefault()?.TargetId, out var personalChatId))
            {
                var chat = await contentService.Retrieve(personalChatId);

                var chatArn = chat?.Properties?.FirstOrDefault(p => p.Key == Consts.CONTENT_CHATROOM_ARN).Value;

                if (string.IsNullOrEmpty(chatArn))
                {
                    logger.LogWarning("ARN for the room with id {roomId} not found", personalChatId);
                }
                else
                {
                    /*
                    await notificationService.Trigger(EventName.SideshowAnnouncement, chatArn, new
                    {
                        userName = User.FindFirstValue(ClaimTypes.Name),
                        contentName = content.OriginalTitle,
                        contentId = content.Id
                    });
                    */
                }
            }
            else
            {
                logger.LogWarning("Customer with id {customerId} does not have personal chat, message can not be sent",
                    customerId);
            }

            return CreatedAtAction(nameof(ContentController.Get), nameof(ContentController)[..^10],
                new { contentId = content?.Id }, ContentResponse.FromEntity(content));
        }
        catch (Exception ex)
        {
            logger.LogError("Error message: {message}, StackTrace: {stackTrace}", ex.Message, ex.StackTrace);
            logger.LogCritical(ex, "ParentId: {ParentId}, Request: {@Request}", parentId, request);
            throw;
        }
    }

    private async Task StopRecording(Content content, string captureId)
    {
        var captureInfo = await chimeIntegration.StopRecording(captureId);
        content.Properties.Remove(Consts.CONTENT_CAPTUREID);
        content.ProcessingStatus = ProcessingStatus.UnderProcessing;
        _ = TraceChimeAction(captureInfo, "Success: Record Stopped");

        // trigger processing & importing of the chime recording (with 10 seconds delay)
        await jobManagerQueue.Enqueue(new ChimeImportJobRequest(User.GetCustomerId(), User.GetEmail())
        {
            ReferenceObjectId = content.Id.ToString(),
            ReferenceObjectName = content.OriginalTitle,
            S3ArchiveUrl = captureInfo.ArchiveUrl,
            RemixContentId = content.ReferenceId
        }, 10);

        //update content via SQS
        await contentUpdateQueue.Enqueue(new BaseDataMessage()
        {
            ContentId = content.Id.ToString(),
            ProcessingStatus = content.ProcessingStatus
        });

        await contentUpdateQueue.Enqueue(new UpdatePropertiesMessage()
        {
            Id = content.Id.ToString(),
            ContentId = content.Id.ToString(),
            UpdateStrategy = ListUpdateStrategy.Remove,
            Properties = new Dictionary<string, string>()
            {
                { Consts.CONTENT_CAPTUREID, string.Empty }
            }
        });
    }

    private async Task StopStreaming(Content content, string streamId)
    {
        var sinkInfo = await chimeIntegration.StopStreaming(streamId);
        content.Properties.Remove(Consts.CONTENT_STREAMID);
        _ = TraceChimeAction(sinkInfo, "Success: Stream Stopped");

        //update content via SQS
        await contentUpdateQueue.Enqueue(new UpdatePropertiesMessage()
        {
            Id = content.Id.ToString(),
            ContentId = content.Id.ToString(),
            UpdateStrategy = ListUpdateStrategy.Remove,
            Properties = new Dictionary<string, string>()
            {
                { Consts.CONTENT_STREAMID, string.Empty }
            }
        });
    }

    private async Task TraceChimeAction(MeetingRef meeting, string description)
    {
        var sessionId = Guid.TryParse(meeting.MeetingId, out var id) ? id : (Guid?)null;
        await integration.TracelogAction(meeting.ContentId, sessionId, description);
    }

    private async Task AddToRemixContentList(Guid remixId, Guid contentId)
    {
        await relationshipUpdate.Enqueue(new SingleRelationshipUpdate
        {
            Action = ActionKind.Add,
            Relation = ContentRelation.Playlist,
            SourceId = remixId.ToString(),
            TargetId = contentId.ToString()
        });
    }

    private async Task<bool> AllowAutoImageGenerationFeature()
    {
        if (await featureManager.IsEnabledAsync("AllowAutomation_AutoImageGeneration")) return true;
        logger.LogInformation("Auto Image generation feature is off");
        return false;
    }
}