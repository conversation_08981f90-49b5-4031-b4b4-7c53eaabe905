using BlueGuava.ContentManagement.Api.Controllers.LegacyVersions;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Filters;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.Library;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing.Printing;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

/// <summary>
/// Query parameters for content search operations
/// </summary>
public class ContentQueryParameters
{
    /// <summary>
    /// Ordering group parameter
    /// </summary>
    [Required]
    public string OrderingGroup { get; set; } = string.Empty;

    /// <summary>
    /// Filter parameter
    /// </summary>
    [Required]
    public string Filter { get; set; } = string.Empty;

    /// <summary>
    /// Search query text
    /// </summary>
    public string? Query { get; set; }

    /// <summary>
    /// Content types to filter by
    /// </summary>
    public IEnumerable<ContentType>? Types { get; set; }

    /// <summary>
    /// Page size for pagination
    /// </summary>
    [Range(1, 1000, ErrorMessage = "Page size must be between 1 and 1000")]
    public int? PageSize { get; set; }

    /// <summary>
    /// Page index for pagination (0-based)
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "Page index must be non-negative")]
    public int PageIndex { get; set; } = 0;

    /// <summary>
    /// Filter by published status
    /// </summary>
    public bool? Published { get; set; }

    /// <summary>
    /// Organization filters
    /// </summary>
    public List<string>? Organizations { get; set; }

    /// <summary>
    /// Longitude for location-based search
    /// </summary>
    [Range(-180.0, 180.0, ErrorMessage = "Longitude must be between -180 and 180")]
    public double? Longitude { get; set; }

    /// <summary>
    /// Latitude for location-based search
    /// </summary>
    [Range(-90.0, 90.0, ErrorMessage = "Latitude must be between -90 and 90")]
    public double? Latitude { get; set; }

    /// <summary>
    /// Search radius in kilometers
    /// </summary>
    [Range(1, 1000, ErrorMessage = "Radius must be between 1 and 1000 kilometers")]
    public int Radius { get; set; } = 5;

    /// <summary>
    /// Owner ID filter
    /// </summary>
    public string? OwnerId { get; set; }

    /// <summary>
    /// Group parameter for grouped queries
    /// </summary>
    public string? Group { get; set; }
}

/// <summary>
/// Feature flag constants for content search
/// </summary>
public static class ContentSearchFeatureFlags
{
    public const string AllowOwnerIdFromToken = "ContentSearch_AllowOwnerIdFromToken";
    public const string PublishedOnly = "ContentSearch_PublishedOnly";
}

public partial class ContentController : ContentControllerBase
{
    /// <summary>
    /// Builds ContentSearch object from query parameters with feature flag validation
    /// </summary>
    /// <param name="parameters">Query parameters</param>
    /// <returns>Configured ContentSearch object</returns>
    private async Task<ContentSearch> BuildContentSearchAsync(ContentQueryParameters parameters)
    {
        var objectOwnerId = await GetOwnerIdAsync(parameters.OwnerId);
        var published = await GetPublishedFilterAsync(parameters.Published);

        return new ContentSearch
        {
            Query = parameters.Query,
            Types = parameters.Types,
            OwnerId = objectOwnerId.ToString(),
            Longitude = parameters.Longitude,
            Latitude = parameters.Latitude,
            Published = published,
            Radius = parameters.Radius,
            OrganizationFilters = parameters.Organizations ?? new List<string>()
        };
    }

    /// <summary>
    /// Determines the owner ID based on feature flags and user context
    /// </summary>
    /// <param name="requestedOwnerId">Owner ID from request</param>
    /// <returns>Resolved owner ID</returns>
    private async Task<Guid> GetOwnerIdAsync(string? requestedOwnerId)
    {
        var allowOwnerIdToken = await featureManager.IsEnabledAsync(ContentSearchFeatureFlags.AllowOwnerIdFromToken);

        if (allowOwnerIdToken)
        {
            return User.IsTechnical() ? Guid.Empty : User.GetCustomerId();
        }

        return Guid.TryParse(requestedOwnerId, out var ownerId) ? ownerId : Guid.Empty;
    }

    /// <summary>
    /// Determines the published filter based on feature flags
    /// </summary>
    /// <param name="requestedPublished">Published filter from request</param>
    /// <returns>Resolved published filter</returns>
    private async Task<bool?> GetPublishedFilterAsync(bool? requestedPublished)
    {
        var showOnlyPublished = await featureManager.IsEnabledAsync(ContentSearchFeatureFlags.PublishedOnly, true);
        return showOnlyPublished ? true : requestedPublished;
    }

    /// <summary>
    /// Extracts count from catalog properties
    /// </summary>
    /// <param name="catalog">Catalog object</param>
    /// <returns>Total count or 0 if not found</returns>
    private static int ExtractTotalCount(Library.Interop.v2.Object catalog)
    {
        return int.TryParse(catalog.Properties?["TotalCount"], out var count) ? count : 0;
    }

    /// <summary>
    /// Get dynamic catalog result from open search
    /// </summary>
    /// <returns></returns>
    [HttpGet("Query")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<Library.Interop.v2.Object>> Query(
        [FromQuery] string o,
        [FromQuery] string f,
        [FromQuery] string? q,
        [FromQuery] IEnumerable<ContentType>? type,
        [FromQuery] int? pageSize = null,
        [FromQuery] int pageIndex = 0,
        [FromQuery] bool? published = null,
        [FromQuery] List<string>? orgs = null,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int radius = 5,
        [FromQuery] string? ownerId = null)
    {
        try
        {
            var parameters = new ContentQueryParameters
            {
                OrderingGroup = o,
                Filter = f,
                Query = q,
                Types = type,
                PageSize = pageSize,
                PageIndex = pageIndex,
                Published = published,
                Organizations = orgs,
                Longitude = longitude,
                Latitude = latitude,
                Radius = radius,
                OwnerId = ownerId
            };

            var searchArgs = await BuildContentSearchAsync(parameters);
            logger.LogInformation("Query SearchArgs: {@SearchArgs}", searchArgs);

            var catalog = await contentDynamicCatalogService.Search(searchArgs, o, f, pageIndex, pageSize);
            if (catalog == null)
            {
                logger.LogWarning("No catalog found for OrderingGroup: {OrderingGroup}, Filter: {Filter}", o, f);
                return NotFound();
            }

            return Ok(catalog);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute query - OrderingGroup: {OrderingGroup}, Filter: {Filter}", o, f);
            throw;
        }
    }
    /// <summary>
    /// Get dynamic catalog result from open search
    /// </summary>
    /// <returns></returns>
    [HttpGet("Query/Cache")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    [ResponseCache(CacheProfileName = CacheProfiles.FiveMinutes)]
    public async Task<ActionResult<Library.Interop.v2.Object>> QueryCache(
        [FromQuery] string o,
        [FromQuery] string f,
        [FromQuery] string? q,
        [FromQuery] IEnumerable<ContentType>? type,
        [FromQuery] int? pageSize = null,
        [FromQuery] int pageIndex = 0,
        [FromQuery] bool? published = null,
        [FromQuery] List<string>? orgs = null,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int radius = 5,
        [FromQuery] string? ownerId = null)
    {
        try
        {
            var parameters = new ContentQueryParameters
            {
                OrderingGroup = o,
                Filter = f,
                Query = q,
                Types = type,
                PageSize = pageSize,
                PageIndex = pageIndex,
                Published = published,
                Organizations = orgs,
                Longitude = longitude,
                Latitude = latitude,
                Radius = radius,
                OwnerId = ownerId
            };

            // Important: include user identity in the cache key if results are permission-scoped
            var userScope = User?.Identity?.IsAuthenticated == true ? User.Identity!.Name : "anon";

            string cacheKey = BuildQueryCountCacheKey(parameters, userScope);


            if (_memoryCache.TryGetValue(cacheKey, out Library.Interop.v2.Object cachedCatalog))
            {
                logger.LogDebug("QueryCache cache hit: {CacheKey}", cacheKey);
                return Ok(cachedCatalog);
            }
            var searchArgs = await BuildContentSearchAsync(parameters);
            logger.LogInformation("Query SearchArgs: {@SearchArgs}", searchArgs);

            var catalog = await contentDynamicCatalogService.Search(searchArgs, o, f, pageIndex, pageSize);
            if (catalog == null)
            {
                logger.LogWarning("No catalog found for OrderingGroup: {OrderingGroup}, Filter: {Filter}", o, f);
                return NotFound();
            }
            // Cache server-side for 5 minutes
            _memoryCache.Set(cacheKey, catalog, new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                Size = 1
                // If you configured SizeLimit on IMemoryCache, also set Size = 1 here.
            });
            return Ok(catalog);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute query - OrderingGroup: {OrderingGroup}, Filter: {Filter}", o, f);
            throw;
        }
    }

    [HttpPost("Query/Script/Cache")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    [ResponseCache(CacheProfileName = CacheProfiles.FiveMinutes)]
    public async Task<ActionResult<Library.Interop.v2.Object>> QueryScriptCache(MustacheQueryRequest request)
    {
        try
        {
            logger.LogInformation("QueryScript: {request}", request.ToJson());

            if (string.IsNullOrEmpty(request.scriptId)) return BadRequest("SCRIPT_ID_REQUIRED");

            // Build cache key
            var cacheKey = BuildQueryScriptCacheKey(request);

            if (_memoryCache.TryGetValue(cacheKey, out Library.Interop.v2.Object cachedResult))
            {
                logger.LogDebug("QueryScript cache hit: {CacheKey}", cacheKey);
                return Ok(cachedResult);
            }

            var catalog = await contentDynamicCatalogService.QueryScript(request.scriptId, request.parameters, request.pageSize.Value, request.pageIndex.Value, default);
            if (catalog == null)
            {
                logger.LogWarning("No catalog found for QueryScript: {request}", request.ToJson());
                return NotFound();
            }
            // Cache for 5 minutes
            _memoryCache.Set(cacheKey, catalog, new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                Size = 1
            });
            logger.LogInformation("QueryScript completed successfully");
            return Ok(catalog);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute QueryScript: {request}", request.ToJson());
            throw;
        }
    }

    [HttpPost("Query/Script")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    [ResponseCache(CacheProfileName = CacheProfiles.OneMinute)]
    public async Task<ActionResult<Library.Interop.v2.Object>> QueryScript(MustacheQueryRequest request)
    {
        try
        {
            logger.LogInformation("QueryScript: {request}", request.ToJson());

            if (string.IsNullOrEmpty(request.scriptId)) return BadRequest("SCRIPT_ID_REQUIRED");

            var catalog = await contentDynamicCatalogService.QueryScript(request.scriptId, request.parameters, request.pageSize.Value, request.pageIndex.Value, default);
            if (catalog == null)
            {
                logger.LogWarning("No catalog found for QueryScript: {request}", request.ToJson());
                return NotFound();
            }

            logger.LogInformation("QueryScript completed successfully");
            return Ok(catalog);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute QueryScript: {request}", request.ToJson());
            throw;
        }
    }

    private static string BuildQueryScriptCacheKey(MustacheQueryRequest request)
    {
        var parametersKey = request.parameters != null
            ? string.Join("&", request.parameters.OrderBy(p => p.Key)
                .Select(p => $"{p.Key}={p.Value}"))
            : string.Empty;

        return $"QueryScript:{request.scriptId}:p:{parametersKey}:ps:{request.pageSize}:pi:{request.pageIndex}";
    }
    /// <summary>
    /// Builds ContentSearch from QueryRequest object
    /// </summary>
    /// <param name="query">Query request</param>
    /// <returns>ContentSearch object</returns>
    private async Task<ContentSearch> BuildContentSearchFromQueryRequestAsync(QueryRequest query)
    {
        // TODO: Implement proper mapping from QueryRequest to ContentSearch
        // This is a placeholder that needs to be implemented based on QueryRequest structure
        var searchArgs = new ContentSearch();

        if (query.Content != null)
        {
            searchArgs.Published = query.Content.Published;
            // Map other content properties as needed
        }

        if (query.Location != null)
        {
            if (query.Location.Coordinate != null)
            {
                searchArgs.Latitude = query.Location.Coordinate.Latitude;
                searchArgs.Longitude = query.Location.Coordinate.Longitude;
                searchArgs.Radius = query.Location.Radius;
            }
            // Map other location properties as needed
        }

        return searchArgs;
    }

    /// <summary>
    /// Get count of content items matching search criteria
    /// </summary>
    /// <returns>Total count of matching items</returns>
    [HttpGet("Query/Count")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    [ResponseCache(CacheProfileName = CacheProfiles.DynamicCatalog)]
    public async Task<ActionResult<int>> QueryCount(
        [FromQuery] string o,
        [FromQuery] string f,
        [FromQuery] string? q = null,
        [FromQuery] IEnumerable<ContentType>? type = null,
        [FromQuery] bool? published = null,
        [FromQuery] List<string>? orgs = null,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int radius = 5,
        [FromQuery] string? ownerId = null)
    {
        try
        {
            var parameters = new ContentQueryParameters
            {
                OrderingGroup = o,
                Filter = f,
                Query = q,
                Types = type,
                Published = published,
                Organizations = orgs,
                Longitude = longitude,
                Latitude = latitude,
                Radius = radius,
                OwnerId = ownerId
            };

            var searchArgs = await BuildContentSearchAsync(parameters);

            var catalog = await contentDynamicCatalogService.Count(
                searchArgs,
                orderingGroup: o,
                filter: f,
                pageIndex: 0,
                pageSize: 0
            );

            int totalCount = catalog?.Properties?
                .FirstOrDefault(p => p.Name == "TotalCount")?.Value is string val && int.TryParse(val, out var parsed)
                ? parsed
                : 0;

            return Ok(totalCount);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute count query - OrderingGroup: {OrderingGroup}, Filter: {Filter}", o, f);
            return StatusCode(500, "Failed to execute count query");
        }
    }

    /// <summary>
    /// Get count of content items matching search criteria
    /// </summary>
    /// <returns>Total count of matching items</returns>
    [HttpGet("Query/Count/Cache")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    [ResponseCache(CacheProfileName = CacheProfiles.DynamicCatalog)]
    public async Task<ActionResult<int>> QueryCountCache(
        [FromQuery] string o,
        [FromQuery] string f,
        [FromQuery] string? q = null,
        [FromQuery] IEnumerable<ContentType>? type = null,
        [FromQuery] bool? published = null,
        [FromQuery] List<string>? orgs = null,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int radius = 5,
        [FromQuery] string? ownerId = null)
    {
        try
        {
            var parameters = new ContentQueryParameters
            {
                OrderingGroup = o,
                Filter = f,
                Query = q,
                Types = type,
                Published = published,
                Organizations = orgs,
                Longitude = longitude,
                Latitude = latitude,
                Radius = radius,
                OwnerId = ownerId
            };

            var searchArgs = await BuildContentSearchAsync(parameters);

            // Important: include user identity in the cache key if results are permission-scoped
            var userScope = User?.Identity?.IsAuthenticated == true ? User.Identity!.Name : "anon";

            string cacheKey = BuildQueryCountCacheKey(parameters, userScope);

            if (_memoryCache.TryGetValue(cacheKey, out int cachedCount))
            {
                logger.LogDebug("QueryCount cache hit: {CacheKey}", cacheKey);
                return Ok(cachedCount);
            }

            var catalog = await contentDynamicCatalogService.Count(
                searchArgs,
                orderingGroup: o,
                filter: f,
                pageIndex: 0,
                pageSize: 0
            );

            int totalCount = catalog?.Properties?
                .FirstOrDefault(p => p.Name == "TotalCount")?.Value is string val && int.TryParse(val, out var parsed)
                ? parsed
                : 0;

            // Cache positive result for 5 minutes
            _memoryCache.Set(cacheKey, totalCount, new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                // Optional: size if you use SizeLimit
                Size = 1
            });
            return Ok(totalCount);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute count query - OrderingGroup: {OrderingGroup}, Filter: {Filter}", o, f);
            return StatusCode(500, "Failed to execute count query");
        }
    }

   private static string BuildQueryCountCacheKey(ContentQueryParameters p, string? userScope)
    {
        // Normalize collections for stable keys
        var types = p.Types?.Select(t => t.ToString()).OrderBy(s => s) ?? Enumerable.Empty<string>();
        var orgs = p.Organizations?.OrderBy(s => s) ?? Enumerable.Empty<string>();

        // Build a compact, deterministic key
        return string.Join('|', new[]
        {
            "QueryCount",
            $"u:{userScope ?? "anon"}",
            $"o:{p.OrderingGroup}",
            $"f:{p.Filter}",
            $"q:{p.Query}",
            $"types:[{string.Join(',', types)}]",
            $"ps:{p.PageSize}",
            $"pi:{p.PageIndex}",
            $"pub:{p.Published}",
            $"orgs:[{string.Join(',', orgs)}]",
            $"lon:{p.Longitude}",
            $"lat:{p.Latitude}",
            $"r:{p.Radius}",
            $"owner:{p.OwnerId}"
        });
    }
    /// <summary>
    /// Get grouped content query results with hit counts per group
    /// </summary>
    /// <returns>Grouped query results with hit counts</returns>
    [HttpGet("Query/Group")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult> QueryWithGroup(
        [FromQuery] string o,
        [FromQuery] string f,
        [FromQuery] string? q,
        [FromQuery] string? g,
        [FromQuery] IEnumerable<ContentType>? type,
        [FromQuery] int? pageSize = null,
        [FromQuery] int pageIndex = 0,
        [FromQuery] bool? published = null,
        [FromQuery] List<string>? orgs = null,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int radius = 5,
        [FromQuery] string? ownerId = null)
    {
        try
        {
            var parameters = new ContentQueryParameters
            {
                OrderingGroup = o,
                Filter = f,
                Query = q,
                Types = type,
                PageSize = pageSize,
                PageIndex = pageIndex,
                Published = published,
                Organizations = orgs,
                Longitude = longitude,
                Latitude = latitude,
                Radius = radius,
                OwnerId = ownerId,
                Group = g
            };

            var searchArgs = await BuildContentSearchAsync(parameters);
            logger.LogInformation("QueryWithGroup SearchArgs: {@SearchArgs}", searchArgs);

            var catalog = await contentDynamicCatalogService.Query(searchArgs, o, f, pageIndex, pageSize, g);
            if (catalog.Hits == null)
            {
                logger.LogWarning("No grouped results found - OrderingGroup: {OrderingGroup}, Filter: {Filter}, Group: {Group}", o, f, g);
                return NotFound();
            }

            var result = new { catalog.Hits, catalog.GroupCounts };
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to execute grouped query - OrderingGroup: {OrderingGroup}, Filter: {Filter}, Group: {Group}", o, f, g);
            throw;
        }
    }

    [HttpGet("Query/GroupOnly")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    [Authorize]
    public async Task<ActionResult> QueryGroupOnly(
    [FromQuery] string o,
    [FromQuery] string f,
    [FromQuery] string? q,
    [FromQuery] string? g,
    [FromQuery] IEnumerable<ContentType>? type,
    [FromQuery] int pageSize = 50,
    [FromQuery] int pageIndex = 0,
    [FromQuery] bool? published = null,
    [FromQuery] List<string>? orgs = null,
    [FromQuery] double? longitude = null,
    [FromQuery] double? latitude = null,
    [FromQuery] int radius = 5,
    [FromQuery] string? ownerId = null)
    {
        try
        {
            var parameters = new ContentQueryParameters
            {
                OrderingGroup = o,
                Filter = f,
                Query = q,
                Types = type,
                PageSize = pageSize,
                PageIndex = pageIndex,
                Published = published,
                Organizations = orgs,
                Longitude = longitude,
                Latitude = latitude,
                Radius = radius,
                OwnerId = ownerId,
                Group = g
            };

            var searchArgs = await BuildContentSearchAsync(parameters);
            logger.LogInformation("QueryGroupOnly SearchArgs: {@SearchArgs}", searchArgs);

            var result = await contentDynamicCatalogService.QueryGroupsOnly(
                searchArgs, o, f, pageIndex, pageSize, g);

            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex,
                "Failed to execute group-only query - OrderingGroup: {OrderingGroup}, Filter: {Filter}, Group: {Group}",
                o, f, g);
            throw;
        }
    }
}