using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common.Entities.Chime;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Service.Chime.Exceptions;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;
using LogAction = BlueGuava.UserLog.Enums.Action;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

/// <summary>
/// Live stream controller
/// </summary>
[ApiController]
[ApiVersion("4.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
public class InteractiveController : ControllerBase
{
    private readonly ILogger<InteractiveController> logger;
    private readonly IContentService contentService;
    private readonly IAmazonIvsService ivsService;
    private readonly IContentIntegration integration;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdate;
    private readonly IAutomationManagementService automationManagementService;
    private readonly IChimeIntegrationService chimeIntegration;
    private readonly IMapper mapper;

    /// <summary>
    /// Constructor
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="contentService"></param>
    /// <param name="ivsService"></param>
    /// <param name="integration"></param>
    /// <param name="relationshipUpdate"></param>
    /// <param name="automationManagementService"></param>
    /// <param name="chimeIntegration"></param>
    /// <param name="mapper"></param>
    public InteractiveController(
        ILogger<InteractiveController> logger,
        IContentService contentService,
        IAmazonIvsService ivsService,
        IContentIntegration integration,
        IMessageQueue<RelationshipUpdate> relationshipUpdate,
        IAutomationManagementService automationManagementService,
        IChimeIntegrationService chimeIntegration,
        IMapper mapper)
    {
        this.logger = logger;
        this.contentService = contentService;
        this.ivsService = ivsService;
        this.integration = integration;
        this.relationshipUpdate = relationshipUpdate;
        this.automationManagementService = automationManagementService;
        this.chimeIntegration = chimeIntegration;
        this.mapper = mapper;
    }


    /// <summary>
    /// Validates if a IVS live exists for a given Chime meeting. <br/>
    /// Start will be called if the live exists but is not streaming.
    /// </summary>
    /// <returns></returns>
    [HttpPost("Start/{sessionId}/{liveContentId}")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<LiveSinkInfo>> Start(string sessionId, Guid liveContentId, [FromBody] CreateInteractiveRequest? request)
    {
        //logger.LogInformation("MeetingId: {sessionId}, LiveContentId: {LiveContentId}", sessionId, liveContentId);

        try
        {
            var meeting = await chimeIntegration.GetMeeting(sessionId);
            var content = await contentService.Retrieve(meeting.ContentId);
            if (content == null)
            {
                _ = chimeIntegration.DeleteMeeting(meeting.MeetingId);
                return Conflict("The content has been deleted");
            } // content no longer exists

            var oldContent = content.RecursiveCopy();

            var streamInfo = await ivsService.TryGetLiveStream(liveContentId);
            if (string.IsNullOrEmpty(streamInfo?.StreamKey))
                return Conflict($"Cannot stream on {liveContentId}");

            var sinkInfo = await chimeIntegration.StartStreaming(sessionId, streamInfo,
                request?.ViewConfiguration ?? ViewConfigurations.Default);
            sinkInfo.LiveStreamUrl = streamInfo.PlaybackUrl;
            sinkInfo.LiveContentId = liveContentId;

            content.Properties ??= new Dictionary<string, string>();
            content.Properties[Consts.CONTENT_STREAMID] = sinkInfo.LiveSinkId ?? string.Empty;

            content = await contentService.Save(content, User.GetCustomerId(), true);

            _ = integration.EnqueueLogEntry(LogAction.Update, oldContent, content, User, "Stream Starting");
            _ = TraceChimeAction(meeting, "Success: Streaming Started");


            var liveContent = await contentService.Retrieve(liveContentId);
            if (liveContent == null)
                return Conflict("The content not found: " + liveContentId); // content no longer exists

            var oldLiveContent = liveContent.RecursiveCopy();

            //updates the live content with the stream started property
            liveContent.Properties ??= new Dictionary<string, string>();
            liveContent.Properties[Consts.CONTENT_BROADCAST_STATUS] = "Started";
            liveContent = await contentService.Save(liveContent, User.GetCustomerId(), true);
            _ = integration.EnqueueLogEntry(LogAction.Update, oldLiveContent, liveContent, User, "Stream Starting");


            return Ok(sinkInfo);
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);

            logger.LogCritical(ex, "SessionId: {sessionId}, LiveContentId: {LiveContentId}", sessionId, liveContentId);
            throw;
        }
    }

    /// <summary>
    /// Creates a IVS live for a given Chime meeting and connects the live content to the meeting.
    /// </summary>
    /// <returns></returns>
    [HttpPost("Create/{sessionId}/{relatedId}")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<LiveSinkInfo>> Create([FromBody] CreateLiveContentRequest createLiveContentRequest,
        [FromRoute] string sessionId, [FromRoute] Guid relatedId)
    {
        //logger.LogInformation("sessionId: {sessionId}, RelatedId: {RelatedId}", sessionId, relatedId);

        try
        {
            var meeting = await chimeIntegration.GetMeeting(sessionId);
            var content = await contentService.Retrieve(meeting.ContentId);
            if (content == null)
            {
                _ = chimeIntegration.DeleteMeeting(meeting.MeetingId);
                return Conflict("The content has been deleted");
            } // content no longer exists

            var request = new CreateLiveRequest
            {
                OriginalTitle = content.OriginalTitle + " Stream",
                ReleaseDate = createLiveContentRequest.ReleaseDate,
                Properties = createLiveContentRequest.Properties,
                Assets = createLiveContentRequest.Assets,
                Duration = createLiveContentRequest.Duration
            };

            var entity = mapper.Map<Content>(request.ToContentRequest(null));
            var liveContent = await integration.CreateIVSLiveContent(entity, User,
                createLiveContentRequest?.ChannelLatencyMode ?? IVSChannelLatencyMode.NORMAL,
                createLiveContentRequest?.ChannelType ?? IVSChannelType.STANDARD);
            _ = automationManagementService.OnIVSLiveCreated(liveContent, User);

            //save the sessionId in the live content properties
            liveContent.Properties ??= new Dictionary<string, string>();
            liveContent.Properties[Consts.CONTENT_SESSIONID] = sessionId;
            if (createLiveContentRequest.Properties != null)
                MergeProperties(liveContent, createLiveContentRequest.Properties);

            liveContent = await contentService.Save(liveContent, Guid.Empty, true);

            var liveContentId = liveContent.Id;

            var streamInfo = await ivsService.TryGetLiveStream(liveContentId);
            if (string.IsNullOrEmpty(streamInfo?.StreamKey))
                return Conflict($"Cannot stream on {liveContentId}");

            await AddToRelatedContent(relatedId, liveContent.Id);
            await AddToRelatedContent(liveContent.Id, content.Id);

            return Ok(liveContent);
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "sessionId: {sessionId}, RelatedId: {RelatedId}", sessionId, relatedId);

            throw;
        }
    }

    /// <summary>
    /// Creates a IVS live for a given Chime meeting and connects the live content to the meeting.
    /// </summary>
    /// <returns></returns>
    [HttpPost("Create/{sessionId}")]
    [ProducesResponseType(200)] // Ok
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<LiveSinkInfo>> Create([FromBody] CreateLiveContentRequest createLiveContentRequest,
        [FromRoute] string sessionId)
    {
        //logger.LogInformation("sessionId: {sessionId}", sessionId);

        try
        {
            var meeting = await chimeIntegration.GetMeeting(sessionId);
            var content = await contentService.Retrieve(meeting.ContentId);
            if (content == null)
            {
                _ = chimeIntegration.DeleteMeeting(meeting.MeetingId);
                return Conflict("The content has been deleted");
            } // content no longer exists

            var request = new CreateLiveRequest
            {
                OriginalTitle = content.OriginalTitle + " Stream",
                ReleaseDate = createLiveContentRequest.ReleaseDate
            };
            var entity = mapper.Map<Content>(request.ToContentRequest(null));
            var liveContent = await integration.CreateIVSLiveContent(entity, User,
                createLiveContentRequest?.ChannelLatencyMode ?? IVSChannelLatencyMode.NORMAL,
                createLiveContentRequest?.ChannelType ?? IVSChannelType.STANDARD);
            _ = automationManagementService.OnIVSLiveCreated(liveContent, User);

            //save the sessionId in the live content properties
            liveContent.Properties ??= new Dictionary<string, string>();
            liveContent.Properties[Consts.CONTENT_SESSIONID] = sessionId;
            if (createLiveContentRequest.Properties != null)
                MergeProperties(liveContent, createLiveContentRequest.Properties);

            liveContent = await contentService.Save(liveContent, Guid.Empty, true);

            var liveContentId = liveContent.Id;

            var streamInfo = await ivsService.TryGetLiveStream(liveContentId);
            if (string.IsNullOrEmpty(streamInfo?.StreamKey))
                return Conflict($"Cannot stream on {liveContentId}");

            await AddToRelatedContent(liveContent.Id, content.Id);

            return Ok(liveContent);
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "sessionId: {MeetingId}", sessionId);

            throw;
        }
    }

    /// <summary>
    /// Calls StopStreaming on the Chime meeting and deletes the IVS live content. and sends a content update about the stopped live
    /// </summary>
    /// <param name="sessionId"></param>
    /// <param name="liveContentId"></param>
    /// <returns></returns>
    [HttpPost("Stop/{sessionId}/{liveContentId}")]
    [ProducesResponseType(204)] // NoContent
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(409)] // Conflict
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> Stop(string sessionId, Guid liveContentId)
    {
        //logger.LogInformation("MeetingId: {MeetingId}", sessionId);

        try
        {
            var meeting = await chimeIntegration.GetMeeting(sessionId);
            var content = await contentService.Retrieve(meeting?.ContentId ?? Guid.Empty);
            if (content == null)
                return BadRequest($"Object not found. sessionId Id: {sessionId}, Meeting.ContentId: {meeting.ContentId}");

            await integration.StopStream(content, liveContentId, User);

            return NoContent();
        }
        catch (Exception ex)
        {
            if (ex is ResourceNotFoundException) return Conflict(ex.Message);
            logger.LogCritical(ex, "sessionId: {sessionId}", sessionId);

            throw;
        }
    }

    private async Task TraceChimeAction(MeetingRef meeting, string description)
    {
        var sessionId = Guid.TryParse(meeting.MeetingId, out var id) ? id : (Guid?)null;
        await integration.TracelogAction(meeting.ContentId, sessionId, description);
    }

    /// <summary> Adds the <paramref name="relatedId"/> as a related content to the entity by <paramref name="contentId"/> </summary>
    private async Task AddToRelatedContent(Guid contentId, Guid relatedId)
    {
        var content = await contentService.Retrieve(contentId);
        if (content == null) return;

        await relationshipUpdate.Enqueue(new SingleRelationshipUpdate()
        {
            Action = ActionKind.Add,
            Relation = ContentRelation.Reference,
            SourceId = contentId.ToString(),
            TargetId = relatedId.ToString()
        });
    }

    private void MergeProperties(Content liveContent, Dictionary<string, string> properties)
    {
        foreach (var property in properties)
        {
            if (liveContent.Properties.TryGetValue(property.Key, out var value))
                liveContent.Properties.Remove(property.Key);

            liveContent.Properties.Add(property.Key, property.Value);
        }
    }
}