﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlueGuava.UserLog.Client;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Delivery.Models.Items;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Service.V2;
using BlueGuava.Extensions.AspNetCore.Authorization.Roles;
using BlueGuava.ItemsProcessing;
using BlueGuava.Library.Delivery.Messaging.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using LogAction = BlueGuava.UserLog.Enums.Action;
using LogComponent = BlueGuava.UserLog.Enums.Component;
using BlueGuava.ContentManagement.Common.Services.V2;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

/// <summary>
/// Provides endpoints for maintenance tasks related to content management.
/// </summary>
[ApiController]
[ApiVersion("4.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize(Policy = Policies.WriteAndChange)]
public class MaintenanceController : ControllerBase
{
    private readonly ILogger<MaintenanceController> logger;
    private readonly IUserLogMessagingService userLogMessagingService;

    /// <inheritdoc />
    public MaintenanceController(
        ILogger<MaintenanceController> logger,
        IUserLogMessagingService userLogMessagingService
    )
    {
        this.logger = logger;
        this.userLogMessagingService = userLogMessagingService;
    }

    /// <summary>
    /// Triggers a background task to migrate chapters from the old format to the new format. <br/>
    /// this is a temporary endpoint that will be removed once the migration is complete.
    /// </summary>
    /// <param name="reindexCommandQueue"></param>
    /// <returns></returns>
    [HttpPut("MigrateChapters")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> MigrateChapters([FromServices] IItemsQueue<ReindexCommand> reindexCommandQueue)
    {
        logger.LogInformation("Called MigrateChapters");

        try
        {
            await reindexCommandQueue.EnqueueWorkItem(new ReindexCommand(true));
            _ = EnqueueLogEntry(LogAction.Update, null, null, "MigrateChapters called");
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "MigrateChapters failed");

            throw;
        }
    }

    /// <summary>
    /// Triggers a background task to calculate the labels of contents and create the entity.json file in s3 bucket.
    /// </summary>
    /// <param name="reindexCommandQueue"></param>
    /// <returns></returns>
    [HttpPut("CalculateLabels")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> CalculateLabels([FromServices] IItemsQueue<ReindexCommand> reindexCommandQueue)
    {
        logger.LogInformation("Called CalculateLabels");

        try
        {
            await reindexCommandQueue.EnqueueWorkItem(new ReindexCommand(false, true));
            _ = EnqueueLogEntry(LogAction.Update, null, null, "CalculateLabels called");
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "CalculateLabels failed");

            throw;
        }
    }

    [HttpPost("TriggerChildRecalculate")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult> TriggerChildrenRecalculate(
        [FromServices] IItemsQueue<SmartCatalogItem> smartCatalogProcessor,
        [FromBody] ObjectReleaseMessage item)
    {
        await smartCatalogProcessor.EnqueueWorkItem(new SmartCatalogItem(item.Payload));

        return Ok();
    }

    [HttpPut("Reindex")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> Reindex([FromServices] ISearchAdapter searchAdapter,
        [FromServices] IItemsQueue<ReindexCommand> reindexCommandQueue)
    {
        logger.LogInformation("Reindex called");

        try
        {
            await searchAdapter.DeleteIndex();
            await searchAdapter.InitializeIndex();

            await reindexCommandQueue.EnqueueWorkItem(new ReindexCommand());
            _ = EnqueueLogEntry(LogAction.Update, null, null, "Search reindex called");

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Reindex Exception");
            throw;
        }
    }

    [HttpPost("Update/CDN")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> UpdateCDNUrl(string oldUrl, string newUrl, [FromServices] IContentService contentService)
    {
        logger.LogInformation("UpdateCDNUrl called");

        try
        {
            _ = Task.Run(async () =>
                {
                    await foreach (var content in contentService.RetrieveAll())
                    {
                        await contentService.UpdateAssetsCDNUrl(content, User, oldUrl, newUrl);
                    }
                });
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "UpdateCDNUrl Exception");
            throw;
        }
    }


    [HttpPost("Folder/Cleanup")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> S3CleanupAsync([FromServices] IContentService contentService)
    {
        logger.LogInformation("S3CleanupAsync called");

        try
        {
            _ = contentService.S3CleanupAsync();
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "S3CleanupAsync Exception");
            throw;
        }
    }

    [HttpPost("Folder/Cleanup/CopyObject")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> S3CopyObjectCleanupAsync([FromServices] IContentService contentService)
    {
        logger.LogInformation("S3CopyObjectCleanupAsync called");

        try
        {
            _ = contentService.S3CopyObjectCleanupAsync();
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "S3CopyObjectCleanupAsync Exception");
            throw;
        }
    }

    [HttpPost("Duration/Fix")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> ContentDurationFix([FromServices] IItemsQueue<ReindexCommand> reindexCommandQueue)
    {
        logger.LogInformation("ContentDurationFix called");

        try
        {
            await reindexCommandQueue.EnqueueWorkItem(new ReindexCommand(false, false, true));
            _ = EnqueueLogEntry(LogAction.Update, null, null, "Content Duration fix called");

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentDurationFix Exception");
            throw;
        }
    }

    [HttpPost("Package/Setup")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> ContentSKUPackageSetup(Guid authGroupId, [FromServices] IItemsQueue<ReindexCommand> reindexCommandQueue, [FromServices] IMediaHelperService mediaHelperService)
    {
        logger.LogInformation("SKUPackageSetup called");

        try
        {
            if (authGroupId == Guid.Empty) return BadRequest("Auth group id should not be null");

            var sku = await mediaHelperService.GetSku(authGroupId.ToString());

            if (sku == null) return BadRequest("Invalid auth group id");

            await reindexCommandQueue.EnqueueWorkItem(new ReindexCommand(false, false, false, true, authGroupId));
            _ = EnqueueLogEntry(LogAction.Update, null, null, "SKU Package Setup called");

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "SKUPackageSetup Exception");
            throw;
        }
    }

    [HttpPost("DeletedFiles/Cleanup")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> DeletedFilesCleanup([FromServices] IItemsQueue<ReindexCommand> reindexCommandQueue)
    {
        logger.LogInformation("DeletedFilesCleanup called");

        try
        {
            await reindexCommandQueue.EnqueueWorkItem(new ReindexCommand(false, false, false, false, null, true));
            _ = EnqueueLogEntry(LogAction.Update, null, null, "Deletec files cleanup called");

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "DeletedFilesCleanup Exception");
            throw;
        }
    }

    [HttpPost("QueryScripts")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> QueryScripts([FromServices] IContentDynamicCatalogService contentDynamicCatalogService)
    {
        logger.LogInformation("DeletedFilesCleanup called");

        try
        {
            await contentDynamicCatalogService.SyncMustacheScriptsFromFile();
            _ = EnqueueLogEntry(LogAction.Update, null, null, "Query Scripts upserted");

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Query Scripts Exception");
            throw;
        }
    }

    private async Task EnqueueLogEntry(LogAction action, Content currentContent, Content updatedContent,
        string? notes = null)
    {
        var objectId = $"{currentContent?.Id ?? updatedContent?.Id}";
        var metadata = new Dictionary<string, string>();
        if (!string.IsNullOrEmpty(notes)) metadata["Notes"] = notes;
        var empty = new Content { Id = Guid.Empty, CreatedDate = default, LastModifiedDate = default };
        await userLogMessagingService.EnqueueLogEntry(User, LogComponent.Content, action,
            objectId, currentContent ?? empty, updatedContent ?? empty, metadata);
    }
}