﻿using BlueGuava.ContentManagement.Api.Infrastructure;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
namespace BlueGuava.ContentManagement.Api.Controllers.V4;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiController]
[AllowAnonymous]
[ApiVersion("4.0")]
public class EnumsController : ControllerBase
{
    [HttpGet("Type")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]

    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetContentTypes()
    {
        return Ok(EnumHelper.FormatEnumValues<ContentType>(v => v != 0));
    }


    [HttpGet("MusimapMood")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetMusimapMoods()
    {
        return Ok(EnumHelper.FormatEnumValues<MusimapMood>(v => v != 0));
    }

    [HttpGet("Role")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetRoles()
    {
        return Ok(EnumHelper.FormatEnumValues<Role>());
    }

    [HttpGet("AssetType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetAssetTypes()
    {
        return Ok(EnumHelper.FormatEnumValues<AssetType>(v => v != 0));
    }

    [HttpGet("SubType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetAssetSubTypes()
    {
        return Ok(EnumHelper.FormatEnumValues<SubType>(v => v != 0));
    }

    [HttpGet("DesignType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetDesignTypes()
    {
        return Ok(EnumHelper.FormatEnumValues<DesignTypes>(v => v != 0));
    }

    [HttpGet("EntityType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetEntityType()
    {
        return Ok(EnumHelper.FormatEnumValues<EntityType>(v => v != 0));
    }

    [HttpGet("LabelType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetLabelType()
    {
        return Ok(EnumHelper.FormatEnumValues<LabelType>(v => v != 0));
    }


    [HttpGet("ContentNotifications")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetContentNotifications()
    {
        return Ok(EnumHelper.FormatEnumValues<ContentNotification>());
    }

    [HttpGet("VisibilityType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetVisibilityTypes()
    {
        return Ok(EnumHelper.FormatEnumValues<Visibility>());
    }

    [HttpGet("ViewConfigurations")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetViewConfigurations()
    {
        return Ok(EnumHelper.FormatEnumValues<ViewConfigurations>());
    }

    [HttpGet("ChannelLatencyMode")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetChannelLatencyMode()
    {
        return Ok(EnumHelper.FormatEnumValues<IVSChannelLatencyMode>());
    }

    [HttpGet("ChannelType")]
    [OutputCache(PolicyName = "CachePolicy", Duration = 24 * 60 * 60)]
    [ProducesResponseType(200)]
    [ProducesResponseType(500)]
    public ActionResult<object> GetChannelType()
    {
        return Ok(EnumHelper.FormatEnumValues<IVSChannelType>());
    }
}