# Features

### Ingest Asset

::: mermaid
sequenceDiagram
    autonumber
    participant C as Client Application
    participant A as API
    participant CS as Content Service
    participant R as Repository and Dynamo
    participant SQ as SQS Queue
    
    C->>A: Ingest asset
    activate C
    activate A
    
    A->>A: Request validation
    
   
    critical Request is not valid 
        rect rgb(255, 0, 0)
            A-->>C: returns 400 <br/>Bad Request
        end
    end
    
    A->>CS: Retrieve content by ContentId
        activate CS
        CS->>R: Retrieve content DTO
            activate R
            R->>R: Retrieve <br/>content Document
            R-->>R: Returns <br/>content Document
            R->>R: Map <br/>Document to Entity
        R-->>CS: Returns content Entity
    CS-->>A: Returns content Entity
    
   
    alt Entity is null
        rect rgb(255, 0, 0)
            A-->>C: returns 404 <br/>Not Found
        end
    else Entity is not null
         A->>A: Shallow copy Entity
         A->>A: Adds new Asset to<br/> Entity.Assets
            A->>CS: Update content
            CS->>R: Update content DTO
            R->>R: Execute Businness Rules
            Note right of R: See More: Feature - Content Update
            R-->>CS: Returns content Entity
            deactivate R
         CS-->>A: Returns content Entity  
         deactivate CS
         
alt Entity is null
rect rgb(255, 0, 0)
    A-->>C: returns 409 <br/>Conflict
end
else Entity is not null
   
    A->>SQ: Enqueue log <br/>for audit logging
    activate SQ
    A->>SQ: Increment <br/>prometheus metrics   
    deactivate SQ 
    
    
    A-->>C: Returns 201 <br/>ContentResponse
    deactivate A
    deactivate C
            
    end
    end
:::

### Content Update

::: mermaid
sequenceDiagram
    autonumber

    participant OT as Other Service
    participant CS as Content Service
    participant R as Repository.Dynamo Service
    participant DB as Dynamo Database
    
    OT->>CS: Update content
    activate OT
    activate CS
        CS->>R: Retrieve content DTO
        activate R
               
            R->>DB: Retrieve content Document
            activate DB
                    DB-->>R: Returns content Document
                R->>R: Map Document to Entity
            R-->>CS: Returns content Entity
    alt Entity is null
        rect rgb(255, 0, 0)
            CS-->>OT: return null
        end
    else Entity is not null
        CS->>CS: Updates Content entity
        CS->>CS: Validates Content entity if it is published
        CS->>R: Update content DTO
            R->>R: Map Entity to DTO
                R->>DB: Update content Document
                DB-->>R: Returns content Document
                deactivate DB
            R->>R: Map Document to Entity
            R-->>CS: Returns content Entity
            deactivate R
    CS-->>OT: Returns content Entity
    deactivate CS
    end
    deactivate OT
:::