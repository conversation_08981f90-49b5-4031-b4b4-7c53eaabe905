﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.SimpleSystemsManagement.Model;
using BlueGuava.UserLog.Client;
using BlueGuava.ContentManagement.Api.Models.ContentV2;
using BlueGuava.ContentManagement.Api.Queuing;
using BlueGuava.ContentManagement.Common.Entities.MediaLive;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;
using VodToLiveRequest = BlueGuava.ContentManagement.Integration.V2.VodToLiveRequest;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

[Authorize]
[ApiController]
[ApiVersion("4.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
public class VodToLiveController : ControllerBase
{
    private static readonly string ContentEndpoint = nameof(ContentController)[..^10];

    private readonly ILogger<VodToLiveController> logger;
    private readonly IContentService contentService;
    private readonly IMessageQueue<MediaLiveControlTask> mediaLiveControl;
    private readonly IMessageQueue<Job> markerJobMessageQueue;
    private readonly IUserLogMessagingService userLogMessaging;
    private readonly IMetricsCollector metricsCollector;
    //private readonly ICorrelationContextAccessor correlationContextAccessor;

    public VodToLiveController(
        ILogger<VodToLiveController> logger,
        IContentService contentService,
        IMessageQueue<MediaLiveControlTask> mediaLiveControl,
        IUserLogMessagingService userLogMessaging,
        IMetricsCollector metricsCollector,
        //ICorrelationContextAccessor correlationContextAccessor,
        IMessageQueue<Job> markerJobMessageQueue)
    {
        this.logger = logger;
        this.contentService = contentService;
        this.mediaLiveControl = mediaLiveControl;
        this.userLogMessaging = userLogMessaging;
        this.metricsCollector = metricsCollector;
        //this.correlationContextAccessor = correlationContextAccessor;
        this.markerJobMessageQueue = markerJobMessageQueue;
    }


    [HttpPost("{contentId}")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> Start(Guid contentId, [FromBody] VodToLiveRequest request)
    {
        //logger.LogInformation("ContentId: {ContentId}, Request: {@Request}", contentId, request);

        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null) return NotFound(contentId);

            var vodToLive = content.Properties?.GetValueOrDefault(Consts.CONTENT_BROADCAST_LIVEID);
            if (!string.IsNullOrEmpty(vodToLive)) return Conflict("Already scheduled");

            if (string.IsNullOrEmpty(request.ObjectUrl))
                TryFillObjectUrl(content, request);

            if (!ValidateRequest(request))
                return ValidationProblem(ModelState);

            var liveContent = new Content();
            liveContent.UpdateWith(content, User.GetCustomerId(), true);
            liveContent.Type = ContentType.LiveStream;
            liveContent.ReleaseDate = request.StartTime ?? DateTime.UtcNow;

            var live = await CreateContent(liveContent,
                c => ChangeLive(c, request, content));
            await StartMediaLiveWorkflow(live.Id, request);

            await UpdateContent(content, c => ChangeVod(c, live.Id, request.StartTime));

            await markerJobMessageQueue.Enqueue(new CopyContentMarkersJob(contentId.ToString(), live.Id));

            return CreatedAtAction(nameof(ContentController.Get), ContentEndpoint,
                new { contentId = live.Id }, ContentResponse.FromEntity(live));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}, Request: {@Request}", contentId, request);
            throw;
        }
    }

    [HttpPut("{contentId}/{liveId}")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> Start(Guid contentId, Guid liveId, [FromBody] VodToLiveRequest request)
    {
        //logger.LogInformation("ContentId: {ContentId}, LiveId: {LiveId}, Request: {@Request}", contentId,
        //    liveId, request);

        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null) return NotFound(contentId);

            var vodToLive = content.Properties?.GetValueOrDefault(Consts.CONTENT_BROADCAST_LIVEID);
            if (!string.IsNullOrEmpty(vodToLive)) return Conflict("Already scheduled");

            if (string.IsNullOrEmpty(request.ObjectUrl))
                TryFillObjectUrl(content, request);

            if (!ValidateRequest(request))
                return ValidationProblem(ModelState);

            var live = await contentService.Retrieve(liveId);
            if (live == null) return NotFound(liveId);
            if (!string.IsNullOrEmpty(live.ExternalId) || (live.Assets?.Any(a => a.Id == liveId) ?? false))
                return Conflict($"Live content already streaming ({live.ExternalId})");

            live = await UpdateContent(live, c => ChangeLive(c, request, content));
            await StartMediaLiveWorkflow(live.Id, request);

            await UpdateContent(content, c => ChangeVod(c, live.Id, request.StartTime));

            return AcceptedAtAction(nameof(ContentController.Get), ContentEndpoint,
                new { contentId = live.Id }, ContentResponse.FromEntity(live));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}, LiveId: {LiveId}, Request: {@Request}", contentId,
                liveId, request);
            throw;
        }
    }

    [HttpDelete("{liveId}")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> Stop(Guid liveId)
    {
        //logger.LogInformation("LiveId: {LiveId}", liveId);

        try
        {
            await mediaLiveControl.Enqueue(new StopStreaming { ContentId = liveId });
            return AcceptedAtAction(nameof(ContentController.Get), ContentEndpoint, new { contentId = liveId });
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "LiveId: {LiveId}", liveId);
            throw;
        }
    }


    private Task StartMediaLiveWorkflow(Guid liveId, VodToLiveRequest request)
    {
        return mediaLiveControl.Enqueue(new CreateChannel
        {
            ContentId = liveId,
            CustomerId = User.GetCustomerId(),
            ObjectUrl = request.ObjectUrl,
            Duration = request.Duration!.Value,
            StartTime = request.StartTime!.Value
        });
    }

    private void TryFillObjectUrl(Content content, VodToLiveRequest request)
    {
        var asset = content.Assets?
            .Where(a => !string.IsNullOrEmpty(a.ObjectUrl))
            .Where(a => a.SubType == SubType.Original && !a.IsDeleted)
            .OrderByDescending(a => a.Id == content.Id)
            .FirstOrDefault();

        if (string.IsNullOrEmpty(asset?.ObjectUrl) || !asset.ObjectUrl.EndsWith(".mp4"))
            ModelState.AddModelError(nameof(request.ObjectUrl), "Cannot determine media url");

        if ((asset == null || asset.Duration <= 0) && content.Duration <= 0)
            ModelState.AddModelError(nameof(request.Duration), "Cannot determine playback duration");

        request.ObjectUrl = asset?.ObjectUrl;
        request.Duration = asset?.Duration > 0 ? asset?.Duration : content.Duration;
    }

    private bool ValidateRequest(VodToLiveRequest request)
    {
        var startLimit = DateTime.UtcNow.AddMinutes(2);
        if (request.StartTime.HasValue && request.StartTime < startLimit)
            ModelState.AddModelError(nameof(request.StartTime),
                "Start time too close to present; it is recommended to plan ahead 5 minutes");

        if (string.IsNullOrEmpty(request.ObjectUrl))
            ModelState.AddModelError(nameof(request.ObjectUrl), "ObjectUrl must have a value");
        if (!(request.ObjectUrl?.EndsWith(".mp4") ?? false))
            ModelState.AddModelError(nameof(request.ObjectUrl), "ObjectUrl must be an MP4 file");

        if (request.Duration == null || request.Duration <= 0)
            ModelState.AddModelError(nameof(request.Duration), "ObjectUrl must be greater than 0");

        return ModelState.IsValid;
    }

    private async Task<Content> CreateContent(Content entity, Action<Content> setup)
    {
        var original = new Content { Id = default, CreatedDate = default, LastModifiedDate = default };

        setup(entity); // set up content
        var content = await contentService.Create(entity, User);
        if (content == null)
            throw new AlreadyExistsException(
                $"The content is already exists with the Id: {entity.Id} or ExternalId: {entity.ExternalId}");

        metricsCollector.IncrementSuccessCounter(ApiMethod.Create, content.Type);

        _ = userLogMessaging.EnqueueLogEntry(User,
            UserLog.Enums.Component.Content,
            UserLog.Enums.Action.Create,
            content.Id.ToString(), original, content);

        return content;
    }

    private async Task<Content?> UpdateContent(Content content, Action<Content> modify)
    {
        var original = content.RecursiveCopy();

        modify(content); // perform modifications
        var updated = await contentService.Update(content, User);
        if (updated == null) return null;

        metricsCollector.IncrementSuccessCounter(ApiMethod.Update, content.Type);

        _ = userLogMessaging.EnqueueLogEntry(User,
            UserLog.Enums.Component.Content,
            UserLog.Enums.Action.Update,
            content.Id.ToString(), original, updated);

        return updated;
    }

    private static void ChangeLive(Content live, VodToLiveRequest request, Content content)
    {
        live.OriginalTitle = !string.IsNullOrEmpty(request.LiveStreamTitle)
            ? request.LiveStreamTitle
            : "Streaming " + content.OriginalTitle;
        live.OriginalLanguage = content.OriginalLanguage;
        live.ExternalId = Guid.NewGuid().ToString();

        live.Properties ??= new Dictionary<string, string>();
        live.Properties[Consts.CONTENT_BROADCAST_VODID] = content.Id.ToString();
        live.Properties[Consts.CONTENT_BROADCAST_STARTTIME] = request.StartTime?.ToString("O") ?? string.Empty;
        live.Properties[Consts.CONTENT_BROADCAST_RELEASETIME] = request.StartTime?.ToString("O") ?? string.Empty;

        live.ReferenceId = content.Id.ToString();
    }

    private static void ChangeVod(Content vod, Guid liveId, DateTime? startTime)
    {
        vod.Properties ??= new Dictionary<string, string>();
        vod.Properties[Consts.CONTENT_BROADCAST_LIVEID] = liveId.ToString();
        vod.Properties[Consts.CONTENT_BROADCAST_STARTTIME] = startTime?.ToString("O") ?? string.Empty;
    }
}