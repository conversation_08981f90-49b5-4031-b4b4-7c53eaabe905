using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common.Entities.IVS;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AspNetCore.IpAddress;
using BlueGuava.ContentManagement.Service.AmazonIVS.Models;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using CorrelationId;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;

using Action = BlueGuava.UserLog.Enums.Action;
using ContentResponse = BlueGuava.ContentManagement.Api.Models.ContentV2.ContentResponse;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

/// <summary>
/// Contains methods to create, connect and close IVS chat.
/// </summary>
[ApiController]
[ApiVersion("4.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Authorize]
public class ChatController : ControllerBase
{
    private static readonly string ContentEndpoint = nameof(ContentController)[..^10];

    private readonly ILogger<ChatController> logger;
    private readonly IContentService contentService;
    private readonly IAmazonIvsService ivsService;
    private readonly IContentIntegration integration;
    private readonly IMetricsCollector metricsCollector;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdate;
    private readonly IAutomationManagementService automationManagementService;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IImageProcessorService imageProcessorService;
    private readonly IFeatureManager featureManager;
    private readonly IHttpContextAccessor httpContextAccessor;
    private string GetCallerIP() => httpContextAccessor.HttpContext?.Request.GetClientIpAddress();


    /// <summary>
    /// Initializes a new instance of the <see cref="ChatController"/> class.
    /// </summary>
    public ChatController(
        ILogger<ChatController> logger,
        IContentService contentService,
        IAmazonIvsService ivsService,
        IContentIntegration integration,
        IMetricsCollector metricsCollector,
        IMessageQueue<RelationshipUpdate> relationshipUpdate,
        IAutomationManagementService automationManagementService,
        ICorrelationContextAccessor correlationContextAccessor,
        IImageProcessorService imageProcessorService,
        IFeatureManager featureManager,
        IHttpContextAccessor httpContextAccessor
        )
    {
        this.logger = logger;
        this.contentService = contentService;
        this.ivsService = ivsService;
        this.integration = integration;
        this.metricsCollector = metricsCollector;
        this.relationshipUpdate = relationshipUpdate;
        this.correlationContextAccessor = correlationContextAccessor;
        this.automationManagementService = automationManagementService;
        this.imageProcessorService = imageProcessorService;
        this.featureManager = featureManager;
        this.httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// Creates the AWS IVS chat.
    /// </summary>
    /// <param name="relatedId">Initial source of chat creation, can be null.</param>
    /// <param name="entity">Chat creation payload.</param>
    /// <returns>Information about created chat.</returns>
    /// <exception cref="ArgumentException">If <paramref name="entity"/> payload is missed or null.</exception>
    [HttpPost("Create/{relatedId?}")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<ContentResponse>> CreateChat(Guid? relatedId, Content entity)
    {
        //logger.LogInformation("Request: {@Request}", entity);

        try
        {
            if (entity == null) throw new ArgumentException("Content cannot be null");

            SetLiveChatContent(entity, relatedId);

            var content = await integration.CreateIVSChatContent(entity, User);
            if (relatedId.HasValue) await AddToRelatedContent(relatedId.Value, content?.Id ?? Guid.Empty);

            if (await AllowAutoImageGenerationFeature())
            {
                var name = entity.Localizations?.FirstOrDefault().Value?.Name;
                if (!string.IsNullOrEmpty(name))
                    await imageProcessorService.GenerateImage(name, content?.Id.ToString());
            }

            _ = automationManagementService.OnContentCreated(content, User);

            return CreatedAtAction(nameof(ContentController.Get), ContentEndpoint,
                new { contentId = content?.Id }, ContentResponse.FromEntity(content));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Request: {@Request}", entity);
            throw;
        }
    }

    /// <summary>
    /// Connects a user to particular chat.
    /// </summary>
    /// <param name="contentId">Chat id.</param>
    /// <returns>A short-lived token enabling the user to enter to the chat room.</returns>
    [HttpPost("{contentId}/Connect")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult<RoomKeyInfo>> ConnectChat(Guid contentId)
    {
        //logger.LogInformation("ContentId: {contentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHATROOM_ARN, out var chatArn);
            if (string.IsNullOrEmpty(chatArn)) return Conflict("No Live Chat");

            var moderator = entity.OwnerId == User.GetCustomerId();
            var roomKeyInfo = await ivsService.CreateRoomKey(chatArn, User, moderator);

            _ = automationManagementService.OnChatConnect(entity.Id, User.GetCustomerId());

            return Ok(roomKeyInfo);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {contentId}", contentId);

            throw;
        }
    }

    /// <summary>
    /// Closes the chat and makes it unavailable to interact with.
    /// </summary>
    /// <param name="contentId">Chat id.</param>
    /// <returns>Action result.</returns>
    [HttpPost("{contentId}/Close")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    public async Task<ActionResult> CloseChat(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var original = entity.RecursiveCopy();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHATROOM_ARN, out var chatArn);
            if (string.IsNullOrEmpty(chatArn)) return Conflict("No Live Chat");

            await ivsService.RemoveChatRoom(chatArn);
            entity.Properties.Remove(Consts.CONTENT_CHATROOM_ARN);

            var content = await contentService.Update(entity, User);
            _ = integration.EnqueueLogEntry(Action.Update, original, content, User, "Close live chat");
            _ = integration.TracelogAction(contentId, null, "Success: Close live chat");
            try
            {
                metricsCollector.IncrementSuccessCounter(ApiMethod.Update, content.Type);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(LiveStreamController), nameof(CloseChat))
                    .Log(LogLevel.Error, ex, "IncrementSuccessCounter failed - ContentId: {ContentId}", contentId);
            }

            _ = automationManagementService.OnChatDisconnect(content.Id, User.GetCustomerId());

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {contentId}", contentId);

            throw;
        }
    }

    /// <summary> Adds the <paramref name="relatedId"/> as a related content to the entity by <paramref name="contentId"/> </summary>
    private async Task AddToRelatedContent(Guid contentId, Guid relatedId)
    {
        var content = await contentService.Retrieve(contentId);
        if (content == null) return;

        await relationshipUpdate.Enqueue(new SingleRelationshipUpdate()
        {
            Action = ActionKind.Add,
            Relation = ContentRelation.Reference,
            SourceId = contentId.ToString(),
            TargetId = relatedId.ToString()
        });
    }

    private async Task<bool> AllowAutoImageGenerationFeature()
    {
        if (await featureManager.IsEnabledAsync("AllowAutomation_AutoImageGeneration")) return true;
        //logger.LogInformation("Auto Image generation feature is off");
        return false;
    }

    private static void SetLiveChatContent(Content content, Guid? relatedId)
    {
        content.Type = ContentType.LiveChat;
        content.ReferenceId = relatedId?.ToString();
    }
    /// <summary>
    ///  Sends a message to the chat room
    /// </summary>
    /// <returns></returns>
    [HttpPost("{contentId}/Message")]
    public async Task<ActionResult> SendMessage(Guid contentId, [FromBody] IvsSendMessageRequest req)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHATROOM_ARN, out var chatArn);
            if (string.IsNullOrEmpty(chatArn)) return Conflict("No Live Chat");

            //Validation in the request model
            await ivsService.SendMessage(chatArn, User, req.Message!);

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    /// <summary>
    /// Deletes a message from the chat room
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{contentId}/Message")]
    public async Task<ActionResult> DeleteMessage(Guid contentId, [FromBody] IvsDeleteMessageRequest req)
    {

        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHATROOM_ARN, out var chatArn);
            if (string.IsNullOrEmpty(chatArn)) return Conflict("No Live Chat");
            //Validation in the request model
            await ivsService.RemoveMessage(chatArn, req.MessageId!, contentId.ToString(), GetCallerIP(), req.Reason);
            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    /// <summary>
    /// Ban a user from the chat room
    /// </summary>
    /// <returns></returns>
    [HttpPost("{contentId}/Ban")]
    public async Task<ActionResult> BanUser(Guid contentId, [FromBody] IvsBanRequest req)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            entity.Properties ??= new Dictionary<string, string>();
            entity.Properties.TryGetValue(Consts.CONTENT_CHATROOM_ARN, out var chatArn);
            if (string.IsNullOrEmpty(chatArn)) return Conflict("No Live Chat");
            //Validation in the request model
            await ivsService.BanUser(chatArn, req.CustomerId!, req.Reason ?? string.Empty);

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }
}