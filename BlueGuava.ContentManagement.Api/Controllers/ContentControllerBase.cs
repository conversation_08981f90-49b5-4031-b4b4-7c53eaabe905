using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using BlueGuava.Authorization.CloudFront;
using BlueGuava.UserLog.Client;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using BlueGuava.HttpRepository;
using BlueGuava.JwtToken;
using BlueGuava.MarkerManagement.Models.Abstraction.Enums;
using CorrelationId;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using LogAction = BlueGuava.UserLog.Enums.Action;
using LogComponent = BlueGuava.UserLog.Enums.Component;

namespace BlueGuava.ContentManagement.Api.Controllers.LegacyVersions;

/// <summary>
///
/// </summary>
public class ContentControllerBase : ControllerBase
{
    protected const string RELATION_API_ROUTE_PREFIX = "/api/v3.0/";
    protected readonly ILogger<ContentControllerBase> logger;
    protected readonly IFeatureManager featureManager;
    protected readonly IHttpRepository relationHttpRepository;
    protected readonly IConfiguration configuration;
    protected readonly ICorrelationContextAccessor correlationContextAccessor;
    protected readonly IContentService contentService;
    protected readonly IContentArchival contentArchival;
    protected readonly IUserLogMessagingService userLogMessagingService;
    protected readonly IMetricsCollector metricsCollector;
    protected readonly string cdnUrl;

    /// <inheritdoc />
    public ContentControllerBase(
        ILogger<ContentControllerBase> logger,
        IHttpRepositoryProvider httpRepositoryProvider,
        IFeatureManager featureManager,
        IConfiguration configuration,
        ICorrelationContextAccessor correlationContextAccessor,
        IContentService contentService,
        IContentArchival contentArchival,
        IUserLogMessagingService userLogMessagingService,
        IMetricsCollector metricsCollector,
        IOptionsMonitor<CdnSettings> cdnSettings
    )
    {
        this.logger = logger;
        this.featureManager = featureManager;
        this.configuration = configuration;
        this.correlationContextAccessor = correlationContextAccessor;
        this.contentService = contentService;
        this.contentArchival = contentArchival;
        this.userLogMessagingService = userLogMessagingService;
        this.metricsCollector = metricsCollector;
        relationHttpRepository = httpRepositoryProvider.CreateHttpRepository(ServiceNames.Collections);
        cdnUrl = cdnSettings.CurrentValue.DefaultUrl ?? configuration["ContentManagement:CDNURL"] ?? "";
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="captions"></param>
    /// <param name="celebs"></param>
    /// <param name="moderations"></param>
    /// <param name="faces"></param>
    /// <param name="emotions"></param>
    /// <param name="labels"></param>
    /// <param name="personTracks"></param>
    /// <param name="subtitles"></param>
    /// <param name="nft"></param>
    /// <returns></returns>
    protected virtual List<MarkerFilter> GenerateMarkerFilters(
        string[]? captions,
        string[]? celebs,
        string[]? moderations,
        string[]? faces,
        string[]? emotions,
        string[]? labels,
        string[]? personTracks,
        string[]? subtitles,
        string[]? nft
    )
    {
        var markerFilters = new List<MarkerFilter>();
        if (captions != null) AddTagFilters(MarkerType.CaptionTag, captions);
        if (celebs != null) AddTagFilters(MarkerType.CelebrityTag, celebs);
        if (moderations != null) AddTagFilters(MarkerType.ContentModerationTag, moderations);
        if (faces != null) AddTagFilters(MarkerType.FaceTag, faces);
        if (emotions != null) AddTagFilters(MarkerType.EmotionTag, emotions);
        if (labels != null) AddTagFilters(MarkerType.LabelTag, labels);
        if (personTracks != null) AddTagFilters(MarkerType.PersonTrackingTag, personTracks);
        if (subtitles != null) AddTagFilters(MarkerType.SubtitleTag, subtitles);
        if (nft != null) AddTagFilters(MarkerType.NFTTag, nft);
        return markerFilters;

        void AddTagFilters(MarkerType tagType, string[] values)
        {
            markerFilters.AddRange(
                values.Select(txt => new MarkerFilter { MarkerType = tagType, Value = HttpUtility.UrlDecode(txt) }));
        }
    }
    /*
    /// <summary>
    /// Organization filter check
    /// </summary>
    /// <param name="organizations">uses User.GetOrganizations to fill this</param>
    /// <returns>true if we need to filter the result with the organization from the token</returns>
    protected virtual bool DoesUserNeedToBeValidatedByOrganization(out List<string> organizations)
    {
        organizations = User.GetOrganizations()?.ToList() ?? new List<string>();
        return !User.IsGlobalAdmin() && !User.IsTechnicalUser() && !User.IsInRole(UserRole.Support.ToString());
    }
    */

    /// <summary>
    /// Retrieves the organization Relations list of the content
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    protected virtual async Task<Pagination<Relation>?> RetrieveOrganizationsByContentId(string contentId,
        CancellationToken cancellation = default)
    {
        var uri = $"{RELATION_API_ROUTE_PREFIX}/{contentId}/Content/Organization";
        return await relationHttpRepository.RetrieveAsync<Pagination<Relation>>(uri, null, cancellation);
    }

    /*
    /// <summary>
    /// Determines that the user is authorized to access to the resource
    /// </summary>
    /// <param name="contentId"></param>
    /// <returns>true if the user is authorized</returns>
    protected virtual async Task<bool> AuthorizedByOrganization(Guid contentId)
    {
        if (!await featureManager.IsEnabledAsync("ContentSearch_AllowOrganizationByToken")) return true;

        if (DoesUserNeedToBeValidatedByOrganization(out var organizations))
        {
            //if we need to validate the user but the user does not have any organization, we return false
            if (!organizations.Any()) return false;

            var currentOrganizations = await RetrieveOrganizationsByContentId(contentId.ToString());
            if (!currentOrganizations?.PageContent?.Any(x => organizations?.Contains(x.TargetId) ?? false) ??
                false) return false;
        }

        return true;
    }*/

    /*
    /// <summary>
    /// Organization filter apply if the user is not ADMIN AND the feature is turned on <br/>
    /// <b> Feature switch: </b> <code> ContentSearch_AllowOrganizationByToken </code>
    /// </summary>
    protected virtual async Task ApplyOrganizationFilterFromToken(ContentSearch searchArgs)
    {
        if (!await featureManager.IsEnabledAsync("ContentSearch_AllowOrganizationByToken"))
            return;

        if (!User.IsGlobalAdmin() && !User.IsTechnicalUser() && !User.IsInRole(UserRole.Support.ToString()))
        {
            var orgsFromToken = User.GetOrganizations()?.ToList();

            if (orgsFromToken == null || orgsFromToken.Count == 0)
                throw new UnauthorizedAccessException("You don't have access to any organization");

            var orgs = orgsFromToken.Intersect(searchArgs?.OrganizationFilters!).ToList();

            if (orgs.Count > 0 && searchArgs?.OrganizationFilters?.Count() > 0)
                searchArgs.OrganizationFilters = orgs;
            else
                searchArgs.OrganizationFilters = orgsFromToken;
        }
    }*/

    /// <summary>
    /// Converts the content to the response object
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    protected virtual ContentResponse ConvertResult(Content content)
    {
        var result = Models.ContentV2.ContentResponse.FromEntity(content);
        if (result == null) return result;

        result.Assets ??= new List<Asset>();
        var svcRoot = configuration["Console:Endpoints:Content"];
        foreach (var asset in result.Assets)
        {
            asset.DownloadUrl = $"{svcRoot}/{result.Id}/Asset/{asset.Id}/Download";
            asset.TokenizedCdnUrl = GetTokenizedCdnUrl(asset, content.Id);
        }

        var secret = configuration["ContentToken:Secret"];
        result.ContentToken = content.GetToken().Encode(secret);

        return result;
    }

    protected virtual async Task<ActionResult> Delete(Guid contentId)
    {
        if (contentId == Guid.Empty) return BadRequest();

        var content = await contentService.Retrieve(contentId);
        if (content == null) return NotFound(); // no such content

        //        var allowDeleteAnyRelationship = await featureManager.IsEnabledAsync("Allow_Delete_Any_Relationship");
        //        if (!allowDeleteAnyRelationship)
        //        {
        if (!User.IsEditor() && !User.IsProjectPerson() && User.GetCustomerId() != content.OwnerId)
            return Forbid();
        //       }
        var isArchived = await contentArchival.ArchiveAllAsset(content, User);
        if (!isArchived) return BadRequest();

        var result = await contentService.Remove(contentId, User);
        if (!result) return NotFound(); // no such content

        _ = EnqueueLogEntry(LogAction.Delete, content, null);

        try
        {
            IncrementMetricsCounters(content, null);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentControllerBase), nameof(Delete))
                .Log(LogLevel.Error, ex, "IncrementMetrics failed - ContentId: {ContentId}", contentId);
        }

        return Ok("Deleted");
    }

    protected async Task EnqueueLogEntry(LogAction action, Content currentContent, Content updatedContent,
        string? notes = null)
    {
        var objectId = $"{currentContent?.Id ?? updatedContent?.Id}";
        var metadata = new Dictionary<string, string>();
        if (!string.IsNullOrEmpty(notes)) metadata["Notes"] = notes;
        var empty = new Content { Id = Guid.Empty, CreatedDate = default, LastModifiedDate = default };
        await userLogMessagingService.EnqueueLogEntry(User, LogComponent.Content, action,
            objectId, currentContent ?? empty, updatedContent ?? empty, metadata);
    }

    protected void IncrementMetricsCounters(Content oldContent, Content newContent,
        ApiMethod createMethod = ApiMethod.Create)
    {
        var contentType = newContent?.Type ?? oldContent?.Type ?? ContentType.None;
        if (oldContent == null && newContent == null) return;
        else if (oldContent == null) // newContent != null
            metricsCollector.IncrementSuccessCounter(createMethod, contentType);
        else if (newContent == null) // oldContent != null
            metricsCollector.IncrementSuccessCounter(ApiMethod.Delete, contentType);
        else // oldContent != null && newContent != null
            metricsCollector.IncrementSuccessCounter(ApiMethod.Update, contentType);

        var wasPublished = oldContent?.Published ?? false;
        var nowPublished = newContent?.Published ?? false;
        if (wasPublished == nowPublished) return; // published flag did not change
        if (nowPublished) metricsCollector.IncrementSuccessCounter(ApiMethod.Publish, contentType);
        else /* was */ metricsCollector.IncrementSuccessCounter(ApiMethod.Unpublish, contentType);
    }

    protected string? GetTokenizedCdnUrl(Asset asset, Guid contentId)
    {
        if (string.IsNullOrEmpty(cdnUrl))
            return null;

        if (!asset.PublicUrl?.Contains(cdnUrl) ?? true)
            return null;

        var result = asset.PublicUrl!;

        if (featureManager.IsEnabledAsync("AllowContentToken").GetAwaiter().GetResult())
        {
            var sessionId = Guid.NewGuid();
            var suffix =
                $"?token={CloudFrontToken.Encode(User.GetCustomerId(), sessionId, contentId, DateTime.UtcNow.AddHours(6))}";
            result += suffix;
        }

        return result;
    }
}