﻿using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using Microsoft.Extensions.Hosting;

namespace BlueGuava.ContentManagement.Api.Monitoring;

public class MetricsInitializer : IHostedService
{
    private readonly IMetricsCollector metrics;

    public MetricsInitializer(IMetricsCollector metrics)
    {
        this.metrics = metrics;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        metrics.InitializeCounters();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}