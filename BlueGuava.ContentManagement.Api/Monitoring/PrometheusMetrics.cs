﻿using System;
using System.Linq;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Prometheus;

namespace BlueGuava.ContentManagement.Api.Monitoring;

public class PrometheusMetrics : IMetricsCollector
{
    private static readonly Counter ReceivedSqsMessageFrom = Metrics.CreateCounter(
        "ent_received_sqs_message_from_total", "Number of SQS message received from sender",
        new CounterConfiguration { LabelNames = new[] { "sender", "processor" } });

    private static readonly Counter Upload_Total = Metrics.CreateCounter("ent_content_upload_total",
        "Number of Uploads", new CounterConfiguration { LabelNames = new[] { "content_type" } });

    private static readonly Counter Create_Total = Metrics.CreateCounter("ent_content_create_total",
        "Number of Created", new CounterConfiguration { LabelNames = new[] { "content_type" } });

    private static readonly Counter Update_Total = Metrics.CreateCounter("ent_content_update_total",
        "Number of Updates", new CounterConfiguration { LabelNames = new[] { "content_type" } });

    private static readonly Counter Delete_Total = Metrics.CreateCounter("ent_content_delete_total",
        "Number of Deletes", new CounterConfiguration { LabelNames = new[] { "content_type" } });

    private static readonly Counter Publish_Total = Metrics.CreateCounter("ent_content_publish_total",
        "Number of Published", new CounterConfiguration { LabelNames = new[] { "content_type" } });

    private static readonly Counter Unpublish_Total = Metrics.CreateCounter("ent_content_unpublish_total",
        "Number of Unpublishes", new CounterConfiguration { LabelNames = new[] { "content_type" } });

    private readonly ILogger<PrometheusMetrics> logger;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public PrometheusMetrics(ILogger<PrometheusMetrics> logger, ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
    }

    public void InitializeCounters()
    {
        foreach (var category in Enum.GetValues(typeof(ContentType)).OfType<ContentType>())
        {
            Upload_Total.WithLabels(category.ToString().ToLower()).Publish();
            Create_Total.WithLabels(category.ToString().ToLower()).Publish();
            Update_Total.WithLabels(category.ToString().ToLower()).Publish();
            Delete_Total.WithLabels(category.ToString().ToLower()).Publish();
            Publish_Total.WithLabels(category.ToString().ToLower()).Publish();
            Unpublish_Total.WithLabels(category.ToString().ToLower()).Publish();
        }

        ReceivedSqsMessageFrom.WithLabels("unknown", "unknown").Publish();
    }

    /// <summary>
    /// Increments success counter
    /// </summary>
    /// <param name="method"></param>
    /// <param name="contentType"></param>
    public void IncrementSuccessCounter(ApiMethod method, ContentType contentType)
    {
        IncrementCounter(GetSuccessCounter(method), contentType.ToString().ToLower());
    }

    /// <summary>
    /// Increments error counter
    /// </summary>
    /// <param name="method"></param>
    /// <param name="contentType"></param>
    /// <param name="category"></param>
    public void IncrementErrorCounter(ApiMethod method, ContentType contentType,
        ErrorCategory category = ErrorCategory.Soft)
    {
        IncrementCounter(GetErrorCounter(method), contentType.ToString().ToLower(), category.ToString().ToLower());
    }


    private void IncrementCounter(Counter counter, params string[] labels)
    {
        //if (logger.IsEnabled(LogLevel.Debug))
        //    logger.Standards(correlationContextAccessor, nameof(PrometheusMetrics), nameof(IncrementCounter))
        //        .Log(LogLevel.Debug, "Counter: {Counter}, Labels: {Labels}", counter.Name, Format(labels));

        try
        {
            //logger.LogInformation("In IncrementCounter -> Counter: {Counter}, Labels: {Labels}", counter.Name, Format(labels));
            if (labels?.Length > 0)
            {
                labels = labels.Select(x =>
                {
                    if (string.IsNullOrEmpty(x)) x = "unknown";
                    return x.ToLower();
                }).ToArray();
                counter.Labels(labels).Inc();
            }
            else
            {
                counter.Inc();
            }
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(PrometheusMetrics), nameof(IncrementCounter))
                .Log(LogLevel.Error, ex, "Counter: {Counter}, Labels: {Labels}", counter.Name, Format(labels));
        }
    }

    /// <summary>
    /// Increments SQS sender counter
    /// </summary>
    public void IncrementSqsMessageFromCounter(params string[] labels)
    {
        var labelList = labels.ToList();
        while (labelList.Count < 2)
        {
            labelList.Add("unknown");
        }
        IncrementCounter(ReceivedSqsMessageFrom, labelList.ToArray());
    }

    private static Counter GetSuccessCounter(ApiMethod method)
    {
        return method switch
        {
            ApiMethod.Upload => Upload_Total,
            ApiMethod.Create => Create_Total,
            ApiMethod.Update => Update_Total,
            ApiMethod.Delete => Delete_Total,
            ApiMethod.Publish => Publish_Total,
            ApiMethod.Unpublish => Unpublish_Total,
            _ => throw new NotSupportedException("Invalid API method: " + method)
        };
    }

    private static Counter GetErrorCounter(ApiMethod method)
    {
        return method switch
        {
            _ => throw new NotSupportedException("Invalid API method: " + method)
        };
    }

    private static string? Format(string[]? labels)
    {
        if (labels == null) return null;
        if (labels.Length == 0) return "[]";
        return "[ \"" + string.Join("\", \"", labels) + "\" ]";
    }
}