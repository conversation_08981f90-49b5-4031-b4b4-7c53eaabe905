﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing.Worker;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

public class ContentIngestProcessor : IMessageItemProcessor<ContentIngestMessage>
{
    //private readonly ClaimsPrincipal? TechnicalUser;
    private readonly ILogger<ContentIngestProcessor> logger;
    private readonly IContentIngestService contentIngestService;
    private readonly IJwtTokenValidator tokenValidator;
    private readonly IMetricsCollector metricsCollector;

    public ContentIngestProcessor(
        IContentIngestService contentIngestService,
        ILogger<ContentIngestProcessor> logger,
        //IConfiguration configuration,
        IJwtTokenValidator tokenValidator,
        IMetricsCollector metricsCollector)
    {
        this.contentIngestService = contentIngestService;
        this.logger = logger;
        this.tokenValidator = tokenValidator;
        this.metricsCollector = metricsCollector;
    }


    public async Task<int> ProcessItem(ContentIngestMessage item, CancellationToken cancel)
    {
        try
        {
            using (logger.BeginScope(new Dictionary<string, object>
            {
                ["CorrelationId"] = Guid.NewGuid().ToString(),
                ["ContentId"] = item.ContentId ?? Guid.Empty,
                ["ContentType"] = item.Type ?? ContentType.None,
                ["UserData"] = item.UserData ?? string.Empty,
                ["WorkflowId"] = item.WorkflowId ?? Guid.Empty,
                ["CustomerId"] = item.OwnerId ?? Guid.Empty
            }))
            {
                metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(), nameof(ContentIngestProcessor));
                var userData = tokenValidator.GetPrincipalFromToken(item.UserData ?? string.Empty);

                //TODO: get user from payload
                await contentIngestService.Ingest(item, userData);
            }
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Error processing content ingest message");
        }
        return 0;
    }
}