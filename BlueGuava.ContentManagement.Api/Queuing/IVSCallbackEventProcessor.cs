﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Service.AmazonIVS.EventBridge;
using BlueGuava.JobManagement.Common;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.MessageQueuing;
using BlueGuava.MessageQueuing.Worker;
using Microsoft.Extensions.Logging;
using Consts = BlueGuava.ContentManagement.Integration.V2.Consts;
using BlueGuava.NotificationService.Client;
using BlueGuava.MessageCenter.Common;
using System.Web;

namespace BlueGuava.ContentManagement.Api.Queuing;

internal class IvsCallbackEventProcessor : IMessageItemProcessor<IvsCallbackEvent>
{
    private readonly ILogger<IvsCallbackEventProcessor> logger;
    private readonly IAmazonIvsService ivsService;
    private readonly IContentService contentService;
    private readonly IMessageQueue<JobRequest> jobManagerQueue;
    private readonly IMetricsCollector metricsCollector;
    private readonly INotificationService notificationService;

    public IvsCallbackEventProcessor(
        ILogger<IvsCallbackEventProcessor> logger,
        IAmazonIvsService ivsService,
        IContentService contentService,
        IMessageQueue<JobRequest> jobManagerQueue,
        IMetricsCollector metricsCollector,
        INotificationService notificationService)
    {
        this.logger = logger;
        this.ivsService = ivsService;
        this.contentService = contentService;
        this.jobManagerQueue = jobManagerQueue;
        this.metricsCollector = metricsCollector;
        this.notificationService = notificationService;
    }


    public async Task<int> ProcessItem(IvsCallbackEvent item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid().ToString(),
            ["IvsCallbackEventId"] = item.Id,
            ["DetailType"] = item.DetailType ?? string.Empty,
            ["ProjectId"] = item?.Resources.ToJson() ?? string.Empty,
            ["TimeFrame"] = item?.Detail.ToJson() ?? string.Empty
        }))
        {
            metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower() ?? "IvsCallbackEvent", nameof(IvsCallbackEventProcessor));
            switch (item.Detail?.EventName)
            {
                case IvsEventNames.RecordingEnd:
                    await HandleRecordingEnd(item);
                    break;
                case IvsEventNames.StreamEnd:
                    await HandleStreamEnd(item);
                    break;
                case IvsEventNames.ChatMessage:
                    await HandleSendMessage(item);
                    break;
            };
        }
        return 0;
    }

    private async Task HandleRecordingEnd(IvsCallbackEvent item)
    {
        var contentId = Guid.Parse(item.Detail.ChannelName);
        var content = await contentService.Retrieve(contentId);
        if (content == null) throw new InvalidOperationException("Content not exists");

        await jobManagerQueue.Enqueue(new IvsImportJobRequest
        {
            OwnerId = content.OwnerId,
            ReferenceObjectId = content.Id.ToString(),
            ReferenceObjectName = content.OriginalTitle,
            Name = $"{content.OriginalTitle} IVS Import {item.EventTime}",
            S3ArchiveUrl = $"s3://{item.Detail.S3BucketName}/{item.Detail.S3KeyPrefix}/"
        });
    }

    private async Task HandleSendMessage(IvsCallbackEvent item)
    {
        logger.LogInformation("HandleSendMessage {@Item}", item);

        var contentId = Guid.Parse(item.Detail.ChannelName);
        var content = await contentService.Retrieve(contentId);
        logger.LogInformation("HandleSendMessage Retrieve ContentId: {ContentId}", contentId);

        if (content == null) throw new InvalidOperationException("Content not exists");

        if (content.AllowEmailNotification != true)
        {
            logger.LogInformation("HandleSendMessage AllowEmailNotification = false. ContentId: {ContentId}", contentId);
            return;
        }

        logger.LogInformation("HandleSendMessage AllowEmailNotification = true. ContentId: {ContentId}", contentId);

        var message = new
        {
            senderId = content.OwnerId.ToString(),
            message = HttpUtility.JavaScriptStringEncode(item.Resources?.FirstOrDefault() ?? ""),
            chatRoomName = item.Detail.ChannelName,
            title = "object name",
            link = "from the original message",
            body = "original message",
        };
        logger.LogInformation("HandleSendMessage notificationService.Trigger is ready. Message: {@Message}", message);

        await notificationService.Trigger(EventName.UserChatMessageNotification, content.OwnerId, message);

        logger.LogInformation("HandleSendMessage notificationService.Trigger is completed.");
    }

    private async Task HandleStreamEnd(IvsCallbackEvent item)
    {
        var channelArn = item.Resources.Single();
        var contentId = Guid.Parse(item.Detail.ChannelName);
        var content = await contentService.Retrieve(contentId);

        // if the channel should not be removed, then channelRef == channelArn
        var channelRef = content?.Properties?.GetValueOrDefault(Consts.CONTENT_CHANNEL_ARN);
        // if the streaming key should not be removed, then a reference to it should exist
        var streamKey = content?.Properties?.GetValueOrDefault(Consts.CONTENT_STREAMING_ARN);

        if (string.IsNullOrEmpty(channelRef))
            await ivsService.RemoveLiveStream(channelArn);
        else if (string.IsNullOrEmpty(streamKey))
            await ivsService.RemoveStreamKeys(channelArn);
    }
}