﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.MediaLive;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Tracewind.Services;
using BlueGuava.MessageQueuing;
using BlueGuava.MessageQueuing.Worker;
using BlueGuava.Tracewind.Common.Models;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

public class MediaLiveStreamJobProcessor : IMessageItemProcessor<MediaLiveStreamJob>
{
    private readonly ILogger<MediaLiveStreamJobProcessor> logger;
    private readonly IMediaLiveControl mediaLive;
    private readonly IMessageQueue<JobUpdate> jobUpdates;
    private readonly ITracewindService tracewind;
    private readonly IMetricsCollector metricsCollector;

    public MediaLiveStreamJobProcessor(
        ILogger<MediaLiveStreamJobProcessor> logger,
        IMediaLiveControl mediaLive,
        IMessageQueue<JobUpdate> jobUpdates,
        ITracewindService tracewind,
        IMetricsCollector metricsCollector)
    {
        this.logger = logger;
        this.mediaLive = mediaLive;
        this.jobUpdates = jobUpdates;
        this.tracewind = tracewind;
        this.metricsCollector = metricsCollector;
    }


    public async Task<int> ProcessItem(MediaLiveStreamJob item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = item.CorrelationId,
            ["StartTime"] = item.StartTime ?? DateTime.MinValue,
            ["ContentId"] = item.ReferenceObjectId ?? Guid.Empty.ToString(),
            ["JobId"] = item.Id,
            ["JobType"] = item.Type,
            ["Action"] = item.Action,
            ["WorkflowId"] = item.WorkflowId,
            ["UseCaseId"] = item.UseCaseId,
            ["ReScheduled"] = item.ReScheduled
        }))
        {
            try
            {
                metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(),
                    nameof(MediaLiveStreamJobProcessor));
                var command = CreateCommand(item);
                await mediaLive.Execute(command);

                var message = $"The {item.Action} command was successfully executed";
                await jobUpdates.Enqueue(new JobUpdate
                {
                    Id = item.Id,
                    Type = item.Type,
                    ReferenceObjectId = item.ReferenceObjectId,
                    Status = JobStatus.FAILED,
                    StatusMessage = message
                });
                _ = tracewind.ToSuccessTraceAsync(item, ObjectType.Content, 1, message);
            }
            catch (Exception ex)
            {
                await jobUpdates.Enqueue(new JobUpdate
                {
                    Id = item.Id,
                    Type = item.Type,
                    ReferenceObjectId = item.ReferenceObjectId,
                    Status = JobStatus.FAILED,
                    StatusMessage = $"Failed to stop channel: {ex.Message}"
                });
                _ = tracewind.ToErrorTraceAsync(item, ObjectType.Content, 1, ex);
            }
        }
        return 0;
    }

    private static MediaLiveCommand CreateCommand(MediaLiveStreamJob item)
    {
        return item.Action switch
        {
            StreamAction.Start => new StartStreaming
            { ContentId = Guid.Parse(item.ReferenceObjectId), StartTime = item.StartTime!.Value },
            StreamAction.Stop => new StopStreaming { ContentId = Guid.Parse(item.ReferenceObjectId) },
            _ => throw new NotSupportedException("Unknwn stream action: " + item.Action)
        };
    }
}