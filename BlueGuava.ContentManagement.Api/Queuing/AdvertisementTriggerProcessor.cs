using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Api.Infrastructure;
using BlueGuava.ContentManagement.Api.Models;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.Library.Common.Enums;
using BlueGuava.MessageQueuing.Worker;
using BlueGuava.OrderedSearchResult;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

/// <summary>
///
/// </summary>
public class AdvertisementTriggerProcessor : IMessageItemProcessor<AdvertismentTrigger>
{
    private readonly ILogger<AdvertisementTriggerProcessor> logger;
    private readonly IOpenSearchService openSearchService;
    private readonly IContentService contentService;
    private readonly IMessagingService messagingService;
    private readonly IMetricsCollector metricsCollector;

    /// <summary>
    /// This is a processor for the <see cref="AdvertismentTrigger"/> <br/>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="openSearchService"></param>
    /// <param name="contentService"></param>
    /// <param name="messagingService"></param>
    /// <param name="metricsCollector"></param>
    public AdvertisementTriggerProcessor(
        ILogger<AdvertisementTriggerProcessor> logger,
        IOpenSearchService openSearchService,
        IContentService contentService,
        IMessagingService messagingService,
        IMetricsCollector metricsCollector
    )
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.contentService = contentService;
        this.messagingService = messagingService;
        this.metricsCollector = metricsCollector;
    }

    /// <inheritdoc />
    public async Task<int> ProcessItem(AdvertismentTrigger item, CancellationToken cancel)
    {
        try
        {
            using (logger.BeginScope(new Dictionary<string, object>
            {
                ["CorrelationId"] = Guid.NewGuid().ToString()
            }))
            {
                metricsCollector.IncrementSqsMessageFromCounter(nameof(AdvertisementTriggerProcessor).ToLower(),
                    "EventBridge.AdvertisementTrigger");
                await SendAdvertisement(cancel);
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error while processing {Name} with id {Id}", nameof(AdvertismentTrigger), item.Id);
        }
        return 0;
    }

    /// <summary>
    /// Searches for Live chat rooms and sends an advertisement to the IVS channel
    /// </summary>
    /// <param name="cancel"></param>
    private async Task SendAdvertisement(CancellationToken cancel)
    {
        var decisionRules = new DecisionRules();
        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Type",
            PropertyValue = ContentType.LiveChat.ToString(),
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });

        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Visibility",
            PropertyValue = Visibility.Public.ToString(),
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });

        var openChatRooms =
            await GetAllPages(async pageIndex =>
                (await openSearchService.Search(null, decisionRules, null, 10, pageIndex, cancel)).Hits);

        openChatRooms = openChatRooms.Where(x =>
            x.Properties != null &&
            (x.Properties?.ContainsKey(Consts.CONTENT_CHATROOM_ARN) ?? false) &&
            !string.IsNullOrEmpty(x.Properties[Consts.CONTENT_CHATROOM_ARN])
        ).ToList();

        if (!openChatRooms.Any()) return;

        // trigger the announcement for all chat rooms
        var tasks = openChatRooms.Select(content =>
            messagingService.EventAdvertisement(content.Properties?[Consts.CONTENT_CHATROOM_ARN]!, content)).ToList();
        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// Paging through the search results with memory limits
    /// </summary>
    /// <param name="searchMethod"></param>
    /// <param name="maxItems">Maximum number of items to prevent memory issues (default: 10,000)</param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private static async Task<List<T>> GetAllPages<T>(Func<int, Task<SearchResult<T>?>> searchMethod, int maxItems = 10_000)
    {
        List<T> allData = new();
        var currentPageIndex = 0;
        var totalPageCount = -1; // Set to -1 to ensure we initialize it on the first call.

        while (totalPageCount == -1 || currentPageIndex < totalPageCount)
        {
            var result = await searchMethod(currentPageIndex);
            if (!result.Data.Any())
                // No more data, break out of the loop.
                break;

            // Check memory limits before adding more data
            if (allData.Count + result.Data.Count() > maxItems)
            {
                // Log warning about truncation (logger not available in static method, so skip logging)
                var remainingCapacity = maxItems - allData.Count;
                if (remainingCapacity > 0)
                {
                    allData.AddRange(result.Data.Take(remainingCapacity));
                }
                break;
            }

            allData.AddRange(result.Data);

            if (totalPageCount == -1)
                // Set totalPageCount on the first call.
                totalPageCount = (int)Math.Ceiling((double)result.TotalCount / result.PageSize);

            currentPageIndex++;
        }

        return allData;
    }
}