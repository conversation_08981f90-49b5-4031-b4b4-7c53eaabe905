﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.MediaLive;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.Logging;
using BlueGuava.MessageQueuing;
using BlueGuava.MessageQueuing.Worker;
using BlueGuava.Tracewind.Common.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueGuava.ContentManagement.Api.Queuing;

public class MediaLiveControlTaskProcessor : IMessageItemProcessor<MediaLiveControlTask>
{
    private readonly ILogger<MediaLiveControlTaskProcessor> logger;
    private readonly IMediaLiveControl mediaLive;
    private readonly IMessageQueue<TraceLogMessage> tracelog;
    private readonly IMetricsCollector metricsCollector;
    private readonly ModuleInfo moduleInfo;

    public MediaLiveControlTaskProcessor(
        ILogger<MediaLiveControlTaskProcessor> logger,
        IMediaLiveControl mediaLive,
        IMessageQueue<TraceLogMessage> tracelog,
        IOptions<ModuleInfo> moduleInfo,
        IMetricsCollector metricsCollector)
    {
        this.logger = logger;
        this.mediaLive = mediaLive;
        this.tracelog = tracelog;
        this.metricsCollector = metricsCollector;
        this.moduleInfo = moduleInfo.Value;
    }


    public async Task<int> ProcessItem(MediaLiveControlTask item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid().ToString(),
            ["MediaLiveControlTaskId"] = item.Id,
            ["Command"] = item.Command?.ToJson() ?? string.Empty
        }))
        {
            metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(),
                nameof(MediaLiveControlTaskProcessor));
            var type = item.Command.GetType().Name;
            var contentId = item.Command.ContentId;

            try
            {
                await mediaLive.Execute(item.Command);
                _ = TracelogAction(contentId, $"Successfully executed {type}");
            }
            catch (Exception ex)
            {
                _ = TracelogAction(contentId, $"The command {type} failed: {ex.ToStringDemystified()}");
                throw;
            }
        }
        return 0;
    }


    public async Task TracelogAction(Guid contentId, string description)
    {
        await tracelog.Enqueue(new TraceLogMessage
        {
            ObjectId = contentId.ToString(),
            ObjectType = (int)ObjectType.Content,
            CorrelationId = contentId.ToString(),
            Description = description,
            CreatedDate = DateTime.UtcNow,
            IpAddress = Environment.MachineName,
            Source = moduleInfo.Name,
            SourceVersion = moduleInfo.Version
        });
    }
}

public class MediaLiveControlTask : IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public MediaLiveCommand? Command { get; set; }

    string? IQueueItem.GroupId
    {
        get => Command?.ContentId.ToString();
        set => _ = value;
    }

    string? IQueueItem.DeduplicationId
    {
        get => Id;
        set => _ = value;
    }

    public string? Sender { get; set; }
    string? IQueueItem.Receipt { get; set; }
}

public static class MessageQueueExtension
{
    /// <summary> Enqueues a <see cref="MediaLiveControlTask"/> <paramref name="command"/> for immediate processing </summary>
    public static Task Enqueue(this IMessageQueue<MediaLiveControlTask> queue, MediaLiveCommand command)
    {
        return queue.Enqueue(new MediaLiveControlTask { Command = command });
    }

    /// <summary> Enqueues a <see cref="MediaLiveControlTask"/> <paramref name="command"/> for processing with a 30 seconds delay </summary>
    public static Task EnqueueDelayed(this IMessageQueue<MediaLiveControlTask> queue, MediaLiveCommand command)
    {
        return queue.Enqueue(new MediaLiveControlTask { Command = command }, 30);
    }
}