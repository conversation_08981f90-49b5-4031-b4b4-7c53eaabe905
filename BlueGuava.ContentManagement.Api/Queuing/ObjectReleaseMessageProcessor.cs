﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Delivery.Models.Items;
using BlueGuava.Extensions.Logging;
using BlueGuava.ItemsProcessing;
using BlueGuava.Library;
using BlueGuava.Library.Delivery.Messaging.Models;
using BlueGuava.MessageQueuing.Worker;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

public class ObjectReleaseMessageProcessor : IMessageItemProcessor<ObjectReleaseMessage>
{
    private readonly ILogger<ObjectReleaseMessageProcessor> logger;
    private readonly IItemProcessor<ManualCatalogItem> manualCatalogProcessor;
    private readonly IItemProcessor<SmartCatalogItem> smartCatalogProcessor;
    private readonly IMetricsCollector metricsCollector;

    public ObjectReleaseMessageProcessor(
        ILogger<ObjectReleaseMessageProcessor> logger,
        IItemProcessor<ManualCatalogItem> manualCatalogProcessor,
        IItemProcessor<SmartCatalogItem> smartCatalogProcessor,
        IMetricsCollector metricsCollector)
    {
        this.logger = logger;
        this.manualCatalogProcessor = manualCatalogProcessor;
        this.smartCatalogProcessor = smartCatalogProcessor;
        this.metricsCollector = metricsCollector;
    }


    public async Task<int> ProcessItem(ObjectReleaseMessage item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid(),
            ["Status"] = item.Payload.Status,
            ["Version"] = item.Payload.Version ?? string.Empty,
            ["CatalogId"] = item.Payload.GetId()
        }))
        {
            try
            {
                metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(),
                    nameof(ObjectReleaseMessageProcessor));
                var props = item.Payload.Properties!.Where(x => !string.IsNullOrEmpty(x.Name));
                switch (props.First(x => x.Name!.Equals("Content:Type"))!.Value)
                {
                    case "Catalog":
                        if (props.First(x => x.Name.Equals(Constants.CONTENT_KIND)).Value.Equals("Manual"))
                            await manualCatalogProcessor.Process(
                                new WorkItemContext<ManualCatalogItem>(new ManualCatalogItem(item.Payload)));
                        else
                            await smartCatalogProcessor.Process(
                                new WorkItemContext<SmartCatalogItem>(new SmartCatalogItem(item.Payload)));

                        break;

                    default:
                        throw new NotSupportedException($"Not supported message: {item?.ToJson()}");
                }
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "{Class} - {Method} - {Message}", nameof(ObjectReleaseMessageProcessor),
                    nameof(ProcessItem), ex.Message);
                throw;
            }
        }
        return 0;
    }
}