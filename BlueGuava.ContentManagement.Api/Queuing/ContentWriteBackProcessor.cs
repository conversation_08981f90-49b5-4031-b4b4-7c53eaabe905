using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using BlueGuava.MessageQueuing.Worker;
using BlueGuava.Reporting.Messages.Entities.WriteBacks;
using CorrelationId;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

public class ContentWriteBackProcessor : IMessageItemProcessor<ContentWriteBackMessage>
{
    private const string ChatActivityMetric = "ChatActivityMetric";
    private readonly IContentService contentService;
    private readonly ILogger<ContentWriteBackProcessor> logger;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IMetricsCollector metricsCollector;

    public ContentWriteBackProcessor(IContentService contentService,
        ILogger<ContentWriteBackProcessor> logger, ICorrelationContextAccessor correlationContextAccessor,
        IMetricsCollector metricsCollector)
    {
        this.contentService = contentService;
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
        this.metricsCollector = metricsCollector;
    }

    public async Task<int> ProcessItem(ContentWriteBackMessage item, CancellationToken cancel)
    {
        try
        {
            using (logger.BeginScope(new Dictionary<string, object>
            {
                ["CorrelationId"] = Guid.NewGuid().ToString(),
                ["ContentId"] = item.EntityId ?? Guid.Empty.ToString(),
                ["FieldToUpdate"] = item.FieldToUpdate ?? string.Empty,
                ["ProjectID"] = item?.ProjectId ?? Guid.Empty.ToString(),
                ["TimeFrame"] = item?.TimeFrame ?? string.Empty,
                ["TimeStamp"] = item?.TimeStamp ?? DateTime.MinValue,
                ["RunDate"] = item?.RunDate ?? DateTime.MinValue,
                ["Quantities"] = item?.Quantities.ToJson() ?? string.Empty,
                ["Receipt"] = item?.Receipt ?? string.Empty
            }))
            {
                metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(),
                    nameof(ContentWriteBackProcessor));
                await HandleItem(item);
            }
        }
        catch (ArgumentException ex)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentWriteBackProcessor), nameof(ProcessItem))
                .Log(LogLevel.Error, ex.Message);
        }
        return 0;
    }

    private async Task HandleItem(ContentWriteBackMessage item)
    {
        if (item == null)
            throw new ArgumentNullException(nameof(item));

        var entity = await contentService.Retrieve(Guid.Parse(item.EntityId!));

        if (entity == null)
            return;

        var update = ToContentUpdateMessage(item);

        var updated = update.UpdateEntity(entity);

        await contentService.Save(updated, Guid.Empty, false, true, true);
    }

    private static UpdateMessage ToContentUpdateMessage(ContentWriteBackMessage item)
    {
        var fieldToUpdate = item.FieldToUpdate;

        if (!string.IsNullOrEmpty(fieldToUpdate))
            return fieldToUpdate switch
            {
                "Properties" => ParsePropertiesReportMessage(item),
                "Labels" => ParseLabelReportMessage(item),
                _ => throw new ArgumentException("Not supported field to update")
            };

        throw new ArgumentException($"Invalid WriteBackMessage received: {item}");
    }

    private static UpdateMessage ParseLabelReportMessage(ContentWriteBackMessage item)
    {
        var labels = new Dictionary<LabelType, List<string>>();

        if (item.Quantities.TryGetValue("Label", out var label))
            labels.Add(LabelType.Tag, new List<string>() { (string)label });

        return new UpdateLabelsMessage
        { ContentId = item.EntityId, Labels = labels, UpdateStrategy = ListUpdateStrategy.Add };
    }

    private static UpdateMessage ParsePropertiesReportMessage(ContentWriteBackMessage item)
    {
        var properties = new Dictionary<string, string>();

        switch (item.TimeFrame)
        {
            case "Daily":
                properties = GetPropertiesFromMessage(item, TimeFramePrefix.Day);
                break;
            case "Weekly":
                properties = GetPropertiesFromMessage(item, TimeFramePrefix.Week);
                break;
            case "Monthly":
                properties = GetPropertiesFromMessage(item, TimeFramePrefix.Month);
                break;
            case "Total":
                properties = GetPropertiesFromMessage(item, TimeFramePrefix.Total);
                break;
            default:
                AddLastActivityProperty(properties, item);
                break;
        }

        return new BaseDataMessage { ContentId = item.EntityId, Properties = properties };
    }

    private static void AddLastActivityProperty(IDictionary<string, string> properties, ContentWriteBackMessage item)
    {
        if (item.Quantities.TryGetValue("LastActivity", out var lastActivity))
            properties.Add(ConstPrefix.Content + ContentMetricPrefix.LastActivity, (string)lastActivity);
    }

    private static Dictionary<string, string> GetPropertiesFromMessage(ContentWriteBackMessage item, string timeFrame)
    {
        var properties = new Dictionary<string, string>();

        if (item.Quantities.TryGetValue("PlaybackHit", out var playbackHit))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.PlaybackHit + timeFrame, (string)playbackHit);

        if (item.Quantities.TryGetValue("PlaybackPopularity", out var playbackPopularity))
        {
            var propValue = Convert.ToInt64(playbackPopularity);

            if (timeFrame == TimeFramePrefix.Total)
            {
                item.Quantities.TryGetValue("FirstDayHits", out var previousPlaybackPopularity);

                propValue = Convert.ToInt64(playbackPopularity) - Convert.ToInt64(previousPlaybackPopularity);
            }

            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.PlaybackPopularity + timeFrame,
                propValue.ToString());
        }

        if (item.Quantities.TryGetValue("PlaybackSpentTime", out var playbackSpentTime))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.PlaybackSpentTime + timeFrame,
                (string)playbackSpentTime);

        if (item.Quantities.TryGetValue("PlaybackPercentage", out var playbackPercentage))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.PlaybackPercentage + timeFrame,
                (string)playbackPercentage);

        if (item.Quantities.TryGetValue("Likes", out var likes))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Like + timeFrame, (string)likes);

        if (item.Quantities.TryGetValue("Dislikes", out var dislikes))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.DisLike + timeFrame, (string)dislikes);

        if (item.Quantities.TryGetValue("Rank", out var rank))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Rank + timeFrame, (string)rank);

        if (item.Quantities.TryGetValue("Vote", out var vote))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Vote + timeFrame, (string)vote);

        if (item.Quantities.TryGetValue("Review", out var review))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Review + timeFrame, (string)review);

        if (item.Quantities.TryGetValue("UniqueViewer", out var uniqueViewer))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.UniqueViewer + timeFrame, (string)uniqueViewer);

        if (item.Quantities.TryGetValue("Message", out var message))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.ChatMessage + timeFrame, (string)message);

        if (item.Quantities.TryGetValue("Participant", out var participant))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Participant + timeFrame, (string)participant);

        var chatActivityMetrics = item.Quantities.Where(x => x.Key.StartsWith(ChatActivityMetric));

        foreach (var m in chatActivityMetrics)
            properties.Add(ConstPrefix.Metrics + $"{m.Key[ChatActivityMetric.Length..]}:" + timeFrame,
                m.Value.ToString()!);

        if (item.Quantities.TryGetValue("ContentHit", out var contentHit))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Hit + timeFrame, (string)contentHit);

        if (item.Quantities.TryGetValue("ContentSpentTime", out var contentSpentTime))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.SpentTime + timeFrame, (contentSpentTime ?? 0.0).ToString());

        if (item.Quantities.TryGetValue("ContentPlayed", out var contentPlayed))
            properties.Add(ConstPrefix.Metrics + ContentMetricPrefix.Played + timeFrame, (string)contentPlayed);

        return properties;
    }
}