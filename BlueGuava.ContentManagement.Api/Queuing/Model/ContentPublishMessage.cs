﻿using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Api.Queuing.Model;

/// <summary>
/// 
/// </summary>
public class ContentPublishMessage : IQueueItem
{
    /// <summary>
    /// 
    /// </summary>
    public string Id { get; set; } = string.Empty;

    string? IQueueItem.Receipt { get; set; }

    /// <summary>
    /// 
    /// </summary>
    string? IQueueItem.GroupId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    string? IQueueItem.DeduplicationId { get; set; }

    public string? Sender { get; set; }

    /// <summary>
    /// Auto publish on or off
    /// </summary>
    public bool Publish { get; set; }
}