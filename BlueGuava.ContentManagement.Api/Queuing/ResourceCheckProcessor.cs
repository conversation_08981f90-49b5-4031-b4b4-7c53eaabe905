using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Api.Infrastructure;
using BlueGuava.ContentManagement.Api.Models;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.Library.Common.Enums;
using BlueGuava.MessageQueuing.Worker;
using BlueGuava.OrderedSearchResult;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

/// <summary>
/// This is a processor for the <see cref="ResourceCheckTrigger"/> <br/>
/// executes OpenSearch search to check if a resource is available
/// </summary>
public class ResourceCheckProcessor : IMessageItemProcessor<ResourceCheckTrigger>
{
    private readonly ILogger<ResourceCheckProcessor> logger;
    private readonly IOpenSearchService openSearchService;
    private readonly IContentService contentService;
    private readonly IContentIntegration integration;
    private readonly IChimeIntegrationService chimeIntegration;
    private readonly IMessagingService messagingService;
    private readonly IMetricsCollector metricsCollector;

    /// <summary>
    /// This is a processor for the <see cref="ResourceCheckTrigger"/> <br/>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="openSearchService"></param>
    /// <param name="contentService"></param>
    /// <param name="integration"></param>
    /// <param name="chimeIntegration"></param>
    /// <param name="messagingService"></param>
    /// <param name="metricsCollector"></param>
    public ResourceCheckProcessor(
        ILogger<ResourceCheckProcessor> logger,
        IOpenSearchService openSearchService,
        IContentService contentService,
        IContentIntegration integration,
        IChimeIntegrationService chimeIntegration,
        IMessagingService messagingService,
        IMetricsCollector metricsCollector
    )
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.contentService = contentService;
        this.integration = integration;
        this.chimeIntegration = chimeIntegration;
        this.messagingService = messagingService;
        this.metricsCollector = metricsCollector;
    }

    /// <inheritdoc />
    public async Task<int> ProcessItem(ResourceCheckTrigger item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid()
        }))
        {
            try
            {
                metricsCollector.IncrementSqsMessageFromCounter("EventBridge.ResourceCheckTrigger",
                    nameof(ResourceCheckProcessor));
                //await CloseUnusedResources(cancel);
                await SendAnnouncements(cancel);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error while processing ResourceCheckTrigger");
            }
            return 0;
        }
    }

    /// <summary>
    /// Searches for resources that are not used anymore and closes them
    /// </summary>
    /// <param name="cancel"></param>
    private async Task CloseUnusedResources(CancellationToken cancel)
    {
        var decisionRules = new DecisionRules();
        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Type",
            PropertyValue = ContentType.LiveStream.ToString(),
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });
        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Labels:Tag",
            PropertyValue = "Broadcast Running",
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 1
        });

        var searchResult = await openSearchService.Search(null, decisionRules, null, 10, 0, cancel);

        foreach (var data in searchResult.Hits.Data)
        {
            var liveContent = await contentService.Retrieve(data.Id);
            //if content is null we can skip it
            if (liveContent == null) continue;

            //if content is not live we can skip it and add/set "Stopped" CONTENT_BROADCAST_STATUS property
            if (!liveContent.Properties?.ContainsKey(Consts.CONTENT_SESSIONID) ?? false)
            {
                liveContent.Properties ??= new Dictionary<string, string>();

                liveContent.Properties[Consts.CONTENT_BROADCAST_STATUS] = "Stopped";

                continue;
            }

            var meetingId = liveContent.Properties[Consts.CONTENT_SESSIONID];
            try
            {
                var meeting = await chimeIntegration.GetMeeting(meetingId);

                //var asd = await chimeIntegration.ListAttendees(meetingId);

                //int stop = 1;
            }
            // if the meeting is not found we can stop the stream throw by GetMeeting
            catch (Service.Chime.Exceptions.ResourceNotFoundException)
            {
                var content = await contentService.Retrieve(meetingId);
                if (content == null) continue;

                //Meeting already stopped we have to kill the IVS.
                //The IVS uses the same ID like in our side
                await integration.StopStream(content, liveContent.Id);
            }
        }
    }

    /// <summary>
    /// Searches for LiveStreams that are running and sends an announcement to the IVS channel
    /// </summary>
    /// <param name="cancel"></param>
    private async Task SendAnnouncements(CancellationToken cancel)
    {
        var decisionRules = new DecisionRules();
        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Type",
            PropertyValue = ContentType.LiveStream.ToString(),
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });

        decisionRules.Add(new DecisionRule
        {
            PropertyName = $"Content:{nameof(Content.ReleaseDate)}",
            PropertyValue = DateTime.UtcNow.ToString(Constants.DateTimeFormat),
            Condition = PropertyCondition.GreaterOrEqual,
            Formatting = PropertyFormatting.DateTime,
            Index = 1
        });

        decisionRules.Add(new DecisionRule
        {
            PropertyName = $"Content:{nameof(Content.ReleaseDate)}",
            PropertyValue = DateTime.UtcNow.AddMinutes(5).ToString(Constants.DateTimeFormat),
            Condition = PropertyCondition.LessOrEqual,
            Formatting = PropertyFormatting.DateTime,
            Index = 2
        });

        //search for all live streams that will run in the next 5 minutes
        var incomingEvents = await openSearchService.Search(null, decisionRules, null, 10, 0, cancel);

        // if there are no events we can skip the rest
        if (!incomingEvents.Hits.Data.Any()) return;

        decisionRules = new DecisionRules();
        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Type",
            PropertyValue = ContentType.LiveChat.ToString(),
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });

        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Visibility",
            PropertyValue = Visibility.Public.ToString(),
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });

        decisionRules.Add(new DecisionRule
        {
            PropertyName = "Content:Published",
            PropertyValue = "true",
            Condition = PropertyCondition.Equal,
            Formatting = PropertyFormatting.Text,
            Index = 0
        });

        var openChatRooms =
            await GetAllPages(async pageIndex =>
                (await openSearchService.Search(null, decisionRules, null, 10, pageIndex, cancel)).Hits);

        openChatRooms = openChatRooms.Where(x =>
            x.Properties != null &&
            (x.Properties?.ContainsKey(Consts.CONTENT_CHATROOM_ARN) ?? false) &&
            !string.IsNullOrEmpty(x.Properties[Consts.CONTENT_CHATROOM_ARN])
        ).ToList();

        if (!openChatRooms.Any()) return;

        var tasks = new List<Task>();
        // trigger the announcement for all chat rooms
        foreach (var content in incomingEvents.Hits.Data)
            tasks.AddRange(openChatRooms.Select(chatRoom =>
                messagingService.EventAnnouncement(
                    chatRoom.Properties?[Consts.CONTENT_CHATROOM_ARN]!,
                    content
                )));
        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// Paging through the search results
    /// </summary>
    /// <param name="searchMethod"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    private static async Task<List<T>> GetAllPages<T>(Func<int, Task<SearchResult<T>?>> searchMethod)
    {
        List<T> allData = new();
        var currentPageIndex = 0;
        var totalPageCount = -1; // Set to -1 to ensure we initialize it on the first call.

        while (totalPageCount == -1 || currentPageIndex < totalPageCount)
        {
            var result = await searchMethod(currentPageIndex);
            if (!result.Data.Any())
                // No more data, break out of the loop.
                break;

            allData.AddRange(result.Data);

            if (totalPageCount == -1)
                // Set totalPageCount on the first call.
                totalPageCount = (int)Math.Ceiling((double)result.TotalCount / result.PageSize);

            currentPageIndex++;
        }

        return allData;
    }
}