﻿using BlueGuava.ContentManagement.Api.Queuing.Model;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing.Worker;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Api.Queuing;

/// <summary>
/// 
/// </summary>
public class ContentSchedulerProcessor : IMessageItemProcessor<ContentPublishMessage>
{
    private readonly ILogger<ContentSchedulerProcessor> logger;
    private readonly IContentService contentService;
    private readonly IMetricsCollector metricsCollector;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="contentService"></param>
    /// <param name="configuration"></param>
    /// <param name="tokenValidator"></param>
    /// <param name="metricsCollector"></param>
    public ContentSchedulerProcessor(ILogger<ContentSchedulerProcessor> logger,
        IContentService contentService,
        IConfiguration configuration,
        IJwtTokenValidator tokenValidator,
        IMetricsCollector metricsCollector
    )
    {
        this.logger = logger;
        this.contentService = contentService;
        this.metricsCollector = metricsCollector;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="item"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task<int> ProcessItem(ContentPublishMessage item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid().ToString(),
            ["ContentId"] = item.Id
        }))
        {
            metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(), nameof(ContentSchedulerProcessor));
            var content = await contentService.Retrieve(new Guid(item.Id));

            if (content == null) return 0;

            content.Published = item.Publish;
            if (item.Publish) content.AutoPublishDate = null;
            else content.AutoUnPublishDate = null;

            await contentService.Update(content, new ClaimsPrincipal());

        }
        return 0;
    }
}