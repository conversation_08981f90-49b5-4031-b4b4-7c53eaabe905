﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Common.Services.V3;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Packages.Entities.Validation;
//using BlueGuava.Extensions.AWS.QDBL;
using BlueGuava.Extensions.Logging;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Tracewind.Services;
using BlueGuava.MessageQueuing;
using BlueGuava.MessageQueuing.Worker;
using BlueGuava.NotificationService.Client;
using BlueGuava.Reporting.Messages.Entities;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using EventName = BlueGuava.MessageCenter.Common.EventName;
using NotificationEvent = BlueGuava.MessageCenter.Common.EventName;

namespace BlueGuava.ContentManagement.Api.Queuing;

public class ContentUpdateMessageProcessor : IMessageItemProcessor<UpdateMessage>
{
    private readonly ILogger<ContentUpdateMessageProcessor> logger;
    private readonly IContentService contentService;
    private readonly IMessageQueue<JobUpdate> jobUpdateMessagingService;
    private readonly IMessageQueue<UseCaseRequest> useCaseRequestMessagingService;
    //private readonly IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging;
    private readonly IMessageQueue<CommercialMetricMessage> commercialMetricsMessages;
    private readonly INotificationService notificationService;
    private readonly ITracewindService tracewindService;

    private readonly IMetricsCollector metricsCollector;

    //private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IFeatureManager featureManager;
    //private readonly ILedgerRepository<ContentLedger> ledgerRepository;
    private readonly ValidationOptions validationOptions;
    private readonly Globalization globalization;
    private readonly IDynamicManifestGenerator dynamicManifestGenerator;
    private readonly IDeliveryService deliveryService;

    public ContentUpdateMessageProcessor(
        ILogger<ContentUpdateMessageProcessor> logger,
        IContentService contentService,
        IMessageQueue<JobUpdate> jobUpdateMessagingService,
        IMessageQueue<UseCaseRequest> useCaseRequestMessagingService,
        IMessageQueue<CommercialMetricMessage> commercialMetricsMessages,
        INotificationService notificationService,
        ITracewindService tracewindService,
        IMetricsCollector metricsCollector,
        //ICorrelationContextAccessor correlationContextAccessor,
        //ILedgerRepository<ContentLedger> ledgerRepository,
        IFeatureManager featureManager,
        IOptionsMonitor<ValidationOptions> validation,
        IOptionsMonitor<Globalization> globalization,
        IDynamicManifestGenerator dynamicManifestGenerator,
        IDeliveryService deliveryService)
    {
        this.logger = logger;
        this.contentService = contentService;
        this.jobUpdateMessagingService = jobUpdateMessagingService;
        this.useCaseRequestMessagingService = useCaseRequestMessagingService;
        this.commercialMetricsMessages = commercialMetricsMessages;
        this.notificationService = notificationService;
        this.tracewindService = tracewindService;
        this.metricsCollector = metricsCollector;
        //this.correlationContextAccessor = correlationContextAccessor;
        //this.ledgerRepository = ledgerRepository;
        this.featureManager = featureManager;
        this.dynamicManifestGenerator = dynamicManifestGenerator;
        this.globalization = globalization.CurrentValue;
        this.deliveryService = deliveryService;
        validationOptions = validation.CurrentValue;
    }

    public async Task<int> ProcessItem(UpdateMessage item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid().ToString(),
            ["ContentId"] = item.ContentId ?? Guid.Empty.ToString(),
            ["CustomerId"] = item.OwnerId ?? Guid.Empty,
            ["JobId"] = item?.JobId ?? Guid.Empty.ToString(),
            ["JobType"] = item?.JobType.ToString() ?? JobType.NONE.ToString()
        }))
        {
            logger.LogInformation("ProcessItem QueueItem: {@QueueItem}", item);

            metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(),
                nameof(ContentUpdateMessageProcessor));
            await HandleItem(item);
        }
        return 0;
    }

    private Task<Content?> HandleItem(UpdateMessage item)
    {
        return item switch
        {
            null => throw new ArgumentNullException(nameof(item)),
            BaseDataMessage bm => ProcessMessage(bm),
            LocalizationMessage lm => ProcessMessage(lm, ReportLocalization),
            AssetUpdateMessage am => ProcessMessage(am, ReportTranscode),
            AssetBatchUpdateMessage am => ProcessMessage(am, ReportTranscode),
            CreditUpdateMessage cm => ProcessMessage(cm),
            UpsertContentMessage um => ProcessMessage(um),
            UpdateLabelsMessage cm => ProcessMessage(cm),
            CloneContentMessage cm => ProcessMessage(cm),
            ReindexContentMessage rcm => ProcessReindexMessage(rcm),
            ContentDetailsMessage cdm => ProcessContentDetailsMessage(cdm),
            UpdatePropertiesMessage upm => ProcessMessage(upm),
            UpdateRelationsMessage upm => ProcessReindexRelationMessage(upm),
            GenerateManifestMessage gmm => ProcessGenerateManifestMessage(gmm),
            _ => throw new NotSupportedException("Unsupported request type: " + item.GetType().Name)
        };
    }


    private delegate Task OnUpdateProcessed<in TUpdate>(Content content, TUpdate message);

    private async Task<Content> ProcessMessage<TUpdate>(TUpdate update, OnUpdateProcessed<TUpdate>? report = null)
        where TUpdate : UpdateMessage
    {
        try
        {
            var entity = await contentService.Retrieve(Guid.TryParse(update.ContentId, out var contentId)
                ? contentId
                : Guid.Empty);
            if (entity == null)
            {
                await UpdateJobs<TUpdate>(update, null);
                return new Content();
            }

            var original = entity?.RecursiveCopy();

            // entity might be null; UpdateEntity might create
            var updated = update.UpdateEntity(entity);
            if (updated?.Id != entity?.Id) original = null;

            if (updated == null)
            {
                logger.LogWarning(
                    "{UpdateType}.UpdateEntity returned null for '{ContentId}'. The Payload was: {Payload}",
                    typeof(TUpdate).Name, update.ContentId, update.ToJson());
                return new Content(); // update does not support deletion, use delete content job
            }

            //logger.LogWarning("Updated1 content via SQS: {@Content}", updated.Assets);

            //logger.LogWarning("Updated2 content via SQS: {@Content}", updated.Properties);

            if (updated.Properties?.ContainsKey(BlueGuava.Library.Constants.CONTENT_MEDIAINFO_DURATION) == true)
            {
                if (int.TryParse(updated.Properties[BlueGuava.Library.Constants.CONTENT_MEDIAINFO_DURATION], out int duration))
                {
                    //duration = duration * 1000;
                    if (updated.Duration == 0)
                    {
                        updated.Duration = duration;
                    }
                    var originalAsset = updated.Assets.Find(x => x.SubType == SubType.Original && !x.IsDeleted);
                    if (originalAsset != null && originalAsset.Duration == 0)
                    {
                        originalAsset.Duration = duration;
                    }
                }
            }
            if (!ValidateContent(updated, update)) return new Content();

            var result = await contentService.Save(updated, update.OwnerId ?? Guid.Empty, false, true);

            //await UpdateQDBL(updated);

            await TriggerWorkflow(update, result);

            if (report != null) _ = report.Invoke(result, update);
            if (!string.IsNullOrEmpty(update.JobId)) _ = TraceJobFinished(update);
            try
            {
                IncrementMetricsCounters(original, result);
            }
            catch
            {
                // increment metrics does not stop execution
            }

            if (update.JobType == 17)
            {
                var payload = new List<Property>();
                payload.Add(new Property() { Name = "ContentId", Value = $"{updated.Id}" });
                payload.Add(new Property() { Name = "Price", Value = updated.InternalPrice });
                payload.Add(new Property() { Name = "Type", Value = "ContentMintingContract" });
                payload.Add(new Property() { Name = "Timestamp", Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") });

                await commercialMetricsMessages.Enqueue(new CommercialMetricMessage()
                {
                    Properties = payload,
                    RunDate = DateTime.UtcNow,
                    TimeStamp = DateTime.UtcNow
                });
            }

            await UpdateJobs<TUpdate>(update, updated);

            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {UpdateType} for '{ContentId}'", typeof(TUpdate).Name,
                update.ContentId);
            throw;
        }
    }

    private async Task UpdateJobs<TUpdate>(TUpdate update, Content? updated) where TUpdate : UpdateMessage
    {
        if (update.JobType == (int)JobType.PUBLISH_CONTENT)
            await jobUpdateMessagingService.Enqueue(new JobUpdate
            {
                Id = update.JobId,
                Type = JobType.PUBLISH_CONTENT,
                StatusMessage = updated != null ? $"Content publish/un-publish command with parameter: {updated.Published}" : $"Content not updated",
                Status = JobStatus.SUCCEED
            });
        else if (update.JobType == (int)JobType.TRANSLATE)
            await jobUpdateMessagingService.Enqueue(new JobUpdate
            {
                Id = update.JobId,
                Type = JobType.TRANSLATE,
                StatusMessage = $"Content localization updated.",
                Status = JobStatus.SUCCEED
            });
        else if (update.JobType == (int)JobType.MEDIA_PROCESSOR
                 && update.JobSubType !=
                 (int)MediaProcessingType.WHISPER_AI_TRANSCRIPT // marker management will close the job
                 && update.JobSubType !=
                 (int)MediaProcessingType.OPENAI_TRANSLATION // marker management will close the job
                )
            await jobUpdateMessagingService.Enqueue(new JobUpdate
            {
                Id = update.JobId,
                Type = JobType.MEDIA_PROCESSOR,
                StatusMessage = $"Media processor job finished.",
                Status = JobStatus.SUCCEED
            });
        else if (update.JobType is (int)JobType.TRANSCODE or (int)JobType.CUT_VIDEO
                 && (update is AssetUpdateMessage { WorkflowStatus: WorkflowStatus.Succeeded } ||
                     (update is AssetBatchUpdateMessage assetBatchUpdate &&
                      assetBatchUpdate.AssetUpdateMessages.Any(x =>
                          x.WorkflowStatus == WorkflowStatus.Succeeded)))
                )
            await jobUpdateMessagingService.Enqueue(new JobUpdate
            {
                Id = update.JobId,
                Type = (JobType)update.JobType,
                StatusMessage = $"Transcode job finished.",
                Status = JobStatus.SUCCEED
            });
    }

    private async Task<Content?> ProcessReindexMessage(ReindexContentMessage update)
    {
        try
        {
            var entity = await contentService.Retrieve(Guid.TryParse(update.ContentId, out var contentId)
                ? contentId
                : Guid.Empty);
            if (entity == null || (entity.IsDeleted ?? false)) return null;

            if (update.MigrateChapterMarkers)
                await contentService.RequestChapterMigration(entity.Id, entity.OriginalLanguage);
            else if (update.CalculateLabels)
                await contentService.ContentGenreUpdate(entity);
            else if (update.FixDuration)
                await contentService.FixContentDuration(entity, update.OwnerId ?? Guid.Empty);
            else if (update.SKUPackageSetup)
                await contentService.SKUPackageSetup(entity, update.OwnerId ?? Guid.Empty, update.AuthGroupId);
            else if (update.CleanDeletedFile)
                await contentService.CleanDeletedFiles(entity, update.OwnerId ?? Guid.Empty);
            else
                await contentService.UpdateForceIndex(entity);

            await deliveryService.ReleaseContent(entity, true);
            return entity;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {UpdateType} for '{ContentId}'", update.GetType().Name,
                update.ContentId);
            throw;
        }
    }

    private async Task<Content?> ProcessReindexRelationMessage(UpdateRelationsMessage update)
    {
        try
        {
            var entity = await contentService.Retrieve(Guid.TryParse(update.ContentId, out var contentId)
                ? contentId
                : Guid.Empty);
            if (entity == null || (entity.IsDeleted ?? false)) return null;

            entity.Relations = update.Relations;
            await contentService.UpdateContentRelations(entity);

            return entity;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {UpdateType} for '{ContentId}'", update.GetType().Name,
                update.ContentId);
            throw;
        }
    }

    private async Task<Content> ProcessContentDetailsMessage(ContentDetailsMessage contentDetailsMessage)
    {
        try
        {
            var entity = await contentService.Retrieve(Guid.Parse(contentDetailsMessage.ContentId));

            if (entity == null) return new Content();

            if (contentDetailsMessage.SourceId.HasValue &&
                !string.IsNullOrWhiteSpace(contentDetailsMessage.Relation))
                await notificationService.Trigger(EventName.RelationApproval, entity.OwnerId);

            return entity;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {UpdateType} for '{ContentId}'", contentDetailsMessage.GetType().Name,
                contentDetailsMessage.ContentId);
            throw;
        }
    }

    private async Task<Content> ProcessGenerateManifestMessage(GenerateManifestMessage generateManifestMessage)
    {
        try
        {
            if (string.IsNullOrEmpty(generateManifestMessage.ContentId))
            {
                logger.LogWarning("GenerateManifestMessage.ContentId is null or empty");
                return new Content();
            }

            var entity = await contentService.Retrieve(Guid.Parse(generateManifestMessage.ContentId));

            if (entity == null)
            {
                logger.LogWarning("Content with id {ContentId} not found", generateManifestMessage.ContentId);
                return new Content();
            }

            await dynamicManifestGenerator.GenerateManifests(entity.Id.ToString());

            return entity;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {UpdateType} for '{ContentId}'",
                generateManifestMessage.GetType().Name, generateManifestMessage.ContentId);
            throw;
        }
    }

    private bool ValidateContent(Content entity, UpdateMessage update)
    {
        const string delim = "\n   * ";
        var modelState = new ModelStateDictionary();

        entity.Publish(globalization.Languages, validationOptions, modelState, true, false);

        if (!modelState.IsValid)
        {
            var builder = new StringBuilder("Cannot update content because of validation errors:");
            var entries = modelState.SelectMany(kvp => kvp.Value.Errors.Select(e => new { kvp.Key, e.ErrorMessage }));
            foreach (var item in entries) builder.Append(delim + $"{item.Key}: {item.ErrorMessage}");

            var ex = new Exception(builder.ToString());
            _ = tracewindService.ToErrorTraceAsync(new Job
            {
                Id = update.JobId,
                Type = (JobType)update.JobType,
                ReferenceObjectId = update.ContentId,
                CorrelationId = update.ContentId
            }, Tracewind.Common.Models.ObjectType.Content, 2, ex);

            _ = jobUpdateMessagingService.Enqueue(new JobUpdate
            {
                Id = update.JobId,
                Type = (JobType)update.JobType,
                ReferenceObjectId = update.ContentId,
                Status = JobStatus.FAILED,
                StatusMessage = ex.Message
            });
        }

        return modelState.IsValid;
    }

    private async Task TriggerWorkflow(UpdateMessage update, Content entity)
    {
        if (!(entity?.Properties?.ContainsKey(Consts.CONTENT_WORKFLOWID) ?? false)) return;
        if (!Guid.TryParse(entity.Properties[Consts.CONTENT_WORKFLOWID], out var workflowId)) return;

        if (!(update is AssetUpdateMessage { SubType: SubType.Original } am)) return;
        await useCaseRequestMessagingService.Enqueue(new UseCaseRequest
        {
            Id = Guid.NewGuid().ToString(),
            WorkflowTemplateId = workflowId.ToString(),
            ReferenceObjectId = entity.Id.ToString(),
            InputFileLocation = am.ObjectUrl,
            OwnerId = update.OwnerId ?? Guid.Empty,
            OwnerEmail = update.OwnerEmail
        });
    }

    private async Task TraceJobFinished(UpdateMessage item)
    {
        await tracewindService.ToSuccessTraceAsync(new Job
        {
            Id = item.JobId,
            Type = (JobType)item.JobType,
            CorrelationId = item.JobId,
            Status = JobStatus.SUCCEED,
            CreatedDate = DateTime.UtcNow,
            ReferenceObjectId = item.ContentId
        },
            Tracewind.Common.Models.ObjectType.Content, 2,
            $"Process Content Update ({item.GetType().Name}).JobType: {item.JobType} {item.ToJson()}.");
    }

    private async Task ReportLocalization(Content entity, LocalizationMessage item)
    {
        // close job at job manager side: the job succeeded
        await jobUpdateMessagingService.Enqueue(new JobUpdate
        {
            Id = item.JobId,
            Type = (JobType)item.JobType,
            Status = JobStatus.SUCCEED
        });
    }

    private async Task ReportTranscode(Content entity, AssetUpdateMessage item)
    {
        var report = item.WorkflowStatus switch
        {
            WorkflowStatus.Succeeded => true,
            WorkflowStatus.Failed => true,
            _ => false
        } && item.SubType switch
        {
            SubType.Original => true,
            SubType.HLS => true,
            SubType.DASH => true,
            SubType.MSS => true,
            SubType.MP4 => true,
            SubType.MP3 => true,
            SubType.WAV => true,
            SubType.FLAC => true,
            _ => false
        };

        if (!report) return;
        if (item.OwnerId == null) return;
        if (item.OwnerId == Guid.Empty) return;

        var info = new Dictionary<string, string>
        {
            ["streamKind"] = $"{item.SubType}",
            ["workflowStatus"] = $"{item.WorkflowStatus}",
            ["objectType"] = "Content",
            ["objectId"] = $"{entity.Id}",
            ["contentId"] = $"{entity.Id}",
            ["objectName"] = entity.OriginalTitle,
            ["fileName"] = Path.GetFileName(item.ObjectUrl),
            ["duration"] = !(item.Duration > 0)
                ? "unknown" // pretty print duration
                : TimeSpan.FromMilliseconds(item.Duration.Value).ToString("hh:mm:ss"),
            ["errorMessage"] = !string.IsNullOrEmpty(item.ErrorMessage)
                ? item.ErrorMessage
                : $"The job {item.WorkflowStatus}"
        };

        var eventName = item.SubType == SubType.Original
            ? NotificationEvent.ContentUploaded
            : NotificationEvent.TranscodingFinished;
        await notificationService.Trigger(eventName, (Guid)item.OwnerId, info);
    }

    private async Task ReportTranscode(Content entity, AssetBatchUpdateMessage item)
    {
        foreach (var updateMessage in item.AssetUpdateMessages) await ReportTranscode(entity, updateMessage);
    }

    private void IncrementMetricsCounters(Content? oldContent, Content? newContent)
    {
        var contentType = newContent?.Type ?? oldContent?.Type ?? ContentType.None;
        if (oldContent == null && newContent == null) return;
        else if (oldContent == null) // newContent != null
            metricsCollector.IncrementSuccessCounter(ApiMethod.Create, contentType);
        else if (newContent == null) // oldContent != null
            metricsCollector.IncrementSuccessCounter(ApiMethod.Delete, contentType);
        else // oldContent != null && newContent != null
            metricsCollector.IncrementSuccessCounter(ApiMethod.Update, contentType);

        var wasPublished = oldContent?.Published ?? false;
        var nowPublished = newContent?.Published ?? false;
        if (wasPublished == nowPublished) return; // published flag did not change
        if (nowPublished) metricsCollector.IncrementSuccessCounter(ApiMethod.Publish, contentType);
        else /* was */ metricsCollector.IncrementSuccessCounter(ApiMethod.Unpublish, contentType);
    }
    /*
        private async Task UpdateQDBL(Content? content)
        {
            if (!await featureManager.IsEnabledAsync("AllowContentQDBL"))
            {
                logger.LogDebug("Content QDBL is turned off by FeatureManager");
                return;
            }

            if (content == null) return;
            var result = await ledgerRepository.Retrieve(content.Id.ToString());
            if (result != null) await ledgerRepository.Update(ContentLedger.FromEntity(content), content.Id.ToString());
            else await ledgerRepository.Save(ContentLedger.FromEntity(content));
        }*/
}