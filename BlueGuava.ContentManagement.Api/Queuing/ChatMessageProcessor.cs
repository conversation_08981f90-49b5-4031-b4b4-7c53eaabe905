using Amazon.KinesisFirehose.Model;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing.Worker;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ChatMessage = BlueGuava.ContentManagement.Messages.Entities.ChatMessage;

namespace BlueGuava.ContentManagement.Api.Queuing;

/// <summary>
/// This is a processor for the <see cref="Messages.Entities.ChatMessage"/> <br/>
/// </summary>
public class ChatMessageProcessor : IMessageItemProcessor<ChatMessage>
{
    private readonly ILogger<ChatMessageProcessor> logger;
    private readonly IAmazonIvsService amazonIvsService;
    private readonly IJwtTokenValidator tokenValidator;
    private readonly IMetricsCollector metricsCollector;

    /// <summary>
    /// This is a processor for the <see cref="ChatMessage"/> <br/>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="amazonIvsService"></param>
    /// <param name="tokenValidator"></param>
    /// <param name="metricsCollector"></param>
    public ChatMessageProcessor(
        ILogger<ChatMessageProcessor> logger,
        IAmazonIvsService amazonIvsService,
        IJwtTokenValidator tokenValidator,
        IMetricsCollector metricsCollector)
    {
        this.logger = logger;
        this.amazonIvsService = amazonIvsService;
        this.tokenValidator = tokenValidator;
        this.metricsCollector = metricsCollector;
    }

    /// <summary>
    /// Sends a message to the websocket
    /// </summary>
    /// <param name="item"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<int> ProcessItem(ChatMessage item, CancellationToken cancel)
    {

        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = item?.Id ?? Guid.Empty.ToString(),
            ["ContentId"] = item?.ContentId ?? Guid.Empty.ToString(),
            ["RoomArn"] = item?.RoomArn ?? string.Empty,
            ["UserAccessToken"] = item?.UserAccessToken ?? string.Empty,
        }))
        {

            try
            {

                if (string.IsNullOrEmpty(item.UserAccessToken))
                    throw new InvalidArgumentException("UserAccessToken is null or empty");

                if (string.IsNullOrEmpty(item.RoomArn)) throw new InvalidArgumentException("RoomArn is null or empty");

                metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower());
                if (item.Deleted)
                {
                    await amazonIvsService.RemoveMessage(item.RoomArn!, item.Id!, item.ContentId!, item.Reason);
                    return 0;
                }

                // send the message to the websocket
                var user = tokenValidator.GetPrincipalFromToken(item.UserAccessToken);
                await amazonIvsService.SendMessage(item.RoomArn!, user, item.Message!);

            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Error processing websocket message");

                // put the message to the dead letter 
                throw;
            }
            return 0;
        }
    }
}