using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.MessageQueuing.Worker;
using Microsoft.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.NotificationService.Client;

namespace BlueGuava.ContentManagement.Api.Queuing;

/// <summary>
/// Processes <see cref="JoinProjectMessage"/> and sends the message based on <see cref="ProjectActivityType"/>
/// come in message body.
/// </summary>
public class JoinProjectMessageProcessor : IMessageItemProcessor<JoinProjectMessage>
{
    private readonly ILogger<JoinProjectMessageProcessor> logger;
    private readonly IOpenSearchService openSearchService;
    private readonly INotificationService notificationService;
    private readonly IJwtTokenValidator tokenValidator;
    private readonly IMetricsCollector metricsCollector;

    /// <summary>
    /// Initializes new instance of <see cref="JoinProjectMessageProcessor"/> class.
    /// </summary>
    /// <param name="logger">Logger.</param>
    /// <param name="openSearchService">Service used for searching resources.</param>
    /// <param name="notificationService">Service used for sending notifications.</param>
    /// <param name="tokenValidator">Service used for JWT validation.</param>
    /// <param name="metricsCollector"></param>
    public JoinProjectMessageProcessor(
        ILogger<JoinProjectMessageProcessor> logger,
        IOpenSearchService openSearchService,
        INotificationService notificationService,
        IJwtTokenValidator tokenValidator,
        IMetricsCollector metricsCollector
    )
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.notificationService = notificationService;
        this.tokenValidator = tokenValidator;
        this.metricsCollector = metricsCollector;
    }

    /// <inheritdoc />
    public async Task<int> ProcessItem(JoinProjectMessage item, CancellationToken cancel)
    {
        using (logger.BeginScope(new Dictionary<string, object>
        {
            ["CorrelationId"] = Guid.NewGuid().ToString(),
            ["JoinProjectMessageId"] = item.Id,
            ["Token"] = item.Token ?? string.Empty,
            ["Receipt"] = item?.Receipt ?? string.Empty,
            ["ActivityType"] = item?.ActivityType.ToString() ?? "NONE"
        }))
        {
            metricsCollector.IncrementSqsMessageFromCounter(item.Sender?.ToLower(),
                nameof(JoinProjectMessageProcessor));
            var validation = tokenValidator.GetPrincipalFromToken(item.Token ?? string.Empty);
            if (validation == null)
                throw new UnauthorizedAccessException("Invalid token");

            var exp = DateTimeOffset.FromUnixTimeSeconds(
                long.TryParse(validation.FindFirst("exp")?.Value, out var expValue)
                    ? expValue
                    : 0);
            if (exp.UtcDateTime < DateTime.UtcNow)
                throw new UnauthorizedAccessException("Token expired");

            /*
            Remove temporary
            var recipientId = validation.Claims.First(i => i.Type == ClaimFields.CustomerId).Value;

            logger.LogDebug("User with Id {userId} made the {activityType} action", recipientId,
                $"{item.ActivityType}");

            switch (item.ActivityType)
            {
                case ProjectActivityType.ConnectToProject:
                {
                    await notificationService.Trigger(EventName.ConnectToProject, recipientId);
                }
                    break;

                case ProjectActivityType.OpenTheProject:
                {
                    var decisionRules = new DecisionRules
                    {
                        new()
                        {
                            PropertyName = "Content:Type",
                            PropertyValue = ContentType.LiveStream.ToString(),
                            Condition = PropertyCondition.Equal,
                            Formatting = PropertyFormatting.Text,
                            Index = 0
                        },
                        new()
                        {
                            PropertyName = $"Content:properties:{Consts.CONTENT_BROADCAST_STATUS}",
                            PropertyValue = "Started",
                            Condition = PropertyCondition.Equal,
                            Formatting = PropertyFormatting.Text,
                            Index = 1
                        }
                    };

                    //search for all live streams that will run in the next 5 minutes
                    var incomingEvents = await openSearchService.Search(null, decisionRules, null, 10, 0, cancel);

                    // if there are no events we can skip the rest
                    if (!incomingEvents.Data.Any()) return 0;

                    await notificationService.Trigger(EventName.OpenTheProject, recipientId);
                }
                    break;

                default:
                    logger.LogError("Incoming ProjectActivityType in payload is not supported: {activityType}",
                        item.ActivityType);
                    break;
            }
            */
        }
        return 0;
    }
}