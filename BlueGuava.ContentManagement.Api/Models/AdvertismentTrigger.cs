using System;
using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Api.Models;

/// <summary>
/// This is a trigger for the <see cref="AdvertismentTrigger"/> <br/>
/// </summary>
public class AdvertismentTrigger : IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    string? IQueueItem.Receipt { get; set; }

    string? IQueueItem.GroupId
    {
        get => $"{Id}";
        set => _ = value;
    }

    string? IQueueItem.DeduplicationId
    {
        get => $"{Id}";
        set => _ = value;
    }

    public string? Sender { get; set; }
}