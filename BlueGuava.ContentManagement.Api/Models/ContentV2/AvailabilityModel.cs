﻿using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class AvailabilityModel : ContentManagement.Integration.V2.AvailabilityModel
{
    public static AvailabilityModel FromEntity(Availability entity)
    {
        return new AvailabilityModel()
        {
            AgeLimit = entity.AgeLimit,
            AdvisoryCodes = entity.AdvisoryCodes,
            AvailableFrom = entity.AvailableFrom,
            AvailableUntil = entity.AvailableUntil,
            Cities = entity.Cities
        };
    }
}