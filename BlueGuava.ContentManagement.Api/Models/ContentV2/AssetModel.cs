﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class AssetModel : ContentManagement.Integration.V2.AssetModel
{
    public static List<AssetModel> FromEntity(List<Asset> ratings)
    {
        return ratings?.ConvertAll(FromEntity) ?? new List<AssetModel>();
    }

    public static AssetModel FromEntity(Asset entity)
    {
        return new AssetModel()
        {
            Id = entity.Id,
            Type = entity.Type,
            SubType = entity.SubType,

            Duration = entity.Duration,
            LifeCyclePolicies = entity.LifeCyclePolicies,
            Locale = entity.Locale,
            ModifiedDate = entity.ModifiedDate,
            ObjectUrl = entity.ObjectUrl,
            PublicUrl = entity.PublicUrl,
            RestoredObjectUrl = entity.RestoredObjectUrl,
            WorkflowStatus = entity.WorkflowStatus,
            IsDeleted = entity.IsDeleted,
            FileSize = entity.FileSize,
            CreatedDate = entity.CreatedDate == default ? DateTime.UtcNow : entity.CreatedDate,
            IsPublic = entity.IsPublic,
            IpfsHash = entity.IpfsHash,
            FileName = entity.FileName
        };
    }
}