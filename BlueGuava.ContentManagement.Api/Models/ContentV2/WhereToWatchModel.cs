﻿using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class WhereToWatchModel : ContentManagement.Integration.V2.WhereToWatchModel
{
    public static List<WhereToWatchModel?> FromEntity(List<WhereToWatch> collection)
    {
        return collection?.ConvertAll(FromEntity) ?? new List<WhereToWatchModel?>();
    }

    public static WhereToWatchModel? FromEntity(WhereToWatch? entity)
    {
        if (entity == null) return null;
        return new WhereToWatchModel
        {
            ProviderName = entity.ProviderName,
            WatchUrl = entity.WatchUrl
        };
    }
}