﻿using BlueGuava.ContentManagement.Common.Entities.V2;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class ExportRequestModel : ContentManagement.Integration.V2.ExportRequestModel
{
    public static ExportRequestModel FromEntity(ExportRequest entity)
    {
        return new ExportRequestModel
        {
            OutputFileExtension = entity.OutputFileExtension,
            ItemIds = entity.ItemIds
        };
    }

    public ExportRequest ToEntity()
    {
        return new ExportRequest
        {
            OutputFileExtension = OutputFileExtension,
            ItemIds = ItemIds
        };
    }
}