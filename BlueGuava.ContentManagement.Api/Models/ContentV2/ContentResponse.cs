﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class ContentResponse : ContentManagement.Integration.V2.ContentResponse
{
    public static ContentResponse? FromEntity(Content? entity)
    {
        if (entity == null) return null;

        return new ContentResponse
        {
            Id = entity.Id,
            Type = entity.Type,
            AllowUserRating = entity.AllowUserRating ?? false,
            AllowComments = entity.AllowComments ?? false,
            AllowMinting = entity.AllowMinting ?? false,
            AllowEmailNotification = entity.AllowEmailNotification ?? false,
            AllowRemix = entity.AllowRemix ?? false,
            AllowChat = entity.AllowChat ?? false,
            AllowSideshow = entity.AllowSideshow ?? false,
            AllowLyrics = entity.AllowLyrics ?? false,
            AllowUpcoming = entity.AllowUpcoming ?? false,
            Assets = entity.Assets ?? new List<Asset>(),
            AuthGroupIds = entity.AuthGroupIds ?? new List<Guid>(),
            Color = entity.Color,
            CreatedDate = entity.CreatedDate,
            Credits = entity.Credits ?? new List<Credit>(),
            Downloadable = entity.Downloadable ?? false,
            Duplicate = entity.Duplicate ?? false,
            Duration = entity.Duration ?? 0,
            ExhibitionWindow = entity.ExhibitionWindow,
            ExternalId = entity.ExternalId,
            LastModifiedBy = entity.LastModifiedBy,
            LastModifiedDate = entity.LastModifiedDate,
            Localizations = entity.Localizations ?? new Dictionary<string, Packages.Entities.V2.Localization>(),
            OriginalFileName = entity.OriginalFileName,
            OriginalTitle = entity.OriginalTitle,
            OwnerId = entity.OwnerId,
            Properties = entity.Properties ?? new Dictionary<string, string>(),
            Published = entity.Published,
            PublishedDate = entity.PublishedDate,
            ReferenceId = entity.ReferenceId,
            ReleaseDate = entity.ReleaseDate,
            Themes = entity.Themes,
            OriginalLanguage = entity.OriginalLanguage,
            OriginalTranscript = entity.OriginalTranscript,
            Entities = entity.Entities,
            WhereToWatch = entity.WhereToWatch ?? new List<WhereToWatch>(),
            InternalPrice = entity.InternalPrice,
            TokenPrice = entity.TokenPrice,
            TokenCurrency = entity.TokenCurrency,
            ProcessingStatus = entity.ProcessingStatus,
            Visibility = entity.Visibility,
            Labels = entity.Labels ?? new Dictionary<LabelType, List<string>>(),
            HasSubtitle = entity.HasSubtitle ?? false,
            HasTags = entity.HasTags ?? false,
            PublishingRule = entity.PublishingRule,
            Notification = entity.Notification,
            IsDeleted = entity.IsDeleted ?? false,
            IsDrmEnabled = entity.IsDrmEnabled ?? false,
            ArchivalPolicy = entity.ArchivalPolicy,
            DeletionPolicy = entity.DeletionPolicy,
            Relations = entity.Relations,
            RewardCode = entity.RewardCode,
            OriginalOwnerId = entity.OriginalOwnerId,
            IsRewarded = entity.IsRewarded ?? false,
            PollHtml = entity.PollHtml,
        };
    }
}