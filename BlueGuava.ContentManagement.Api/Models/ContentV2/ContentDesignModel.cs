﻿using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class ContentDesignModel : ContentManagement.Integration.V2.ContentDesignModel
{
    public static ContentDesignModel FromEntity(ContentDesign entity)
    {
        return new ContentDesignModel()
        {
            MainTextColor = entity.MainTextColor,
            ShortInfoColor = entity.ShortInfoColor,
            ArtistTextColor = entity.ArtistTextColor,
            BackgroundColor = entity.BackgroundColor
        };
    }
}