﻿using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Api.Models.ContentV2;

public class CreditModel : ContentManagement.Integration.V2.CreditModel
{
    public static List<CreditModel> FromEntity(List<Credit> credits)
    {
        return credits?.ConvertAll(FromEntity) ?? new List<CreditModel>();
    }

    public static CreditModel FromEntity(Credit entity)
    {
        return new CreditModel
        {
            Name = entity.Name,
            Roles = entity.Roles,
            Character = entity.Character
        };
    }
}