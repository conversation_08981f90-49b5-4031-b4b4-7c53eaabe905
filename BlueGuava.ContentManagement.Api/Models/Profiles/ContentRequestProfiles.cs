using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Api.Models.Profiles;

public class ContentRequestProfiles : Profile
{
    public ContentRequestProfiles()
    {
        CreateMap<ContentRequest, Content>()
            .ForMember(dest => dest.Localizations,
                opt => opt.MapFrom(src => ConvertLocalization(src.Localizations)))
            .ForMember(dest => dest.ExhibitionWindow,
                opt => opt.MapFrom(src => ConvertAvailability(src.ExhibitionWindow)))
            .ForMember(dest => dest.Themes,
                opt => opt.MapFrom(src => ConvertThemes(src.Themes)))
            .ReverseMap()
            ;

        CreateMap<IngestContentRequestV2, Content>()
            .ForMember(dest => dest.Id,
                opt => opt.MapFrom(src => src.ContentId ?? Guid.NewGuid()))
            .ForMember(dest => dest.Localizations,
                opt => opt.MapFrom(src => ConvertLocalization(src.Localizations)))
            .ForMember(dest => dest.ExhibitionWindow,
                opt => opt.MapFrom(src => ConvertAvailability(src.ExhibitionWindow)))
            .ForMember(dest => dest.Themes,
                opt => opt.MapFrom(src => ConvertThemes(src.Themes)))
            ;

        CreateMap<AssetModel, Asset>()
            .ForMember(dest => dest.Id,
                opt => opt.MapFrom(src => src.Id ?? Guid.NewGuid()))
            .ForMember(dest => dest.Type,
                opt => opt.MapFrom(src => src.Type == AssetType.Pdf ? AssetType.File : src.Type))
            .ForMember(dest => dest.WorkflowStatus,
                opt => opt.MapFrom(src => src.WorkflowStatus ?? Packages.Entities.DrmEntities.WorkflowStatus.Unknown))
            .ForMember(dest => dest.CreatedDate,
                opt => opt.MapFrom(src => src.CreatedDate == default ? DateTime.UtcNow : src.CreatedDate))
            .ReverseMap()
            ;


        CreateMap<CreditModel, Credit>()
            .ReverseMap()
            ;

        CreateMap<WhereToWatchModel, WhereToWatch>()
            .ReverseMap()
            ;
    }


    private static Dictionary<string, Packages.Entities.V2.Localization> ConvertLocalization(
        Dictionary<string, LocalizationModel>? src)
    {
        return src?.ToDictionary(e => e.Key, e => e.Value.ToEntity(), StringComparer.OrdinalIgnoreCase)
               ?? new Dictionary<string, Packages.Entities.V2.Localization>(StringComparer.OrdinalIgnoreCase);
    }

    private static Dictionary<string, Availability?> ConvertAvailability(Dictionary<string, AvailabilityModel>? src)
    {
        return src?.ToDictionary(e => e.Key, e => e.Value.ToEntity(), StringComparer.OrdinalIgnoreCase)
               ?? new Dictionary<string, Availability?>(StringComparer.OrdinalIgnoreCase);
    }

    private static Dictionary<DesignTypes, ContentDesign> ConvertThemes(
        Dictionary<DesignTypes, ContentDesignModel>? src)
    {
        return src
                   ?.OrderBy(x => x.Key.GetHashCode())
                   ?.ToDictionary(x => x.Key, x => x.Value.ToEntity())
               ?? new Dictionary<DesignTypes, ContentDesign>();
    }
}