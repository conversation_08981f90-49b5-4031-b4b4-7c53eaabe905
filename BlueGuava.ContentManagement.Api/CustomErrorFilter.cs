using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlueGuava.Extensions.AspNetCore.ExceptionHandling;
using BlueGuava.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
namespace BlueGuava.ContentManagement.Api;

public class CustomErrorFilter : IAsyncResultFilter
{
    private const string ERROR_TEMPLATE = "ERROR_{0}_{1}_{2}";

    private readonly Dictionary<int, string> statusCodesWithStrings = new()
    {
        { 400, "BADREQUEST" },
        { 401, "UNAUTHORIZED" },
        { 403, "FORBIDDEN" },
        { 404, "NOTFOUND" },
        { 409, "CONFLICT" }
    };

    public async Task OnResultExecutionAsync(ResultExecutingContext context, ResultExecutionDelegate next)
    {
        var traceId = context.HttpContext.TraceIdentifier;

        if (int.TryParse(JToken.Parse(context.Result.ToJson()!)["StatusCode"]?.ToString(), out var statusCode) &&
            !IsSuccessStatusCode(statusCode) && statusCodesWithStrings.Any(x => x.Key == statusCode))
        {
            var errorTitle = GetErrorTitle(context, statusCode);
            var errorMessage = "";
            if (context.Result is BadRequestObjectResult badRequestResult)
            {
                //var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<CustomErrorFilter>>();

                if (badRequestResult.Value is ValidationProblemDetails validationProblemDetails)
                {
                    errorMessage = string.Join("; ", validationProblemDetails.Errors
                        .SelectMany(kvp => kvp.Value.Select(err => $"{kvp.Key}: {err}")));
                }
                else if (badRequestResult.Value is string errorString)
                {
                    errorMessage = errorString;
                }
                else if (badRequestResult.Value != null)
                {
                    errorMessage = JsonConvert.SerializeObject(badRequestResult.Value, Formatting.Indented);
                }
                else
                {
                    errorMessage = "No additional error details available.";
                }
            }
            context.Result = CustomErrorResponse.ToObjectResult(statusCode, traceId, errorTitle, errorMessage!);
        }

        await next();
    }

    private static bool IsSuccessStatusCode(int statusCode)
    {
        return statusCode is >= 200 and <= 299;
    }

    private string GetErrorTitle(ActionContext context, int statusCode)
    {
        var method = ((ControllerActionDescriptor)context.ActionDescriptor).MethodInfo;
        var methodName = method.Name;
        var errorTitle = string.Format(ERROR_TEMPLATE, "CON", methodName.ToUpperInvariant(),
            statusCodesWithStrings[statusCode]);
        if (methodName.ToLowerInvariant() != "get") return errorTitle;
        var parameterName = method.GetParameters()[0].Name?.ToUpperInvariant();
        errorTitle += $"_{parameterName}";
        return errorTitle;
    }
}