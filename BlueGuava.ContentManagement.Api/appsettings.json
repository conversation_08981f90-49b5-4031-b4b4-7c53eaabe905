﻿{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.AspNetCore.Hosting.Diagnostics": "Error",
        "AspNetCore.HealthChecks.UI": "Warning",
        "HealthChecks": "Warning",
        "Microsoft.AspNetCore.Authentication": "Information"
      }
    },
    "Enrich": [
      "FromLogContext",
      "With",
      "WithExceptionDetails",
      "WithCorrelationId"
    ],
    "Using": [
      "Serilog.Sinks.Console"
    ],
    "WriteTo": {
      "0": {
        "Name": "Console",
        "Args": {
          "formatter": {
            "type": "Serilog.Formatting.Json.JsonFormatter, Serilog",
            "renderMessage": true
          }
        }
      }
    }
  },
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 1000,
      "MaxConcurrentUpgradedConnections": 1000
    }
  },
  "FeatureManagement": {
    "AllowOpenSearch": true,
    "AllowAutomation": false,
    "AllowAutomation_AutoTagsDate": false,
    "AllowAutomation_CreatorAssignment": false,
    "AllowAutomation_ProjectAssignment": false,
    "AllowContentAutoPublish": false,
    "AllowContentQDBL": false,
    "AllowAutomation_AutoImageGeneration": false,
    "ContentSearch_AllowOrganizationByToken": true,
    "Send_ContentCreationMessage_On_Content_Reindex": true,
    "Allow_DynamicManifestManipulation": false,
    "AllowExtractFileName": false,
    "AllowContentToken": true,
    "AllowContentCacheNexius": false
  },
  "Ledger": {
    "Name": "ent-qldb",
    "TableName": "Content"
  },
  "ImageProcessor:CommunicationSettings": {
    "CircuitBreakerThreshold": 1,
    "CircuitBreakerDuration": "00:00:30"
  },
  "ResourceCheckTriggers": {
    "BatchSize": 1
  },
  "ItemSequenceOptions": {
    "BufferSize": 200,
    "BufferTime": "00:00:30"
  },
  "HealthCheckOptions": {
    "Period": "00:00:60"
  }
}