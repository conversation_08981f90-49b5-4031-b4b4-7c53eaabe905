﻿using System;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Api.Models;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Logging;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueGuava.ContentManagement.Api.Localization.V2;

public interface IExternalContentManagementService
{
    Task<bool> RequestLocalization(TranslateJobRequest request, <PERSON>laims<PERSON>rincipal user);

    /// <summary>
    /// Creates an xml file in the S3 from the Localization proeprty with the given sourceLocale
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="sourceLocale"></param>
    /// <returns> if there is something to translate <code>true</code> and the file location <code>false</code> if not</returns>
    Task<(bool, string?, string?)> PutLocalizationFileToS3(Guid contentId, string sourceLocale);

    Task<bool> RequestExport(ContentSearch searchArgs, ExportRequest? rawExportRequest, ClaimsPrincipal user);
}

public class ExternalContentManagementService : IExternalContentManagementService
{
    private static readonly PropertyInfo[] translatableFields = typeof(Packages.Entities.V2.Localization)
        .GetProperties()
        .Where(prop => Attribute.IsDefined(prop, typeof(Packages.Entities.Translatable))).ToArray();

    private readonly string translateBucket;
    private readonly ILogger<ExternalContentManagementService> logger;
    private readonly IContentRepository contentRepository;
    private readonly IContentExport contentExport;
    private readonly IS3Repository s3Repository;
    private readonly IMessageQueue<JobRequest> jobRequestMessagingService;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public ExternalContentManagementService(
        ILogger<ExternalContentManagementService> logger,
        IOptionsMonitor<S3Bucket> bucketSettings,
        IContentExport contentExport,
        IContentRepository contentRepository,
        IS3Repository s3Repository,
        IMessageQueue<JobRequest> jobRequestMessagingService,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.contentExport = contentExport;
        this.contentRepository = contentRepository;
        this.s3Repository = s3Repository;
        this.jobRequestMessagingService = jobRequestMessagingService;
        this.correlationContextAccessor = correlationContextAccessor;
        translateBucket = bucketSettings?.CurrentValue?.Ingest ?? string.Empty;
    }

    public async Task<bool> RequestExport(ContentSearch searchArgs, ExportRequest? rawExportRequest,
        ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ExternalContentManagementService),
                    nameof(RequestExport))
                .Log(LogLevel.Debug, ContentSearch.LogFormat, searchArgs.GetValues());

        if (rawExportRequest?.ItemIds?.Count > 0)
        {
            // TODO: this also should be a job
            _ = contentExport.ExportContents(rawExportRequest, user);
        }
        else
        {
            var request = new ExportJobRequest(user.GetCustomerId(), user.GetEmail(), ExportJobType.Content)
            {
                Name = "ContentExportJob_" + DateTime.UtcNow.Ticks,
                SearchParameters = searchArgs.ToJson(),
                OutputFileExtension =
                    "csv", //rawExportRequest?.OutputFileExtension, //force this because of the console
                ItemIds = rawExportRequest?.ItemIds
            };

            await jobRequestMessagingService.Enqueue(request);
        }

        return true;
    }

    public async Task<bool> RequestLocalization(TranslateJobRequest request, ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ExternalContentManagementService),
                    nameof(RequestLocalization))
                .Log(LogLevel.Debug, "Request: {Request}", request.ToJson());

        var (result, locale, inputFileLocation) =
            await PutLocalizationFileToS3(new Guid(request.ReferenceObjectId), request.SourceLanguageCode);

        if (!result) return false;

        request.InputFileLocation = inputFileLocation;
        request.SourceLanguageCode = locale;
        request.TranslationType = TranslationType.CONTENT_LOCALIZATION;
        request.OwnerId = user.GetCustomerId();
        request.OwnerEmail = user.GetEmail();

        await jobRequestMessagingService.Enqueue(request);
        return true;
    }

    public async Task<(bool, string?, string?)> PutLocalizationFileToS3(Guid contentId, string? sourceLocale)
    {
        var content = await contentRepository.Retrieve(contentId);
        if (content == null) return (false, null, null);

        //cant determine the source language
        if (string.IsNullOrEmpty(content.OriginalLanguage) &&
            (string.IsNullOrEmpty(sourceLocale) || sourceLocale == "-"))
            throw new ArgumentException(
                "Can't determine SourceLanguage because the OriginalLanguage of the content and the input locale is null or empty");

        if (string.IsNullOrEmpty(sourceLocale) || sourceLocale == "-") sourceLocale = content.OriginalLanguage;

        if (content.Localizations == null)
            return (false, null, null);

        if (!content.Localizations.TryGetValue(sourceLocale ?? string.Empty, out var localization) &&
            !content.Localizations.TryGetValue("--", out localization))
            return (false, null, null);

        var properties = translatableFields // translate only marked fields that are not empty
            .Select(p => new Property { Key = p.Name, Value = p.GetValue(localization) as string })
            .Where(e => !string.IsNullOrEmpty(e.Value)).ToList();

        if (properties.Count <= 0) return (false, null, null);

        var path = $"{contentId}/localizations/{DateTime.UtcNow.Ticks}/loc_{sourceLocale}.xml";
        await s3Repository.PutFileAsync(new ContentLocalization { Translations = properties }, translateBucket,
            path, "localizations");

        return (true, sourceLocale, $"s3://{translateBucket}/{path}");
    }
}