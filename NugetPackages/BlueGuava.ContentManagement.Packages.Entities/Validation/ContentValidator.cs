﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using BlueGuava.ContentManagement.Packages.Entities.Exceptions;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace BlueGuava.ContentManagement.Packages.Entities.Validation
{
    public static class ContentValidatorExtentsion
    {
        private const string delim = "\n   * ";

        public static bool Publish(this Content entity, List<string> languages, ValidationOptions validation,
            ModelStateDictionary? modelState = null, bool forJobs = false, bool throwEx = true)
        {
            modelState ??= new ModelStateDictionary();
            if (!NeedsElevatedValidation(entity)) return true;

            if (validation.CheckLocalization.IsEnabled(entity.Type, forJobs))
                ValidateLocalizations(entity.Localizations, languages, modelState);

            if (validation.CheckImages.IsEnabled(entity.Type, forJobs))
                ValidateAssets(entity.Assets, validation?.CheckImages?.ImageTypes, "--", modelState);

            if (validation?.CheckStreams.IsEnabled(entity.Type, forJobs) == true)
                ValidateAssets(entity.Assets, validation.CheckStreams?.StreamTypes, null, modelState);

            if ((validation?.CheckDuration.IsEnabled(entity.Type, forJobs) ?? false) && entity.Duration <= 0)
                modelState.AddModelError(nameof(entity.Duration), "Duration must be specified");

            var builder = new StringBuilder($"Cannot update content with id {entity.Id} because of validation errors:");
            var entries = modelState.SelectMany(kvp => kvp.Value.Errors.Select(e => new { kvp.Key, e.ErrorMessage }));
            foreach (var item in entries) builder.Append(delim + $"{item.Key}: {item.ErrorMessage}");

            if (!modelState.IsValid && throwEx) throw new ContentValidationException(builder.ToString());
            return modelState.IsValid;
        }

        private static void ValidateLocalizations(Dictionary<string, Localization>? localizations,
            List<string> languages, ModelStateDictionary modelState)
        {
            foreach (var lang in languages)
            {
                var path = $"{nameof(Content.Localizations)}.{lang}";
                if (localizations == null || !localizations.TryGetValue(lang, out var entry))
                {
                    modelState.AddModelError(path, $"{lang} localization must be specified");
                }
                else
                {
                    if (string.IsNullOrEmpty(entry.Name))
                        modelState.AddModelError($"{path}.{nameof(entry.Name)}", "Must have value");
                    if (string.IsNullOrEmpty(entry.ShortInfo) && string.IsNullOrEmpty(entry.Description))
                    {
                        modelState.AddModelError($"{path}.{nameof(entry.ShortInfo)}",
                            $"Must have value if '{nameof(entry.Description)}' is not specified.");
                        modelState.AddModelError($"{path}.{nameof(entry.Description)}",
                            $"Must have value if '{nameof(entry.ShortInfo)}' is not specified.");
                    }
                }
            }
        }

        private static void ValidateAssets(List<Asset>? assets, List<List<SubType>>? subtypeList, string? lang,
            ModelStateDictionary modelState)
        {
            assets ??= new List<Asset>();
            subtypeList ??= new List<List<SubType>>();
            foreach (var subList in subtypeList)
            {
                if (subList.Count == 0) continue;
                if (!assets.Any(a =>
                        subList.Contains(a.SubType) && (string.IsNullOrEmpty(lang) || MatchingLocale(a.Locale, lang)) &&
                        !a.IsDeleted && !string.IsNullOrWhiteSpace(a.ObjectUrl + a.PublicUrl)))
                {
                    var subtypes = subList.Count == 1
                        ? $"a {subList[0]}"
                        : $"either {string.Join(", ", subList.Take(subList.Count - 1))}, or {subList[^1]}";
                    modelState.AddModelError(nameof(Content.Assets),
                        $"Must contain {subtypes} subtyped asset with ObjectUrl or PublicUrl specified.");
                }
            }
        }

        private static bool NeedsElevatedValidation(Content content)
        {
            if (!content.Published) return false;
            if (content.Type == ContentType.Advertisement) return false;
            return true;
        }

        private static bool MatchingLocale(string? assetLocale, string expected)
        {
            if (expected != "--" && assetLocale == expected) return true;
            return string.IsNullOrEmpty(assetLocale) || assetLocale == expected;
        }
    }
}