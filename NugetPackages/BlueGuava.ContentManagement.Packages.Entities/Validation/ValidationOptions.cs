﻿using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Packages.Entities.Validation
{
    public class ValidationOptions
    {
        public FeatureValidation? CheckLocalization { get; set; }

        public ContentImageValidation? CheckImages { get; set; }

        public ContentStreamsValidation? CheckStreams { get; set; }

        public FeatureValidation? CheckDuration { get; set; }
    }

    public class FeatureValidation
    {
        public bool Enabled { get; set; }
        public bool ForJobs { get; set; }
        public List<ContentType>? ForTypes { get; set; }
    }

    public class ContentImageValidation : FeatureValidation
    {
        public List<List<SubType>>? ImageTypes { get; set; }
    }

    public class ContentStreamsValidation : FeatureValidation
    {
        public List<List<SubType>>? StreamTypes { get; set; }
    }

    public static class ValidationSwitchExtension
    {
        // not `null`, is enabled, and (ForContent) list is `null` or contains type
        public static bool IsEnabled(this FeatureValidation? validation, ContentType type, bool forJobs)
        {
            return validation != null && validation.Enabled && (!forJobs || validation.ForJobs)
                   && (validation?.ForTypes == null || validation.ForTypes.Contains(type));
        }
    }
}