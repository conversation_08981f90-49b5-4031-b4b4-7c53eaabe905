﻿using System;

namespace BlueGuava.ContentManagement.Packages.Entities.DrmEntities
{
    public class DrmEntry
    {
        public StreamKind StreamKind { get; set; }

        public string? ContentUrl { get; set; }
        public DrmKind DrmKind { get; set; }
        public string? DrmKeyId { get; set; }
        public string? DrmContentKey { get; set; }

        public WorkflowStatus WorkflowStatus { get; set; }
        public DateTime ChangeDate { get; set; }
        public string? ErrorMessage { get; set; }
        public string? NotificationEmail { get; set; }
    }
}