﻿<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="../BlueGuava.ContentManagement.PackagesVersion.csproj" />

    <PropertyGroup>
        <Description>Content V2 entity classes</Description>
        <Authors>Blue Guava Technology</Authors>
        <Company>Blue Guava Technology</Company>
        <Copyright>Blue Guava Technology</Copyright>
        <LangVersion>12</LangVersion>
        <Nullable>enable</Nullable>
        <TargetFrameworks>net8.0</TargetFrameworks>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BlueGuava.Library.Constants" Version="8.1.4" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.ContentToken\BlueGuava.ContentManagement.ContentToken.csproj" />
    </ItemGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

</Project>
