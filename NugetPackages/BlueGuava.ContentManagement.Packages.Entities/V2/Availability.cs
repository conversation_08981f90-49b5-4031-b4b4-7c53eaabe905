﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class Availability
    {
        public List<string>? AdvisoryCodes { get; set; } = new List<string>();

        /// <summary>
        /// Represents an <see cref="int"/> value of the content age limit
        /// </summary>
        public int AgeLimit { get; set; }

        /// <summary>
        /// Represents a <see cref="DateTime"/> when the content availability starts
        /// </summary>
        public DateTime? AvailableFrom { get; set; }

        /// <summary>
        /// Represents a <see cref="DateTime"/> when the content availability ends
        /// </summary>
        public DateTime? AvailableUntil { get; set; }

        public string[]? Cities { get; set; }

        public Availability ShallowCopy()
        {
            return (Availability)MemberwiseClone();
        }

        public Availability RecursiveCopy()
        {
            var copy = (Availability)MemberwiseClone();
            copy.AdvisoryCodes = AdvisoryCodes?.ToList() ?? new List<string>();
            return copy;
        }
    }
}