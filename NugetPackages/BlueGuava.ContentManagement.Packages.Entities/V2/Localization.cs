﻿namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class Localization
    {
        /// <value>
        /// The property represents a <see cref="string"/> value of the content's name, localized
        /// </value>
        [Translatable]
        public string? Name { get; set; }

        /// <value>
        /// The property represents a <see cref="string"/> value of the content's short description, localized
        /// </value>
        [Translatable]
        public string? ShortInfo { get; set; }

        /// <value>
        /// The property represents a <see cref="string"/> value of the content's description, localized
        /// </value>
        [Translatable]
        public string? Description { get; set; }

        public bool B64 { get; set; }

        public Localization ShallowCopy()
        {
            return (Localization)MemberwiseClone();
        }
    }
}