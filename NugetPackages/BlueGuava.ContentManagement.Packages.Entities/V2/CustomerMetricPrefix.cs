﻿namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public static class ContentMetricPrefix
    {
        public const string PlaybackHit = "Playback:Hit:";
        public const string PlaybackPopularity = "Playback:Popularity:";
        public const string PlaybackSpentTime = "Playback:SpentTime:";
        public const string PlaybackPercentage = "Playback:Percentage:";
        public const string Like = "Likes:";
        public const string DisLike = "Dislikes:";
        public const string Rank = "Rank:";
        public const string Vote = "Vote:";
        public const string Review = "Review:";
        public const string UniqueViewer = "UniqueViewer:";
        public const string ChatMessage = "Message:";
        public const string Participant = "Participant:";
        public const string LastActivity = "LastActivity";
        public const string Hit = "Hit:";
        public const string Played = "Played:";
        public const string SpentTime = "SpentTime:";
    }

    public static class TimeFramePrefix
    {
        public const string Day = "1Day";
        public const string Week = "7Day";
        public const string Month = "30Day";
        public const string Total = "Total";
    }
}