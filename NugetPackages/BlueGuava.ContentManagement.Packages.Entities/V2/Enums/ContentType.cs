﻿namespace BlueGuava.ContentManagement.Packages.Entities.V2.Enums
{
    public enum ContentType
    {
        None = 0,

        Video = 1,
        Audio = 2,
        Trailer = 3,
        Advertisement = 4,
        LiveStream = 5,
        Podcast = 6,
        Playlist = 7, //Collection
        Image = 8,
        Collection = 9, //Collection
        Text = 10,
        Pdf = 11,
        Word = 12,

        File = 13,
        Merchandise = 14,
        SmartContract = 15,
        ArFile = 16, // augmented reality
        VrStream = 17, // virtual reality

        Series = 18,
        Season = 19, // ReferenceId => Series.ExternalId
        Episode = 20, // ReferenceId => Season.ExternalId

        Remix = 21, //Collection
        CameraCapture = 22,
        ScreenCapture = 23,
        LiveChat = 24,

        //[THINAIR-1716]
        UnityWeb = 25,
        UnrealWeb = 26,
        RemixVideo = 27,
        Poll = 28,

        RemixV2 = 29,

        Form = 30,



    }
}