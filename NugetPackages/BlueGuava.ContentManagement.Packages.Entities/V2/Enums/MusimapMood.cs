﻿using System.Runtime.Serialization;

namespace BlueGuava.ContentManagement.Packages.Entities.V2.Enums
{
    public enum MusimapMood
    {
        [EnumMember(Value = "M00")] Unknown = 0,

        [EnumMember(Value = "M11")] UpAir_Happiness = 11,
        [EnumMember(Value = "M12")] UpAir_Dynamism = 12,
        [EnumMember(Value = "M13")] UpAir_Temperature = 13,

        [EnumMember(Value = "M21")] AboveFire_Imagination = 21,
        [EnumMember(Value = "M22")] AboveFire_SelfControl = 22,
        [EnumMember(Value = "M23")] AboveFire_Spirituality = 23,

        [EnumMember(Value = "M31")] DownMetal_Coldness = 31,
        [EnumMember(Value = "M32")] DownMetal_Sensibility = 32,
        [EnumMember(Value = "M33")] DownMetal_Withdrawal = 33,

        [EnumMember(Value = "M41")] WithinWater_Love = 41,
        [EnumMember(Value = "M42")] WithinWater_Nourishment = 42,
        [EnumMember(Value = "M43")] WithinWater_Intellect = 43,

        [EnumMember(Value = "M51")] OnGround_Playfulness = 51,
        [EnumMember(Value = "M52")] OnGround_Warrior = 52,
        [EnumMember(Value = "M53")] OnGround_Roots = 53,

        [EnumMember(Value = "M61")] OutWood_GoodVibes = 61,
        [EnumMember(Value = "M62")] OutWood_Manliness = 62,
        [EnumMember(Value = "M63")] OutWood_Extroversion = 63
    }
}