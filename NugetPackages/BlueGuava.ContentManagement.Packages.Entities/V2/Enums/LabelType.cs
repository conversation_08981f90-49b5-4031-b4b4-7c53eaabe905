﻿namespace BlueGuava.ContentManagement.Packages.Entities.V2.Enums
{
    public enum LabelType
    {
        None = 0,

        Genre = 1,
        Category = 2,
        Location = 3,
        Album = 4,

        //[EN-9500]
        Place = 5,
        Person = 6,
        Keyword = 7,
        Mood = 8,
        Tag = 9,

        Attribute = 10,
        Skill = 11,
        Showreel = 12,
        Experience = 13,

        Type = 14,
        Style = 15,
        Instruction = 16,

        //MARKER tags for search
        CaptionTag = 100, // 0x00000064
        CelebrityTag = 101, // 0x00000065
        ContentModerationTag = 102, // 0x00000066
        FaceTag = 103, // 0x00000067
        LabelTag = 104, // 0x00000068
        PersonTrackingTag = 105, // 0x00000069
        SubtitleTag = 106, // 0x0000006A
        EmotionTag = 107, // 0x0000006B
        CutTag = 108, // 0x0000006C
        NFTTag = 109, // 0x0000006D
        DynamicTag = 110 // 0x0000006E
    }
}