using System.Collections.Generic;
using System.Linq;

namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class Credit
    {
        public string? Name { get; set; }
        public List<Role> Roles { get; set; } = new List<Role>();
        public string? Character { get; set; }

        public Credit ShallowCopy()
        {
            return (Credit)MemberwiseClone();
        }

        public Credit RecursiveCopy()
        {
            var result = (Credit)MemberwiseClone();
            result.Roles = Roles?.ToList() ?? new List<Role>();
            return result;
        }
    }

    public enum Role
    {
        Director = 1,
        Writer = 2,
        Producer = 3,
        Cast = 4,
        Photography = 5,
        Production = 6,
        Editor = 7,
        Costume = 8,
        Music = 9,
        Casting = 10,
        Effects = 11,
        Stunts = 12,
        Crew = 13
    }
}