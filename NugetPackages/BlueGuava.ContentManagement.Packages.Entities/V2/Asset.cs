﻿using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class Asset
    {
        /// <summary>
        /// Represents a <see cref="Guid"/> unique id of the asset
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// Represents the <see cref="AssetType" /> of the asset
        /// </summary>
        public AssetType Type { get; set; }

        /// <summary>
        /// Represents the <see cref="Enums.SubType" /> of the asset
        /// </summary>
        public SubType SubType { get; set; }

        /// <summary>
        /// Represents the <see cref="DrmEntities.WorkflowStatus"/> of the asset
        /// </summary>
        public WorkflowStatus WorkflowStatus { get; set; }

        /// <summary>
        ///  Represents the
        ///  <see href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-lifecycle-mgmt.html">S3 lifecycle policies</see>
        ///  attached to the <see cref="Asset"/>
        /// </summary>
        public List<string>? LifeCyclePolicies { get; set; } = new List<string>();

        /// <summary>
        /// Represents a <see cref="string"/> value of the public endpoint where the <see cref="Asset">Asset</see> is available
        /// </summary>
        public string? PublicUrl { get; set; }

        /// <summary>
        /// Represents a <see cref="string"/> value of the 
        /// </summary>
        public string? ObjectUrl { get; set; }

        /// <summary>
        /// Represents a <see cref="string"/> value of the restored S3 file from
        /// <see href="https://aws.amazon.com/s3/glacier/">S3 Glacier</see>
        /// </summary>
        public string? RestoredObjectUrl { get; set; }

        /// <summary>
        /// Represents a <see cref="string"/> value of the LanguageCode of the asset
        /// </summary>
        public string? Locale { get; set; }

        /// <summary>
        /// Represents an <see cref="string"/> value of the duration of the <see cref="Asset">Asset</see>
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// The property represents a <see cref="DateTime"/> when the <see cref="Asset">Asset</see> was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// The property represents a <see cref="DateTime"/> when the <see cref="Asset">Asset</see> was last modified
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// Calculated url in the controller for asset download
        /// </summary>
        public string? DownloadUrl { get; set; }

        /// <summary>
        /// Support logical delete True if the asset is hidden
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Size of the file in kilobytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Signals whether the asset is public or not
        /// </summary>
        public bool IsPublic { get; set; }

        /// <summary>
        /// An IPFS Hash links web files to your domain. <br/>
        /// Users can build, design, and code from scratch their website. <br/>
        /// In these instances, the user manually pins their website files to an IPFS node, creating an IPFS hash.
        /// </summary>
        public string? IpfsHash { get; set; }

        /// <summary>
        /// Filename computed from ingest or from <see cref="ObjectUrl"/>
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// Uploader user id from the authorization token
        /// </summary>
        public string? UploaderUserId { get; set; }

        /// <summary>
        /// Contains the CloudFront CDN Url with token 
        /// </summary>
        public string? TokenizedCdnUrl { get; set; }

        /// <summary>
        /// Indicates whether content was uploaded by user or other service
        /// </summary>
        public bool UploadedByUser { get; set; }

        public Asset ShallowCopy()
        {
            return (Asset)MemberwiseClone();
        }

        public Asset RecursiveCopy()
        {
            var copy = (Asset)MemberwiseClone();
            copy.LifeCyclePolicies = LifeCyclePolicies?.ToList() ?? new List<string>();
            return copy;
        }
    }
}