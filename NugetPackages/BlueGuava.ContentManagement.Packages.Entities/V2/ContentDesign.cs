﻿namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class ContentDesign
    {
        public string? MainTextColor { get; set; }
        public string? ShortInfoColor { get; set; }
        public string? ArtistTextColor { get; set; }
        public string? BackgroundColor { get; set; }

        public void UpdateWith(ContentDesign other)
        {
            MainTextColor = other.MainTextColor;
            ShortInfoColor = other.ShortInfoColor;
            ArtistTextColor = other.ArtistTextColor;
            BackgroundColor = other.BackgroundColor;
        }

        public ContentDesign ShallowCopy()
        {
            return (ContentDesign)MemberwiseClone();
        }
    }
}