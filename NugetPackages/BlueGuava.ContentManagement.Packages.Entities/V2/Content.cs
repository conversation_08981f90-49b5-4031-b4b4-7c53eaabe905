﻿using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Library;

namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class Content : IEntity
    {
        [BaseData]
        [ExportData]
        public Guid Id { get; set; }

        [BaseData]
        [ExportData]
        public ContentType Type { get; set; }

        [BaseData]
        [ExportData]
        public string? Color { get; set; }

        public List<Credit>? Credits { get; set; }

        public Dictionary<string, Localization>? Localizations { get; set; }

        [BaseData]
        public Dictionary<string, Availability>? ExhibitionWindow { get; set; }

        public Dictionary<DesignTypes, ContentDesign>? Themes { get; set; }

        public List<Asset>? Assets { get; set; }

        [BaseData]
        [ExportData]
        public DateTime? PublishedDate { get; set; }

        [BaseData]
        [ExportData]
        public DateTime? ReleaseDate { get; set; }

        [BaseData]
        [ExportData]
        public DateTime? ForceIndexed { get; set; }

        [BaseData]
        [ExportData]
        public bool? Duplicate { get; set; }

        [BaseData]
        [ExportData]
        public bool? Downloadable { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowRemix { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowMinting { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowEmailNotification { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowComments { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowUserRating { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowSideshow { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowLyrics { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowUpcoming { get; set; }

        [BaseData]
        [ExportData]
        public bool Published { get; set; }

        [BaseData]
        [ExportData]
        public Guid OwnerId { get; set; }

        [BaseData]
        [ExportData]
        public Guid LastModifiedBy { get; set; }

        [BaseData]
        [ExportData]
        public DateTime LastModifiedDate { get; set; }

        [BaseData]
        [ExportData]
        public DateTime CreatedDate { get; set; }

        public List<Guid>? AuthGroupIds { get; set; }

        public Dictionary<string, string>? Properties { get; set; }

        [BaseData]
        [ExportData]
        public string? ExternalId { get; set; }

        [BaseData]
        [ExportData]
        public string? ReferenceId { get; set; }

        [BaseData]
        [ExportData]
        public string? OriginalTitle { get; set; }

        [BaseData]
        [ExportData]
        public string? OriginalFileName { get; set; }

        [BaseData]
        [ExportData]
        public int? Duration { get; set; }

        [BaseData]
        [ExportData]
        public string? OriginalLanguage { get; set; }
        [BaseData]
        [ExportData]
        public string? OriginalTranscript { get; set; }

        [BaseData]
        [ExportData]
        public string? ArchivalPolicy { get; set; }

        [BaseData]
        [ExportData]
        public string? DeletionPolicy { get; set; }

        [BaseData]
        public Dictionary<EntityType, List<string>>? Entities { get; set; }

        [BaseData]
        public List<WhereToWatch>? WhereToWatch { get; set; }

        [BaseData]
        [ExportData]
        public int? InternalPrice { get; set; }
        [BaseData]
        [ExportData]
        public double? TokenPrice { get; set; }
        [BaseData]
        [ExportData]
        public string? TokenCurrency { get; set; }

        [BaseData]
        [ExportData]
        public ProcessingStatus? ProcessingStatus { get; set; }

        [BaseData]
        [ExportData]
        public bool? AllowChat { get; set; }

        [BaseData]
        [ExportData]
        public Visibility? Visibility { get; set; } = Enums.Visibility.Public;

        [BaseData]
        public Dictionary<LabelType, List<string>>? Labels { get; set; }

        [BaseData]
        [ExportData]
        public bool? HasTags { get; set; }

        [BaseData]
        [ExportData]
        public bool? HasSubtitle { get; set; }

        [BaseData]
        [ExportData]
        public PublishingRule? PublishingRule { get; set; } = Enums.PublishingRule.Upcoming;

        [BaseData]
        [ExportData]
        public ContentNotification? Notification { get; set; } = ContentNotification.Followed;

        [BaseData]
        [ExportData]
        public DateTime? AutoPublishDate { get; set; }
        [BaseData]
        [ExportData]
        public DateTime? AutoUnPublishDate { get; set; }

        [BaseData]
        [ExportData]
        public bool? IsDeleted { get; set; }

        [BaseData]
        [ExportData]
        public bool? IsDrmEnabled { get; set; }

        public List<string>? Relations { get; set; }

        [BaseData]
        [ExportData]
        public string? RewardCode { get; set; }

        [BaseData]
        [ExportData]
        public Guid? OriginalOwnerId { get; set; }

        [BaseData]
        [ExportData]
        public bool? IsRewarded { get; set; }

        public string? PollHtml { get; set; }

        public Content()
        {
            Id = Guid.NewGuid();
            Assets = new List<Asset>();
            AuthGroupIds = new List<Guid>();
            Credits = new List<Credit>();
            Entities = new Dictionary<EntityType, List<string>>();
            ExhibitionWindow = new Dictionary<string, Availability>();
            Localizations = new Dictionary<string, Localization>();
            Properties = new Dictionary<string, string>();
            Themes = new Dictionary<DesignTypes, ContentDesign>();
            WhereToWatch = new List<WhereToWatch>();
            CreatedDate = DateTime.UtcNow;
            LastModifiedDate = CreatedDate;
            Labels = new Dictionary<LabelType, List<string>>();
        }

        public void UpdateWith(Content other, Guid userId, bool maintenance)
        {
            /* this.Id = other.Id; // IDs must be the same */
            Type = other.Type;
            AllowUserRating = other.AllowUserRating;
            AllowComments = other.AllowComments;
            AllowMinting = other.AllowMinting;
            AllowEmailNotification = other.AllowEmailNotification;
            AllowChat = other.AllowChat;
            AllowRemix = other.AllowRemix;
            AllowSideshow = other.AllowSideshow;
            AllowLyrics = other.AllowLyrics;
            AllowUpcoming = other.AllowUpcoming;
            Assets = other.Assets ?? new List<Asset>();
            AuthGroupIds = other.AuthGroupIds ?? new List<Guid>();
            Color = other.Color;
            Credits = other.Credits ?? new List<Credit>();
            Downloadable = other.Downloadable;
            Duplicate = other.Duplicate;
            Duration = other.Duration;
            ExhibitionWindow = other.ExhibitionWindow;
            ExternalId = other.ExternalId;

            Localizations = other.Localizations ?? new Dictionary<string, Localization>();
            OriginalFileName = other.OriginalFileName;
            OriginalTitle = other.OriginalTitle;
            OwnerId = other.OwnerId == default ? OwnerId : other.OwnerId;
            Properties = other.Properties ?? new Dictionary<string, string>();
            Published = other.Published;
            PublishedDate = other.PublishedDate;
            ReferenceId = other.ReferenceId;
            ReleaseDate = other.ReleaseDate;
            Themes = other.Themes;
            OriginalLanguage = other.OriginalLanguage;
            OriginalTranscript = other.OriginalTranscript;
            ArchivalPolicy = other.ArchivalPolicy;
            DeletionPolicy = other.DeletionPolicy;
            Entities = other.Entities;
            WhereToWatch = other.WhereToWatch;
            InternalPrice = other.InternalPrice;
            TokenPrice = other.TokenPrice;
            TokenCurrency = other.TokenCurrency;
            ProcessingStatus = other.ProcessingStatus;
            Visibility = other.Visibility;
            Labels = other.Labels ?? new Dictionary<LabelType, List<string>>();
            HasTags = other.HasTags;
            HasSubtitle = other.HasSubtitle;
            PublishingRule = other.PublishingRule;
            Notification = other.Notification;
            this.Relations = other.Relations;
            AutoPublishDate = other.AutoPublishDate;
            AutoUnPublishDate = other.AutoUnPublishDate;
            IsDeleted = other.IsDeleted;
            IsDrmEnabled = other.IsDrmEnabled;
            RewardCode = other.RewardCode;
            OriginalOwnerId = other.OriginalOwnerId;
            IsRewarded = other.IsRewarded;
            PollHtml = other.PollHtml;

            var hasSubtitle = (Labels?.ContainsKey(LabelType.SubtitleTag) ??
                              false) && (Labels[LabelType.SubtitleTag]?.Any() ?? false);

            if (Properties.ContainsKey(Constants.CONTENT_HASSUBTITLE)) Properties[Constants.CONTENT_HASSUBTITLE] = hasSubtitle.ToString();
            else this?.Properties?.Add(Constants.CONTENT_HASSUBTITLE, hasSubtitle.ToString());

            //if there is any celebrity or label tag on the content
            var hasTags = (Labels?.ContainsKey(LabelType.CelebrityTag) ??
                           false) && (Labels[LabelType.CelebrityTag]?.Any() ?? false) ||
                          (Labels?.ContainsKey(LabelType.LabelTag) ??
                           false) && (Labels[LabelType.LabelTag]?.Any() ?? false);

            Properties ??= new Dictionary<string, string>();
            if (Properties.ContainsKey(Constants.CONTENT_HASTAGS)) Properties[Constants.CONTENT_HASTAGS] = hasTags.ToString();
            else this?.Properties?.Add(Constants.CONTENT_HASTAGS, hasTags.ToString());

            if (!maintenance)
            {
                LastModifiedBy = userId;
                LastModifiedDate = DateTime.UtcNow;
            }
        }

        public void PartialUpdateWith(Content other, Guid userId, bool maintenance, int strategy = 1)
        {
            /* this.Id = other.Id; // IDs must be the same */
            Type = CheckAndReturn<ContentType>(Type, (other.Type == ContentType.None ? Type : other.Type));
            AllowUserRating = CheckAndReturn<bool?>(AllowUserRating, other.AllowUserRating);
            AllowComments = CheckAndReturn<bool?>(AllowComments, other.AllowComments);
            AllowMinting = CheckAndReturn<bool?>(AllowMinting, other.AllowMinting);
            AllowEmailNotification = CheckAndReturn<bool?>(AllowEmailNotification, other.AllowEmailNotification);
            AllowChat = CheckAndReturn<bool?>(AllowChat, other.AllowChat);
            AllowRemix = CheckAndReturn<bool?>(AllowRemix, other.AllowRemix);
            AllowSideshow = CheckAndReturn<bool?>(AllowSideshow, other.AllowSideshow);
            AllowLyrics = CheckAndReturn<bool?>(AllowLyrics, other.AllowLyrics);
            AllowUpcoming = CheckAndReturn<bool?>(AllowUpcoming, other.AllowUpcoming);

            Assets = UpdateAssets(Assets, other.Assets) ?? new List<Asset>();
            AuthGroupIds = UpdateList<Guid>(AuthGroupIds, other.AuthGroupIds) ?? new List<Guid>();
            Color = CheckAndReturn<string>(Color, other.Color);
            Credits = UpdateCredits(Credits, other.Credits);
            Downloadable = CheckAndReturn<bool?>(Downloadable, other.Downloadable);
            Duplicate = CheckAndReturn<bool?>(Duplicate, other.Duplicate);
            Duration = CheckAndReturn<int?>(Duration, other.Duration);
            ExhibitionWindow = UpdateDictionary<string, Availability>(ExhibitionWindow, other.ExhibitionWindow);
            ExternalId = CheckAndReturn<string>(ExternalId, other.ExternalId);
            Localizations = UpdateLocalization(Localizations, other.Localizations) ?? new Dictionary<string, Localization>();
            OriginalFileName = CheckAndReturn<string>(OriginalFileName, other.OriginalFileName);
            OriginalTitle = CheckAndReturn<string>(OriginalTitle, other.OriginalTitle);
            OwnerId = other.OwnerId == default ? OwnerId : other.OwnerId;
            Properties = UpdateDictionary<string, string>(Properties, other.Properties) ?? new Dictionary<string, string>();
            Published = CheckAndReturn<bool>(Published, other.Published);
            PublishedDate = CheckAndReturn<DateTime?>(PublishedDate, other.PublishedDate);
            ReferenceId = CheckAndReturn<string>(ReferenceId, other.ReferenceId);
            ReleaseDate = CheckAndReturn<DateTime?>(ReleaseDate, other.ReleaseDate);
            Themes = UpdateDictionary<DesignTypes, ContentDesign>(Themes, other.Themes);
            OriginalLanguage = CheckAndReturn<string>(OriginalLanguage, other.OriginalLanguage);
            OriginalTranscript = CheckAndReturn<string>(OriginalTranscript, other.OriginalTranscript);
            ArchivalPolicy = CheckAndReturn<string>(ArchivalPolicy, other.ArchivalPolicy);
            DeletionPolicy = CheckAndReturn<string>(DeletionPolicy, other.DeletionPolicy);
            Entities = UpdateDictionary<EntityType, List<string>>(Entities, other.Entities);
            WhereToWatch = UpdateWhereToWatch(WhereToWatch, other.WhereToWatch);
            InternalPrice = CheckAndReturn<int?>(InternalPrice, other.InternalPrice);
            TokenPrice = CheckAndReturn<double?>(TokenPrice, other.TokenPrice);
            TokenCurrency = CheckAndReturn<string>(TokenCurrency, other.TokenCurrency);
            ProcessingStatus = CheckAndReturn<ProcessingStatus?>(ProcessingStatus, other.ProcessingStatus);
            Visibility = CheckAndReturn<Visibility?>(Visibility, other.Visibility);

            Labels = strategy switch
            {
                0 => other.Labels,
                2 => DeleteDictionary<LabelType, List<string>>(Labels, other.Labels),
                _ => UpdateDictionary<LabelType, List<string>>(Labels, other.Labels)
            }; // ?? new Dictionary<LabelType, List<string>>();

            HasTags = CheckAndReturn<bool?>(HasTags, other.HasTags);
            HasSubtitle = CheckAndReturn<bool?>(HasSubtitle, other.HasSubtitle);
            PublishingRule = CheckAndReturn<PublishingRule?>(PublishingRule, other.PublishingRule);
            Notification = CheckAndReturn<ContentNotification?>(Notification, other.Notification);
            AutoPublishDate = CheckAndReturn<DateTime?>(AutoPublishDate, other.AutoPublishDate);
            AutoUnPublishDate = CheckAndReturn<DateTime?>(AutoUnPublishDate, other.AutoUnPublishDate);
            IsDeleted = CheckAndReturn<bool?>(IsDeleted, other.IsDeleted);
            IsDrmEnabled = CheckAndReturn<bool?>(IsDrmEnabled, other.IsDrmEnabled);
            RewardCode = CheckAndReturn<string>(RewardCode, other.RewardCode);
            IsRewarded = CheckAndReturn<bool?>(IsRewarded, other.IsRewarded);
            PollHtml = other.PollHtml;

            var hasSubtitle = (Labels?.ContainsKey(LabelType.SubtitleTag) ??
                              false) && (Labels[LabelType.SubtitleTag]?.Any() ?? false);

            if (Properties.ContainsKey(Constants.CONTENT_HASSUBTITLE)) Properties[Constants.CONTENT_HASSUBTITLE] = hasSubtitle.ToString();
            else this?.Properties?.Add(Constants.CONTENT_HASSUBTITLE, hasSubtitle.ToString());

            //if there is any celebrity or label tag on the content
            var hasTags = (Labels?.ContainsKey(LabelType.CelebrityTag) ??
                           false) && (Labels[LabelType.CelebrityTag]?.Any() ?? false) ||
                          (Labels?.ContainsKey(LabelType.LabelTag) ??
                           false) && (Labels[LabelType.LabelTag]?.Any() ?? false);

            Properties ??= new Dictionary<string, string>();
            if (Properties.ContainsKey(Constants.CONTENT_HASTAGS)) Properties[Constants.CONTENT_HASTAGS] = hasTags.ToString();
            else this?.Properties?.Add(Constants.CONTENT_HASTAGS, hasTags.ToString());

            if (!maintenance)
            {
                LastModifiedBy = userId;
                LastModifiedDate = DateTime.UtcNow;
            }
        }

        //private Dictionary<LabelType, List<string>> UpdateLabels(Dictionary<LabelType, List<string>> labels) =>


        private Dictionary<string, Localization> UpdateLocalization(Dictionary<string, Localization> oldLocalization, Dictionary<string, Localization> newLocalization)
        {
            if (newLocalization == null) return oldLocalization;

            oldLocalization ??= new Dictionary<string, Localization>();
            foreach (var item in newLocalization)
            {
                if (oldLocalization.ContainsKey(item.Key))
                {
                    oldLocalization[item.Key] = item.Value;
                }
                else
                {
                    oldLocalization.Add(item.Key, item.Value);
                }
            }
            return oldLocalization;
        }

        private List<Asset> UpdateAssets(List<Asset> oldAssets, List<Asset> newAssets)
        {
            if (newAssets == null) return oldAssets;

            oldAssets ??= new List<Asset>();
            foreach (var item in newAssets)
            {
                var asset = oldAssets.FirstOrDefault(s => s.Id == item.Id);
                if (asset != null)
                {
                    var index = oldAssets.IndexOf(asset);
                    oldAssets[index] = item;
                }
                else
                {
                    oldAssets.Add(item);
                }
            }
            return oldAssets;
        }

        private static List<T> UpdateList<T>(List<T> oldList, List<T> newList)
        {
            if (newList == null) return oldList;

            oldList ??= new List<T>();
            foreach (var item in newList)
            {
                var existingItem = oldList.FirstOrDefault(x => x.Equals(item));
                if (existingItem != null)
                {
                    var index = oldList.IndexOf(existingItem);
                    oldList[index] = item;
                }
                else
                {
                    oldList.Add(item);
                }
            }
            return oldList;
        }

        private static List<Credit> UpdateCredits(List<Credit> oldList, List<Credit> newList)
        {
            if (newList == null) return oldList;

            oldList ??= new List<Credit>();
            foreach (var item in newList)
            {
                var existingItem = oldList.FirstOrDefault(x => x.Name?.Equals(item.Name) == true);
                if (existingItem != null)
                {
                    var index = oldList.IndexOf(existingItem);
                    oldList[index] = item;
                }
                else
                {
                    oldList.Add(item);
                }
            }
            return oldList;
        }

        private static List<WhereToWatch> UpdateWhereToWatch(List<WhereToWatch> oldList, List<WhereToWatch> newList)
        {
            if (newList == null) return oldList;

            oldList ??= new List<WhereToWatch>();
            foreach (var item in newList)
            {
                var existingItem = oldList.FirstOrDefault(x => x.ProviderName?.Equals(item.ProviderName) == true);
                if (existingItem != null)
                {
                    var index = oldList.IndexOf(existingItem);
                    oldList[index] = item;
                }
                else
                {
                    oldList.Add(item);
                }
            }
            return oldList;
        }

        private static Dictionary<TKey, TValue> UpdateDictionary<TKey, TValue>(Dictionary<TKey, TValue> oldDictionary, Dictionary<TKey, TValue> newDictionary)
        {
            if (newDictionary == null) return oldDictionary;

            oldDictionary ??= new Dictionary<TKey, TValue>();
            foreach (var item in newDictionary)
            {
                if (oldDictionary.ContainsKey(item.Key))
                {
                    if (typeof(TValue) == typeof(List<string>))
                    {
                        oldDictionary[item.Key] = (TValue)(object)((List<string>)(object)oldDictionary[item.Key]).Union((List<string>)(object)item.Value).ToList();
                    }
                    else
                    {
                        oldDictionary[item.Key] = item.Value;
                    }
                }
                else
                {
                    oldDictionary.Add(item.Key, item.Value);
                }
            }
            return oldDictionary;
        }

        private static Dictionary<TKey, TValue> DeleteDictionary<TKey, TValue>(Dictionary<TKey, TValue> oldDictionary, Dictionary<TKey, TValue> newDictionary)
        {
            if (newDictionary == null) return oldDictionary;

            oldDictionary ??= new Dictionary<TKey, TValue>();
            foreach (var item in newDictionary)
            {
                if (oldDictionary.ContainsKey(item.Key))
                {
                    if (typeof(TValue) == typeof(List<string>))
                    {
                        var oldItems = (List<string>)(object)oldDictionary[item.Key];
                        var newItems = (List<string>)(object)newDictionary[item.Key];

                        foreach (var n in newItems)
                        {
                            if (oldItems.Contains(n))
                            {
                                oldItems.Remove(n);
                            }
                        }
                    }
                }
            }
            return oldDictionary;
        }

        private static T CheckAndReturn<T>(T value1, T value2)
        {
            if (value2 == null) return value1;
            if (EqualityComparer<T>.Default.Equals(value1, value2))
            {
                return value1;
            }
            else
            {
                return value2;
            }
        }

        public Content ShallowCopy() => (Content)MemberwiseClone();

        public Content RecursiveCopy()
        {
            var copy = (Content)MemberwiseClone();
            copy.Assets = Assets?.ConvertAll(e => e.RecursiveCopy());
            copy.AuthGroupIds = AuthGroupIds?.ToList();
            copy.Credits = Credits?.ConvertAll(e => e.RecursiveCopy());
            copy.Entities = Entities?.ToDictionary(e => e.Key, e => e.Value.ToList());
            copy.ExhibitionWindow = ExhibitionWindow?.ToDictionary(e => e.Key, e => e.Value.RecursiveCopy());
            copy.Localizations = Localizations?.ToDictionary(e => e.Key, e => e.Value.ShallowCopy());
            copy.Properties = Properties?.ToDictionary(e => e.Key, e => e.Value);
            copy.Themes = Themes?.ToDictionary(e => e.Key, e => e.Value.ShallowCopy());
            copy.WhereToWatch = WhereToWatch?.ConvertAll(e => e.ShallowCopy());
            copy.Labels = Labels?.ToDictionary(e => e.Key, e => e.Value.ToList());
            copy.Relations = Relations?.Select(e => e).ToList() ?? new List<string>();

            return copy;
        }


        public ContentToken.ContentToken GetToken() => new ContentToken.ContentToken
        {
            ContentId = Id,
            Duration = (int)Math.Ceiling((Duration ?? 0) / 1000.0),
            SkuIds = AuthGroupIds?.Select(x => x.ToString())
        };
    }
}
