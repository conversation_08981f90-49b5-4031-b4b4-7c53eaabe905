﻿using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Library;

namespace BlueGuava.ContentManagement.Packages.Entities.V2
{
    public class ContentPoll
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? ExternalId { get; set; }
        public string? ReferenceId { get; set; }
        public string? SelectedChoiceName { get; set; }
        public string? SelectedChoiceValue { get; set; }
        public string? CollectionName { get; set; }
        public string GetId() => $"P:{Guid.NewGuid()}";

        public ContentPoll()
        {
            Id = GetId();
        }
    }
}
