﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.ContentToken;

// TODO: use BlueGuava.Extensions.Security.SecretToken
// WARNING: Secret token requires configuration update:
// currently we use Encoding.UTF8.GetBytes, that package
// uses Convert.FromBase64String to transform secret
// string to AES.Key byte array
public sealed class ContentToken
{
    public ContentToken()
    {
    }

    public ContentToken(Guid contentId, int duration)
    {
        ContentId = contentId;
        Duration = duration;
    }

    public Guid ContentId { get; set; }
    public int Duration { get; set; }
    public IEnumerable<string>? SkuIds { get; set; } = new List<string>();


    public static ContentToken? Decode(string token, string secret)
    {
        try
        {
            if (string.IsNullOrEmpty(token)) return null;
            var buffer = Encoding.UTF8.GetString(DecodeToken(token, secret));
            return JsonConvert.DeserializeObject<ContentToken>(buffer);
        }
        catch
        {
            return null;
        }
    }

    public string Encode(string secret)
    {
        var json = JsonConvert.SerializeObject(this);
        var buffer = Encoding.UTF8.GetBytes(json);
        return EncodeToken(buffer, secret);
    }


    private static byte[] DecodeToken(string base64, string secret)
    {
        using var data = new MemoryStream();
        using (var token = new MemoryStream(Convert.FromBase64String(base64)))
        using (var cipher = CreateAesCipher(secret, token, false))
        using (var transform = cipher.CreateDecryptor())
        using (var crypto = new CryptoStream(token, transform, CryptoStreamMode.Read))
        using (var gzip = new GZipStream(crypto, CompressionMode.Decompress))
        {
            gzip.CopyTo(data);
        }

        return data.ToArray();
    }

    private static string EncodeToken(byte[] buffer, string secret)
    {
        using var token = new MemoryStream();
        using (var cipher = CreateAesCipher(secret, token, true))
        using (var transform = cipher.CreateEncryptor())
        using (var crypto = new CryptoStream(token, transform, CryptoStreamMode.Write))
        using (var gzip = new GZipStream(crypto, CompressionLevel.Optimal))
        using (var data = new MemoryStream(buffer))
        {
            data.CopyTo(gzip);
        }

        return Convert.ToBase64String(token.ToArray());
    }


    private static Aes CreateAesCipher(string secret, Stream stream, bool createIV)
    {
        var iv = new byte[16];
        if (createIV)
        {
            RandomNumberGenerator.Create().GetBytes(iv);
            stream.Write(iv, 0, iv.Length);
            stream.Position = 0;
        }

        var cipher = Aes.Create();
        cipher.Key = Encoding.UTF8.GetBytes(secret);
        stream.Read(iv, 0, iv.Length);
        cipher.IV = iv;

        return cipher;
    }
}