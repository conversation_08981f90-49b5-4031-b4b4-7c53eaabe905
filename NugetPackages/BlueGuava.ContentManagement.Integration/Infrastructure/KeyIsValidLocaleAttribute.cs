﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Reflection;

namespace BlueGuava.ContentManagement.Integration.Infrastructure;

public sealed class KeyIsValidLocaleAttribute : ValidationAttribute
{
    /// <summary>
    /// Marks whether empty value is accepted
    /// </summary>
    public bool AllowEmpty { get; set; }

    /// <summary>
    /// Marks whether the unknown language code '--' is accepted or not
    /// </summary>
    public bool AcceptUnknown { get; set; } = false;

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var type = value.GetType();
        if (!type.IsGenericType) throw new InvalidOperationException("Not a dictionary");
        if (type.GetGenericTypeDefinition() != typeof(Dictionary<,>))
            throw new InvalidOperationException("Not a dictionary");

        var keyType = type.GenericTypeArguments[0];
        if (keyType != typeof(string)) throw new InvalidOperationException("Dictionary key is not string");
        var entryType = typeof(KeyValuePair<,>).MakeGenericType(type.GenericTypeArguments);
        var keyField = entryType.GetProperty("Key", BindingFlags.Public | BindingFlags.Instance)
                       ?? throw new InvalidOperationException("Breaking change in KeyValuePair<,>");

        var issues = new List<string>();
        foreach (var entry in (ICollection)value)
        {
            var key = (string?)keyField.GetValue(entry);
            if (!ValidateLocale(key)) issues.Add(key ?? string.Empty);
        }

        return issues.Count == 0
            ? ValidationResult.Success
            : new ValidationResult("Not a valid language code", issues);
    }

    private bool ValidateLocale([NotNullWhen(true)] string? value)
    {
        try
        {
            if (string.IsNullOrEmpty(value)) return AllowEmpty;
            if (value == "--") return AcceptUnknown;
            var locale = CultureInfo.GetCultureInfo(value);
            // TODO: add property validatation (eg: LCID)
            return locale != null;
        }
        catch
        {
            return false;
        }
    }
}