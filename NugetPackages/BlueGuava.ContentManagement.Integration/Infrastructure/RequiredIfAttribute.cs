﻿using System;
using System.Collections;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;

namespace BlueGuava.ContentManagement.Integration.Infrastructure;

public sealed class RequiredIfAttribute : ValidationAttribute
{
    private readonly string methodName;

    public RequiredIfAttribute(string methodName, string? errorMessage = null)
    {
        this.methodName = methodName.TrimStart('@');
        ErrorMessage = string.IsNullOrEmpty(errorMessage)
            ? "The property must have a value"
            : errorMessage;
    }

    public int MinCount { get; set; } = 0;

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        const BindingFlags f = BindingFlags.Public | BindingFlags.NonPublic
                                                   | BindingFlags.Instance | BindingFlags.Static;

        var method = validationContext.ObjectType.GetMethods(f)
            .Where(m => !m.IsSpecialName).Where(m => m.Name == methodName).Where(m => m.ReturnType == typeof(bool))
            .Where(m => !m.GetParameters().Where(p => !p.ParameterType.IsAssignableFrom(validationContext.ObjectType))
                .Any())
            .OrderBy(m => m.GetParameters().Any()).ThenBy(m =>
                m.GetParameters().FirstOrDefault()?.ParameterType != validationContext.ObjectType)
            .FirstOrDefault() ?? throw new InvalidOperationException($"Invalid method name: {methodName}");

        var param = method.GetParameters().Any() ? new object[] { validationContext.ObjectInstance } : null;
        var isRequired = (bool)method.Invoke(method.IsStatic ? null : validationContext.ObjectInstance, param)!;
        if (!isRequired) return ValidationResult.Success;

        if (value == null) return new ValidationResult("The property must have value");
        if (value is string str && string.IsNullOrWhiteSpace(str))
            return new ValidationResult("The property must not be empty");
        if (value is ICollection coll && coll.Count < MinCount)
            return new ValidationResult($"Value must be at least {MinCount} long");
        if (IsNumber(value, out var number) && number < MinCount)
            return new ValidationResult($"The property must be greater than {MinCount - 1}");
        return ValidationResult.Success;
    }

    private static bool IsNumber(object value, out decimal result)
    {
        try
        {
            result = Convert.ToDecimal(value);
            return true;
        }
        catch
        {
            result = 0;
            return false;
        }
    }
}