﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace BlueGuava.ContentManagement.Integration.Infrastructure;

public sealed class IsValidLocaleAttribute : ValidationAttribute
{
    public IsValidLocaleAttribute()
    {
        ErrorMessage = "Not a valid language code";
    }

    public override bool IsValid(object? value)
    {
        var text = value as string;
        if (string.IsNullOrEmpty(text)) return true;
        return (AcceptUnknown && text == "--") || ValidateLocale(text);
    }

    /// <summary>
    /// Marks whether the unknown language code '--' is accepted or not
    /// </summary>
    public bool AcceptUnknown { get; set; } = false;

    private static bool ValidateLocale(string value)
    {
        try
        {
            var locale = CultureInfo.GetCultureInfo(value);
            // TODO: add property validatation (eg: LCID)
            return locale != null;
        }
        catch
        {
            return false;
        }
    }
}