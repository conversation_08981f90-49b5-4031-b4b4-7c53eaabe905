﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BlueGuava.ContentManagement.Integration.Infrastructure;

[AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
public sealed class AgeLimitAttribute : ValidationAttribute
{
    public AgeLimitAttribute() : base("Must be a number between 0 and 21, or equal to 99")
    {
    }

    public override bool IsValid(object? value)
    {
        if (value == null) return true;
        if (!(value is int num)) return false;
        return (num >= 0 && num <= 21) || num == 99;
    }
}