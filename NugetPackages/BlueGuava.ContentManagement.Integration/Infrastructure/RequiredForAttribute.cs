﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reflection;

namespace BlueGuava.ContentManagement.Integration.Infrastructure;

/// <summary>
/// Base type for <see cref="RequiredWhenAttribute"/> and <see cref="RequiredUnlessAttribute"/>
/// </summary>
public abstract class RequiredForAttribute : ValidationAttribute
{
    private readonly string propertyName;
    private readonly string valueList;

    protected RequiredForAttribute(string propertyName, string valueList)
    {
        this.propertyName = propertyName;
        this.valueList = valueList;
    }

    public int MinCount { get; set; } = 0;

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        var property = validationContext.ObjectType.GetProperty(propertyName)
                       ?? throw new InvalidOperationException($"Invalid dependent property name: {propertyName}");

        var propValue = property.GetValue(validationContext.ObjectInstance);
        var checkValues = Parse(property.PropertyType, valueList);
        if (!ShouldCheck(property.PropertyType, propValue, checkValues))
            return ValidationResult.Success;

        if (value == null) return new ValidationResult("The property must have value");
        if (value is string str && string.IsNullOrWhiteSpace(str))
            return new ValidationResult("The property must not be empty");
        if (value is ICollection coll && coll.Count < MinCount)
            return new ValidationResult($"Value must be at least {MinCount} long");
        if (value is long number && number < MinCount)
            return new ValidationResult($"The property must be greater than {MinCount - 1}");
        return ValidationResult.Success;
    }

    private bool ShouldCheck(Type type, object? value, object? valueListParam)
    {
        var flags = BindingFlags.Instance | BindingFlags.NonPublic;
        return (bool)GetType().GetMethods(flags)
            .Where(m => m.Name == nameof(ShouldCheck))
            .Where(m => m.IsGenericMethodDefinition)
            .Single().MakeGenericMethod(type)
            .Invoke(this, new[] { value, valueListParam })!;
    }

    protected abstract bool ShouldCheck<T>([MaybeNull] T value, T[] valueList);

    private static object? Parse(Type type, string valueList)
    {
        var arrayType = type.MakeArrayType();
        return Newtonsoft.Json.JsonConvert
            .DeserializeObject($"[{valueList}]", arrayType);
    }
}

public sealed class RequiredWhenAttribute : RequiredForAttribute
{
    public RequiredWhenAttribute(string propertyName, string valueList)
        : base(propertyName, valueList)
    {
    }

    protected override bool ShouldCheck<T>([MaybeNull] T value, T[] values)
    {
        var comparer = EqualityComparer<T>.Default;
        foreach (var item in values)
            if (comparer.Equals(item, value))
                return true;
        return false;
    }
}

public sealed class RequiredUnlessAttribute : RequiredForAttribute
{
    public RequiredUnlessAttribute(string propertyName, string jsonValues)
        : base(propertyName, jsonValues)
    {
    }

    protected override bool ShouldCheck<T>([MaybeNull] T value, T[] values)
    {
        var comparer = EqualityComparer<T>.Default;
        foreach (var item in values)
            if (comparer.Equals(item, value))
                return false;
        return true;
    }
}