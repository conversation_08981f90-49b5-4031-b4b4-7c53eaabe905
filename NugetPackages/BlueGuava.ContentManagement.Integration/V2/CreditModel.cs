﻿using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreditModel
{
    public string? Name { get; set; }
    public List<Role> Roles { get; set; } = new();
    public string? Character { get; set; }

    public bool IsValidRecord()
    {
        return Roles?.Count > 0 && (!string.IsNullOrEmpty(Name) || !NotCast());
    }

    private bool NotCast()
    {
        return Roles.Any(r => r != Role.Cast);
    }

    internal Credit ToEntity()
    {
        return new Credit
        {
            Name = Name,
            Roles = Roles,
            Character = Character
        };
    }

    public static List<Credit> ToEntity(List<CreditModel> credits)
    {
        return credits?.ConvertAll(c => c.ToEntity()) ?? new List<Credit>();
    }
}