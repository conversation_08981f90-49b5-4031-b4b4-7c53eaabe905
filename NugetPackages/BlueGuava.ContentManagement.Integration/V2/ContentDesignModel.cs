﻿using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Integration.V2;

public class ContentDesignModel
{
    public string? MainTextColor { get; set; }
    public string? ShortInfoColor { get; set; }
    public string? ArtistTextColor { get; set; }
    public string? BackgroundColor { get; set; }

    public ContentDesign ToEntity()
    {
        return new ContentDesign()
        {
            MainTextColor = MainTextColor,
            ShortInfoColor = ShortInfoColor,
            ArtistTextColor = ArtistTextColor,
            BackgroundColor = BackgroundColor
        };
    }
}