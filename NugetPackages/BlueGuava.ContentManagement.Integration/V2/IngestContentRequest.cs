﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class IngestContentRequest
{
    [Required] public Guid ContentId { get; set; }

    [Required] public string? OriginalFileName { get; set; }

    [Required] public string? ObjectUrl { get; set; }

    [Required] public ContentType? Type { get; set; }

    public DateTime? ReleaseDate { get; set; }

    public int? Duration { get; set; }

    public Guid? WorkflowId { get; set; }

    public Guid OwnerId { get; set; }


    public ContentRequest ToContentRequest()
    {
        var assets = new List<AssetModel>();
        assets.Add(new AssetModel()
        {
            Id = ContentId,
            Type = DeductAssetType(Type ?? ContentType.None),
            SubType = SubType.Original,

            Duration = Duration,
            Locale = "--",
            ObjectUrl = ObjectUrl,
            PublicUrl = "", //TODO: compute from S3 and CDN
            WorkflowStatus = WorkflowStatus.Succeeded
        });

        return new ContentRequest
        {
            Type = Type,
            Assets = assets,
            OriginalFileName = OriginalFileName?.Trim(),
            ReleaseDate = ReleaseDate,
            Duration = Duration ?? 0,
            OwnerId = OwnerId
        };
    }

    public static AssetType DeductAssetType(ContentType contentType)
    {
        switch (contentType)
        {
            case ContentType.Video:
            case ContentType.LiveStream:
            case ContentType.Trailer:
            case ContentType.Advertisement:
                return AssetType.Video;

            case ContentType.Podcast:
            case ContentType.Audio:
                return AssetType.Audio;

            case ContentType.Playlist:
            case ContentType.Collection:
                return AssetType.None;

            case ContentType.Image:
                return AssetType.Image;

            case ContentType.Pdf:
                return AssetType.File;

            case ContentType.Word:
            case ContentType.Text:
                return AssetType.Text;

            default:
                return AssetType.None;
        }
    }
}