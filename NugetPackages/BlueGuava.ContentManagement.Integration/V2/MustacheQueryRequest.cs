﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Integration.V2
{
    public class MustacheQueryRequest
    {
        public string scriptId { get; set; }
        public IDictionary<string, object> parameters { get; set; } = new Dictionary<string, object>();
        public int? pageSize { get; set; } = 10;
        public int? pageIndex { get; set; } = 0;
    }
}
