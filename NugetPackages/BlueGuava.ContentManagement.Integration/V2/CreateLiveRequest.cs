﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreateLiveRequest
{
    [Required] public string? OriginalTitle { get; set; }

    public Guid? WorkflowId { get; set; }

    public DateTime? ReleaseDate { get; set; }
    public List<AssetModel>? Assets { get; set; }
    public int Duration { get; set; }
    public Dictionary<string, string>? Properties { get; set; }

    public ContentRequest ToContentRequest(Guid? relatedId)
    {
        var request = new ContentRequest
        {
            Type = ContentType.LiveStream,
            OriginalTitle = OriginalTitle,
            ReferenceId = relatedId?.ToString(),
            ReleaseDate = ReleaseDate,
            Assets = Assets,
            Duration = Duration,
            Properties = Properties
        };

        var key = Consts.CONTENT_WORKFLOWID;
        if (WorkflowId.HasValue && WorkflowId != Guid.Empty)
        {
            request.Properties ??= new Dictionary<string, string>();
            request.Properties[key] = WorkflowId.ToString()!;
        }

        return request;
    }
}