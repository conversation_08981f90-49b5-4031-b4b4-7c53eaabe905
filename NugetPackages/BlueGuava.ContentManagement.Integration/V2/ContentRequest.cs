﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Integration.Infrastructure;
using System.Linq;

namespace BlueGuava.ContentManagement.Integration.V2;

public class ContentRequest
{
    [Required] public ContentType? Type { get; set; }

    public string? OriginalFileName { get; set; }

    [Required] public string? OriginalTitle { get; set; }

    [KeyIsValidLocale(AcceptUnknown = true)]
    public Dictionary<string, LocalizationModel>? Localizations { get; set; }

    [KeyIsValidLocale(AcceptUnknown = true)]
    public Dictionary<string, AvailabilityModel>? ExhibitionWindow { get; set; }

    public string? Color { get; set; }

    public List<CreditModel>? Credits { get; set; }

    public Dictionary<MusimapMood, string>? Moods { get; set; }

    public Dictionary<DesignTypes, ContentDesignModel>? Themes { get; set; }

    public List<AssetModel>? Assets { get; set; }

    public DateTime? PublishedDate { get; set; }

    public DateTime? ReleaseDate { get; set; }

    public DateTime? ForceIndexed { get; set; }

    public bool? Duplicate { get; set; }

    public bool? Downloadable { get; set; }

    public bool? AllowMinting { get; set; }

    public bool? AllowEmailNotification { get; set; }

    public bool? AllowRemix { get; set; }

    public bool? AllowComments { get; set; }

    public bool? AllowUserRating { get; set; }

    public bool? AllowChat { get; set; }

    public bool? AllowSideshow { get; set; }

    public bool? AllowLyrics { get; set; }

    public bool? AllowUpcoming { get; set; }

    public bool? Published { get; set; }

    public Guid? OwnerId { get; set; }

    public List<Guid>? AuthGroupIds { get; set; }

    public Dictionary<string, string>? Properties { get; set; }

    public string? ExternalId { get; set; }

    public string? ReferenceId { get; set; }

    public string? OriginalLanguage { get; set; }
    public string? OriginalTranscript { get; set; }

    public int? Duration { get; set; }

    public Dictionary<EntityType, List<string>>? Entities { get; set; }

    public List<WhereToWatchModel>? WhereToWatch { get; set; }

    public int? InternalPrice { get; set; }
    public double? TokenPrice { get; set; }
    public string? TokenCurrency { get; set; }
    public Visibility? Visibility { get; set; }

    public Dictionary<LabelType, List<string>>? Labels { get; set; }
    public PublishingRule? PublishingRule { get; set; }
    public ContentNotification? Notification { get; set; }

    public DateTime? AutoPublishDate { get; set; }
    public DateTime? AutoUnPublishDate { get; set; }

    public bool? IsDrmEnabled { get; set; }

    public string? ArchivalPolicy { get; set; }
    public string? DeletionPolicy { get; set; }

    public List<string>? Organizations { get; set; }
    public string? RewardCode { get; set; }
    public string? PollHtml { get; set; }

    public void TrimCreditRecords()
    {
        Credits = Credits?.Where(credit => credit.IsValidRecord()).ToList();
    }
}