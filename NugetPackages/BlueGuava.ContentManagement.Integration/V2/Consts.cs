namespace BlueGuava.ContentManagement.Integration.V2;

public static class Consts
{
    /// <summary> name for AWS IVS channel ARN </summary>
    public const string CONTENT_CHANNEL_ARN = "Content:Live:Arn";

    /// <summary> name for AWS IVS stream key ARN </summary>
    public const string CONTENT_STREAMING_ARN = "Content:Streaming:Arn";

    /// <summary> name for AWS IVS stream key value </summary>
    public const string CONTENT_STREAMING_TOKEN = "Content:Streaming:Token";

    /// <summary> name for AWS IVS ingest endpoint </summary>
    public const string CONTENT_STREAMING_URL = "Content:Streaming:Url";

    /// <summary> name for AWS IVS chat room ARN </summary>
    public const string CONTENT_CHATROOM_ARN = "Content:Chat:Arn";

    /// <summary> name for AWS Chime SDK meeting ID </summary>
    public const string CONTENT_SESSIONID = "Content:Process:SessionId";

    /// <summary> name for AWS Chime SDK media capture pipeline ID </summary>
    public const string CONTENT_CAPTUREID = "Content:Process:CaptureId";

    /// <summary> name for AWS Chime SDK media capture pipeline ID </summary>
    public const string CONTENT_MEDIAPIPELINEID = "Content:MediaPipelineId";

    /// <summary> name for AWS Chime SDK media live connector pipeline ID </summary>
    public const string CONTENT_STREAMID = "Content:Process:StreamId";

    /// <summary> name for the identifier of the associated workflow to run </summary>
    public const string CONTENT_WORKFLOWID = "Content:Process:WorkflowId";

    /// <summary> name for the (AWS MediaLive) LiveStream content ID </summary>
    public const string CONTENT_BROADCAST_LIVEID = "Content:Process:Broadcast:LiveId";

    /// <summary> name for the time the content is scheduled (on AWS MediaLive) to broadcast </summary>
    public const string CONTENT_BROADCAST_STARTTIME = "Content:Process:Broadcast:StartTime";

    public const string CONTENT_BROADCAST_RELEASETIME = "Content:Process:Broadcast:ReleaseTime";

    /// <summary> name for the ID of the content being broadcast live (on AWS MediaLive) </summary>
    public const string CONTENT_BROADCAST_VODID = "Content:Process:Broadcast:VodId";

    public const string CONTENT_BROADCAST_STATUS = "Content:Live:Status";

    /// <summary> name for the Channel latency mode </summary>
    public const string CONTENT_IVS_CHANNELLATENCYMODE = "Content:IVS:ChannelLatencyMode";

    /// <summary> name for the Channel Type </summary>
    public const string CONTENT_IVS_CHANNELTYPE = "Content:IVS:ChannelType";

    /// <summary> name for the View configuration </summary>
    public const string CONTENT_IVS_VIEWCONFIGURATION = "Content:IVS:ViewConfiguration";
}