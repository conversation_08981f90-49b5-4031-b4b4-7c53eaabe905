﻿using System;
using System.IO;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class IngestAssetRequestV1 : IngestAssetRequestV2
{
    public Guid ContentId { get; set; }
    public string UserData { get; set; } = "";
}
public class IngestAssetRequestV2
{
    public string OriginalFileName { get; set; }

    public string ObjectUrl { get; set; }

    public bool IsPublic { get; set; } = false;

    public string PublicUrl { get; set; }

    public DateTime ReleaseDate { get; set; } = System.DateTime.Now;

    public SubType SubType { get; set; } = SubType.None;

    public ContentType Type { get; set; } = ContentType.None;

    public string Locale { get; set; } = "--";

    public bool UploadedByUser { get; set; } = false;

    public Asset ToAsset()
    {
        var extension = Path.GetExtension(OriginalFileName);
        return new Asset()
        {
            Id = Guid.NewGuid(),
            Type = Type == ContentType.None ?
                DeductAssetType(extension)
                : IngestContentRequest.DeductAssetType(Type),
            SubType = SubType == SubType.None ?
                DeductSubAssetType(extension)
                : SubType,
            Locale = Locale,
            ObjectUrl = ObjectUrl,
            PublicUrl = PublicUrl, //TODO: compute from S3 and CDN
            CreatedDate = DateTime.Now,
            IsPublic = IsPublic,
            WorkflowStatus = WorkflowStatus.Succeeded,
            FileName = OriginalFileName,
            UploadedByUser = UploadedByUser
        };
    }

    #region HACK: detect asset type based on file extension

    private static AssetType DeductAssetType(string? extension)
    {
        switch (extension?.ToLower())
        {
            case ".jpeg":
            case ".jpg":
            case ".png":
            case ".gif":
            case ".tiff":
            case ".psd":
            case ".ai":
            case ".eps":
            case ".indd":
            case ".raw":
            case ".avif":
                return AssetType.Image;

            case ".webm":
            case ".mpg":
            case ".mp2":
            case ".mpeg":
            case ".mpe":
            case ".mpv":
            case ".ogg":
            case ".mp4":
            case ".m4p":
            case ".m4v":
            case ".avi":
            case ".wmv":
            case ".mov":
            case ".qt":
            case ".flv":
            case ".swf":
            case ".avchd":
            case ".ts":
                return AssetType.Video;

            case ".mp3":
            case ".aac":
            case ".flac":
            case ".alac":
            case ".wav":
            case ".aiff":
            case ".dsd":
            case ".pcm":
            case ".wma":
            case ".m4b":
            case ".3ga":
            case ".amr":
            case ".ape":
            case ".arf":
            case ".midi":
                return AssetType.Audio;

            case ".doc":
            case ".docx":
            case ".json":
            case ".xml":
                return AssetType.Text;
            case ".pdf":
            case ".ppt":
            case ".pptx":
            case ".xlsx":
            case ".xls":
                return AssetType.File;
            case ".txt":
                return AssetType.Text;
            default:
                return AssetType.File;
        }
    }

    private static SubType DeductSubAssetType(string? extension)
    {
        switch (extension?.ToLower())
        {
            case ".mp4":
                return Packages.Entities.V2.Enums.SubType.MP4;

            case ".mp3":
                return Packages.Entities.V2.Enums.SubType.MP3;
            case ".wav":
                return Packages.Entities.V2.Enums.SubType.WAV;
            case ".flac":
                return Packages.Entities.V2.Enums.SubType.FLAC;

            case ".doc":
            case ".docx":
                return Packages.Entities.V2.Enums.SubType.Word;
            case ".pdf":
                return Packages.Entities.V2.Enums.SubType.PDF;
            case ".ppt":
            case ".pptx":
                return Packages.Entities.V2.Enums.SubType.Powerpoint;
            case ".xlsx":
            case ".xls":
                return Packages.Entities.V2.Enums.SubType.Excel;
            case ".txt":
                return Packages.Entities.V2.Enums.SubType.Text;
            default:
                return Packages.Entities.V2.Enums.SubType.Other;
        }
    }

    #endregion
}