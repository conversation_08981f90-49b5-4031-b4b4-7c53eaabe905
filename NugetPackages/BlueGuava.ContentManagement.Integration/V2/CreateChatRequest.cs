﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreateChatRequest
{
    [Required] public string? OriginalTitle { get; set; }

    public Guid? WorkflowId { get; set; }

    public string? ShortInfo { get; set; }

    public ContentRequest ToContentRequest(Guid? relatedId)
    {
        var request = new ContentRequest
        {
            Type = ContentType.LiveChat,
            OriginalTitle = OriginalTitle,
            ReferenceId = relatedId?.ToString()
        };

        var key = Consts.CONTENT_WORKFLOWID;
        if (WorkflowId.HasValue && WorkflowId != Guid.Empty)
        {
            request.Properties ??= new Dictionary<string, string>();
            request.Properties[key] = WorkflowId.ToString()!;
        }

        return request;
    }
}