﻿using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Integration.V2;

public class LocalizationModel
{
    public string? Name { get; set; }
    public string? ShortInfo { get; set; }
    public string? Description { get; set; }
    public bool B64 { get; set; }

    public Localization ToEntity()
    {
        return new Localization()
        {
            Name = Name?.Trim(),
            ShortInfo = ShortInfo?.Trim(),
            Description = Description?.Trim()
        };
    }
}