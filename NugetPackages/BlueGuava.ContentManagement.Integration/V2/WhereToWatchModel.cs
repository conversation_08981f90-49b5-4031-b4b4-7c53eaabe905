﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Integration.V2;

public class WhereToWatchModel
{
    [Required] public string? ProviderName { get; set; }

    [Required][Url] public string? WatchUrl { get; set; }

    public WhereToWatch ToEntity()
    {
        return new WhereToWatch()
        {
            ProviderName = ProviderName,
            WatchUrl = WatchUrl
        };
    }

    public static List<WhereToWatch> ToEntity(List<WhereToWatchModel> collection)
    {
        return collection?.Select(e => e.ToEntity())?.Distinct()?.ToList() ?? new List<WhereToWatch>();
    }
}