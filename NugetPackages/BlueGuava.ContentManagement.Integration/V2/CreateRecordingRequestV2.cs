using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreateRecordingRequestV2 : ContentRequest
{
    public Guid? WorkflowId { get; set; }
    public IVSChannelLatencyMode ChannelLatencyMode { get; set; } = IVSChannelLatencyMode.NORMAL;
    public IVSChannelType ChannelType { get; set; } = IVSChannelType.BASIC;
    public ViewConfigurations ViewConfiguration { get; set; } = ViewConfigurations.Default;

    public ContentRequest ToContentRequest(Guid? remixId)
    {
        var request = new ContentRequest
        {
            Type = ContentType.CameraCapture,
            OriginalTitle = OriginalTitle,
            ReferenceId = remixId?.ToString(),
            OriginalLanguage = OriginalLanguage
        };

        var key = Consts.CONTENT_WORKFLOWID;

        request.Properties ??= new Dictionary<string, string>();
        request.Properties.Add(Consts.CONTENT_IVS_VIEWCONFIGURATION, ViewConfiguration.ToString());
        request.Properties.Add(Consts.CONTENT_IVS_CHANNELLATENCYMODE, ChannelLatencyMode.ToString());
        request.Properties.Add(Consts.CONTENT_IVS_CHANNELTYPE, ChannelType.ToString());

        if (WorkflowId.HasValue && WorkflowId != Guid.Empty)
        {
            request.Properties ??= new Dictionary<string, string>();
            request.Properties[key] = WorkflowId.ToString()!;
        }

        return request;
    }
}