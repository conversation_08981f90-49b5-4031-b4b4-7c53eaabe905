﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreateRecordingRequest
{
    [Required] public string? OriginalTitle { get; set; }

    public Guid? WorkflowId { get; set; }

    public string? OriginalLanguage { get; set; }

    public ContentRequest ToContentRequest(Guid? remixId)
    {
        var request = new ContentRequest
        {
            Type = ContentType.CameraCapture,
            OriginalTitle = OriginalTitle,
            ReferenceId = remixId?.ToString(),
            OriginalLanguage = OriginalLanguage
        };

        var key = Consts.CONTENT_WORKFLOWID;
        if (WorkflowId.HasValue && WorkflowId != Guid.Empty)
        {
            request.Properties ??= new Dictionary<string, string>();
            request.Properties[key] = WorkflowId.ToString()!;
        }

        return request;
    }
}