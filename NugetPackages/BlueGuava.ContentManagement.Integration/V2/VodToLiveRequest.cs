﻿using System;
using System.ComponentModel.DataAnnotations;
using BlueGuava.ContentManagement.Integration.Infrastructure;

namespace BlueGuava.ContentManagement.Integration.V2;

public class VodToLiveRequest
{
    [Required] public DateTime? StartTime { get; set; }

    public string? ObjectUrl { get; set; }

    [Range(1, int.MaxValue)]
    [RequiredUnless(nameof(ObjectUrl), "null, \"\"")]
    public int? Duration { get; set; }

    public string? LiveStreamTitle { get; set; }
}