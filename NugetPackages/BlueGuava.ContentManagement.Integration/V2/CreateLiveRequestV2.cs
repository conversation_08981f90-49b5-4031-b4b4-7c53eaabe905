using System;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreateLiveRequestV2 : ContentRequest
{
    public CreateLiveRequestV2()
    {
        Type = ContentType.LiveStream;
    }

    public Guid? WorkflowId { get; set; }
    public IVSChannelLatencyMode ChannelLatencyMode { get; set; } = IVSChannelLatencyMode.NORMAL;
    public IVSChannelType ChannelType { get; set; } = IVSChannelType.BASIC;
}