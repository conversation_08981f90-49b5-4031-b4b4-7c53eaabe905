﻿using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using System;
using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Integration.V2;

public class CreateLiveContentRequest
{
    public Dictionary<string, string>? Properties { get; set; }
    public IVSChannelLatencyMode ChannelLatencyMode { get; set; } = IVSChannelLatencyMode.NORMAL;
    public IVSChannelType ChannelType { get; set; } = IVSChannelType.BASIC;
    public DateTime? ReleaseDate { get; set; }
    public List<AssetModel>? Assets { get; set; }
    public int Duration { get; set; }
}