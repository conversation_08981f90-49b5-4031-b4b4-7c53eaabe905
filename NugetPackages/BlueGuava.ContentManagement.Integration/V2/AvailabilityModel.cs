﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Integration.V2;

public class AvailabilityModel
{
    public int AgeLimit { get; set; }
    public List<string>? AdvisoryCodes { get; set; }
    public DateTime? AvailableFrom { get; set; }
    public DateTime? AvailableUntil { get; set; }

    public string[]? Cities { get; set; }

    public Availability? ToEntity()
    {
        // min and null are the same
        if (AvailableFrom == DateTime.MinValue)
            AvailableFrom = null;

        // max and null are the same
        if (AvailableUntil == DateTime.MaxValue)
            AvailableFrom = null;

        if (AvailableFrom == null && AvailableUntil == null)
            return null; // remove if nothing specific

        return new Availability
        {
            AgeLimit = AgeLimit,
            AdvisoryCodes = AdvisoryCodes,
            AvailableFrom = AvailableFrom,
            AvailableUntil = AvailableUntil,
            Cities = Cities
        };
    }
}