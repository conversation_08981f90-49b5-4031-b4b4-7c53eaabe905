using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class IngestContentRequestV2 : ContentRequest
{
    public string? AssetId { get; set; }
    public Guid? ContentId { get; set; }
    public string? ObjectUrl { get; set; }
    public string? PublicUrl { get; set; }
    public string? UserData { get; set; }
    public string? ExternalId { get; set; }

    public Guid? WorkflowId { get; set; }
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public bool UploadedByUser { get; set; } = false;

    public new string OriginalTitle
    {
        get => base.OriginalTitle ?? OriginalFileName ?? string.Empty;
        set => base.OriginalTitle = value;
    }

    public void AddAsset()
    {
        Assets ??= new List<AssetModel>();

        //do not add a second original asset or an original asset for a remix
        //if (Type == ContentType.Remix || Assets.Any(a => a.SubType == SubType.Original && !a.IsDeleted))
        //    return;

        Assets.Add(new AssetModel()
        {
            Id = ContentId,
            Type = DeductAssetType(Type ?? ContentType.None),
            SubType = SubType.Original,

            Duration = Duration,
            Locale = OriginalLanguage ?? OriginalLanguage ?? "--",
            ObjectUrl = ObjectUrl,
            PublicUrl = PublicUrl,
            WorkflowStatus = WorkflowStatus.Succeeded,
            UploadedByUser = UploadedByUser,
            FileName = OriginalFileName ?? OriginalTitle ?? string.Empty
        });
    }

    public static AssetType DeductAssetType(ContentType contentType)
    {
        switch (contentType)
        {
            case ContentType.Video:
            case ContentType.LiveStream:
            case ContentType.Trailer:
            case ContentType.Advertisement:
                return AssetType.Video;

            case ContentType.Podcast:
            case ContentType.Audio:
                return AssetType.Audio;

            case ContentType.Playlist:
            case ContentType.Collection:
                return AssetType.None;

            case ContentType.Image:
                return AssetType.Image;

            case ContentType.Pdf:
                return AssetType.File;

            case ContentType.Word:
            case ContentType.Text:
                return AssetType.Text;

            default:
                return AssetType.None;
        }
    }
}