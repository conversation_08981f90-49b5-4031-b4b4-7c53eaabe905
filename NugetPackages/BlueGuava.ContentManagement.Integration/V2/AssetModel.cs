﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Integration.Infrastructure;

namespace BlueGuava.ContentManagement.Integration.V2;

public class AssetModel
{
    public Guid? Id { get; set; }

    [Required] public AssetType Type { get; set; }

    [Required] public SubType SubType { get; set; }

    [RequiredIf(nameof(IsLocaleRequired))]
    [IsValidLocale(AcceptUnknown = true)]
    public string? Locale { get; set; }

    //[RequiredIf(nameof(IsDurationRequired), "Required for Audio and Video type content", MinCount = 1)]
    public int? Duration { get; set; }

    public WorkflowStatus? WorkflowStatus { get; set; }

    public List<string>? LifeCyclePolicies { get; set; }

    public string? PublicUrl { get; set; }

    public string? ObjectUrl { get; set; }

    public string? RestoredObjectUrl { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsDeleted { get; set; }

    public long FileSize { get; set; }

    public DateTime CreatedDate { get; set; }

    public bool IsPublic { get; set; }

    public string? IpfsHash { get; set; }

    public string? FileName { get; set; }

    /// <summary>
    /// Uploader user id from the authorization token
    /// </summary>
    public string? UploaderUserId { get; set; }

    /// <summary>
    /// Indicates whether content was uploaded by user or other service
    /// </summary>
    public bool UploadedByUser { get; set; }

    public static bool IsDurationRequired(AssetModel request)
    {
        return (request.Type == AssetType.Audio || request.Type == AssetType.Video)
               && (request.WorkflowStatus == Packages.Entities.DrmEntities.WorkflowStatus.Succeeded
                   || request.WorkflowStatus == Packages.Entities.DrmEntities.WorkflowStatus.Overwritten)
               && !request.IsDeleted;
    }

    public static bool IsLocaleRequired(AssetModel request)
    {
        return (request.Type == AssetType.Text || request.Type == AssetType.Pdf)
               && (request.WorkflowStatus == Packages.Entities.DrmEntities.WorkflowStatus.Succeeded
                   || request.WorkflowStatus == Packages.Entities.DrmEntities.WorkflowStatus.Overwritten)
               && !request.IsDeleted;
    }


    public static List<Asset> ToEntity(List<AssetModel> ratings)
    {
        return ratings?.Select(e => e.ToEntity())?.Distinct()?.ToList() ?? new List<Asset>();
    }


    public Asset ToEntity()
    {
        return new Asset
        {
            Id = Id ?? Guid.NewGuid(),
            Type = Type == AssetType.Pdf ? AssetType.File : Type,
            SubType = SubType,

            Duration = Duration ?? 0,
            LifeCyclePolicies = LifeCyclePolicies,
            Locale = Locale,
            ModifiedDate = ModifiedDate,
            ObjectUrl = ObjectUrl,
            PublicUrl = PublicUrl,
            RestoredObjectUrl = RestoredObjectUrl,
            WorkflowStatus = WorkflowStatus ?? Packages.Entities.DrmEntities.WorkflowStatus.Unknown,
            IsDeleted = IsDeleted,
            FileSize = FileSize,
            CreatedDate = CreatedDate == default ? DateTime.UtcNow : CreatedDate,
            IpfsHash = IpfsHash,
            IsPublic = IsPublic,
            FileName = FileName,
            UploaderUserId = UploaderUserId,
            UploadedByUser = UploadedByUser
        };
    }
}