﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BlueGuava.ContentManagement.Integration.V2;

/// <summary>
/// Represents a query request for content and location-based filtering
/// </summary>
public class QueryRequest : IValidatableObject
{
    /// <summary>
    /// Content-specific query parameters
    /// </summary>
    public QueryContentRequest? Content { get; set; }

    /// <summary>
    /// Location-specific query parameters
    /// </summary>
    public QueryLocationRequest? Location { get; set; }

    /// <summary>
    /// Validates the query request using data annotations
    /// </summary>
    /// <param name="validationContext">Validation context</param>
    /// <returns>Collection of validation results</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // Validate content request if present
        if (Content != null)
        {
            var contentResults = new List<ValidationResult>();
            var contentContext = new ValidationContext(Content);

            if (!Validator.TryValidateObject(Content, contentContext, contentResults, true))
            {
                results.AddRange(contentResults);
            }
        }

        // Validate location request if present
        if (Location != null)
        {
            var locationResults = new List<ValidationResult>();
            var locationContext = new ValidationContext(Location);

            if (!Validator.TryValidateObject(Location, locationContext, locationResults, true))
            {
                results.AddRange(locationResults);
            }
        }

        return results;
    }

    /// <summary>
    /// Simple validation method for backward compatibility
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        var validationContext = new ValidationContext(this);
        var results = new List<ValidationResult>();
        return Validator.TryValidateObject(this, validationContext, results, true);
    }
}

/// <summary>
/// Content-specific query parameters with validation
/// </summary>
public class QueryContentRequest : IValidatableObject
{
    private static readonly DateTime MinAllowedDate = new(2025, 1, 1);
    private static readonly int MaxFutureDays = 7;

    /// <summary>
    /// Start date for content filtering (must be after 2025-01-01) Content:Event:StartDate
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date for content filtering (must be within 7 days from today) Content:Event:EndDate
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Filter by published status (defaults to true, false values are not allowed)
    /// </summary>
    public bool? Published { get; set; } = true;

    /// <summary>
    /// Content task types for filtering (Content:Task:Type)
    /// </summary>
    public List<string>? TaskTypes { get; set; }

    /// <summary>
    /// Content task types for filtering (Content:Task:SubType)
    /// </summary>
    public List<string>? TaskSubTypes { get; set; }

    /// <summary>
    /// Content categories for filtering (Content:Task:Category)
    /// </summary>
    public List<string>? Categories { get; set; }

    /// <summary>
    /// Validates the content request parameters
    /// </summary>
    /// <param name="validationContext">Validation context</param>
    /// <returns>Collection of validation results</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // Validate start date
        if (StartDate.HasValue && StartDate.Value < MinAllowedDate)
        {
            results.Add(new ValidationResult(
                $"Start date must be on or after {MinAllowedDate:yyyy-MM-dd}",
                new[] { nameof(StartDate) }));
        }

        // Validate end date
        var maxAllowedEndDate = DateTime.Today.AddDays(MaxFutureDays);
        if (EndDate.HasValue && EndDate.Value > maxAllowedEndDate)
        {
            results.Add(new ValidationResult(
                $"End date cannot be more than {MaxFutureDays} days in the future",
                new[] { nameof(EndDate) }));
        }

        // Validate date range
        if (StartDate.HasValue && EndDate.HasValue && StartDate.Value > EndDate.Value)
        {
            results.Add(new ValidationResult(
                "Start date cannot be after end date",
                new[] { nameof(StartDate), nameof(EndDate) }));
        }

        // Validate published status
        if (Published.HasValue && !Published.Value)
        {
            results.Add(new ValidationResult(
                "Only published content can be queried (Published must be true or null)",
                new[] { nameof(Published) }));
        }

        return results;
    }
}
/// <summary>
/// Location-specific query parameters for content distribution filtering
/// Supports various distribution types:
/// - GLOBAL: No location restrictions
/// - REGIONAL: Filtered by county (Content:Task:Regions contains User.County)
/// - OEVK: Filtered by constituency (Content:Task:Regions contains User.OEVK)
/// - POOL: Filtered by pool ID (Content:Task:Regions contains User.PoolId)
/// - LOCAL: Filtered by coordinates within radius
/// </summary>
public class QueryLocationRequest : IValidatableObject
{
    private const int MinRadiusKm = 1;
    private const int MaxRadiusKm = 1000;

    /// <summary>
    /// User roles for community-based filtering (Content:Task:Community)
    /// </summary>
    public List<string>? Roles { get; set; }

    /// <summary>
    /// County for regional filtering (Content:Task:Regions)
    /// Examples: "Budapest", "Borsod-Abaúj-Zemplén"
    /// </summary>
    [StringLength(100, ErrorMessage = "County name cannot exceed 100 characters")]
    public string? County { get; set; }

    /// <summary>
    /// Pool identifier for pool-based filtering (Content:Task:Regions)
    /// Should be a valid GUID string
    /// </summary>
    public string? PoolId { get; set; }

    /// <summary>
    /// Constituency identifier for OEVK filtering (Content:Task:Regions)
    /// </summary>
    [StringLength(50, ErrorMessage = "Constituency cannot exceed 50 characters")]
    public string? Constituency { get; set; }

    /// <summary>
    /// Geographic coordinate for location-based filtering
    /// </summary>
    public Coordinate? Coordinate { get; set; }

    /// <summary>
    /// Search radius in kilometers (1-1000 km)
    /// Only used when Coordinate is specified
    /// </summary>
    [Range(MinRadiusKm, MaxRadiusKm, ErrorMessage = "Radius must be between 1 and 1000 kilometers")]
    public int Radius { get; set; } = 10; // Default 10km radius

    /// <summary>
    /// Validates the location request parameters
    /// </summary>
    /// <param name="validationContext">Validation context</param>
    /// <returns>Collection of validation results</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // Validate PoolId format if provided
        if (!string.IsNullOrEmpty(PoolId) && !Guid.TryParse(PoolId, out _))
        {
            results.Add(new ValidationResult(
                "PoolId must be a valid GUID format",
                new[] { nameof(PoolId) }));
        }

        // Validate coordinate and radius relationship
        if (Coordinate != null && Radius <= 0)
        {
            results.Add(new ValidationResult(
                "Radius must be greater than 0 when coordinate is specified",
                new[] { nameof(Radius) }));
        }

        // Validate coordinate if present
        if (Coordinate != null)
        {
            var coordinateResults = new List<ValidationResult>();
            var coordinateContext = new ValidationContext(Coordinate);

            if (!Validator.TryValidateObject(Coordinate, coordinateContext, coordinateResults, true))
            {
                results.AddRange(coordinateResults);
            }
        }

        return results;
    }
}

/// <summary>
/// Represents a geographic coordinate with latitude and longitude
/// </summary>
public class Coordinate : IValidatableObject
{
    private const double MinLatitude = -90.0;
    private const double MaxLatitude = 90.0;
    private const double MinLongitude = -180.0;
    private const double MaxLongitude = 180.0;

    /// <summary>
    /// Latitude in decimal degrees (-90 to 90)
    /// </summary>
    [Range(MinLatitude, MaxLatitude, ErrorMessage = "Latitude must be between -90 and 90 degrees")]
    public double Latitude { get; set; }

    /// <summary>
    /// Longitude in decimal degrees (-180 to 180)
    /// </summary>
    [Range(MinLongitude, MaxLongitude, ErrorMessage = "Longitude must be between -180 and 180 degrees")]
    public double Longitude { get; set; }

    /// <summary>
    /// Validates the coordinate values
    /// </summary>
    /// <param name="validationContext">Validation context</param>
    /// <returns>Collection of validation results</returns>
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var results = new List<ValidationResult>();

        // Additional validation for special cases
        if (double.IsNaN(Latitude) || double.IsInfinity(Latitude))
        {
            results.Add(new ValidationResult(
                "Latitude must be a valid number",
                new[] { nameof(Latitude) }));
        }

        if (double.IsNaN(Longitude) || double.IsInfinity(Longitude))
        {
            results.Add(new ValidationResult(
                "Longitude must be a valid number",
                new[] { nameof(Longitude) }));
        }

        return results;
    }

    /// <summary>
    /// Returns a string representation of the coordinate
    /// </summary>
    /// <returns>Formatted coordinate string</returns>
    public override string ToString()
    {
        return $"({Latitude:F6}, {Longitude:F6})";
    }

    /// <summary>
    /// Determines whether the specified object is equal to the current coordinate
    /// </summary>
    /// <param name="obj">Object to compare</param>
    /// <returns>True if equal, false otherwise</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not Coordinate other) return false;
        return Math.Abs(Latitude - other.Latitude) < 0.000001 &&
               Math.Abs(Longitude - other.Longitude) < 0.000001;
    }

    /// <summary>
    /// Returns a hash code for the coordinate
    /// </summary>
    /// <returns>Hash code</returns>
    public override int GetHashCode()
    {
        return HashCode.Combine(Latitude, Longitude);
    }
}
