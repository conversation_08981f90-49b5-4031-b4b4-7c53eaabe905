﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Integration.V2;

public class ContentResponse
{
    public Guid Id { get; set; }

    public ContentType Type { get; set; }

    public string? Color { get; set; }

    public List<Credit>? Credits { get; set; }

    public Dictionary<string, Localization>? Localizations { get; set; }

    public Dictionary<string, Availability>? ExhibitionWindow { get; set; }

    public Dictionary<DesignTypes, ContentDesign>? Themes { get; set; }

    public List<Asset>? Assets { get; set; }

    public DateTime? PublishedDate { get; set; }

    public DateTime? ReleaseDate { get; set; }

    public DateTime? ForceIndexed { get; set; }

    public bool? Duplicate { get; set; }

    public bool? Downloadable { get; set; }

    public bool? AllowMinting { get; set; }
    public bool? AllowEmailNotification { get; set; }
    public bool? AllowRemix { get; set; }

    public bool? AllowComments { get; set; }

    public bool? AllowUserRating { get; set; }

    public bool? AllowChat { get; set; }

    public bool? AllowSideshow { get; set; }

    public bool? AllowLyrics { get; set; }

    public bool? AllowUpcoming { get; set; }

    public bool? Published { get; set; }

    public Guid OwnerId { get; set; }

    public Guid LastModifiedBy { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public List<Guid>? AuthGroupIds { get; set; }

    public Dictionary<string, string>? Properties { get; set; }

    public List<string>? Tags { get; set; }

    public string? ExternalId { get; set; }

    public string? ReferenceId { get; set; }

    public string? OriginalTitle { get; set; }

    public string? OriginalFileName { get; set; }

    public int? Duration { get; set; }

    public string? OriginalLanguage { get; set; }

    public string? OriginalTranscript { get; set; }

    public Dictionary<EntityType, List<string>>? Entities { get; set; }

    public List<WhereToWatch>? WhereToWatch { get; set; }

    public int? InternalPrice { get; set; }
    public double? TokenPrice { get; set; }
    public string? TokenCurrency { get; set; }

    public string? ContentToken { get; set; }
    public ProcessingStatus? ProcessingStatus { get; set; }
    public Visibility? Visibility { get; set; }

    public Dictionary<LabelType, List<string>>? Labels { get; set; }

    public bool? HasTags { get; set; }
    public bool? HasSubtitle { get; set; }
    public PublishingRule? PublishingRule { get; set; }
    public ContentNotification? Notification { get; set; }

    public string? ArchivalPolicy { get; set; }

    public string? DeletionPolicy { get; set; }


    public List<string>? Relations { get; set; }

    public bool? IsDeleted { get; set; }
    public bool? IsDrmEnabled { get; set; }
    public string? RewardCode { get; set; }

    public Guid? OriginalOwnerId { get; set; }
    public bool? IsRewarded { get; set; }
    public string? PollHtml { get; set; }
}