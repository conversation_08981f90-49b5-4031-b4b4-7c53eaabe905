﻿using System.Runtime.CompilerServices;
using BlueGuava.ContentManagement.Packages.Entities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

[assembly: InternalsVisibleTo("BlueGuava.ContentManagement.Api")]
[assembly: TypeForwardedTo(typeof(BaseData))]
[assembly: TypeForwardedTo(typeof(Translatable))]
[assembly: TypeForwardedTo(typeof(Asset))]
[assembly: TypeForwardedTo(typeof(Availability))]
[assembly: TypeForwardedTo(typeof(Content))]
[assembly: TypeForwardedTo(typeof(ContentDesign))]
[assembly: TypeForwardedTo(typeof(Role))]
[assembly: TypeForwardedTo(typeof(Credit))]
[assembly: TypeForwardedTo(typeof(Localization))]
[assembly: TypeForwardedTo(typeof(WhereToWatch))]
[assembly: TypeForwardedTo(typeof(AssetType))]
[assembly: TypeForwardedTo(typeof(LabelType))]
[assembly: TypeForwardedTo(typeof(ContentType))]
[assembly: TypeForwardedTo(typeof(DesignTypes))]
[assembly: TypeForwardedTo(typeof(EntityType))]
[assembly: TypeForwardedTo(typeof(MusimapMood))]
[assembly: TypeForwardedTo(typeof(SubType))]