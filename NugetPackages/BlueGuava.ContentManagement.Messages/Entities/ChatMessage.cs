using BlueGuava.MessageQueuing;
using System;

namespace BlueGuava.ContentManagement.Messages.Entities;

public class ChatMessage : IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string? RoomArn { get; set; }
    public string? Message { get; set; }

    #region Delete Message properties
    public bool Deleted { get; set; }
    public string? ContentId { get; set; }
    public string? Reason { get; set; }

    #endregion
    public string? UserAccessToken { get; set; }

    string? IQueueItem.Receipt { get; set; }
    string? IQueueItem.GroupId { get => $"{Id}"; set => _ = value; }
    string? IQueueItem.DeduplicationId { get => $"{Id}"; set => _ = value; }
    public string? Sender { get; set; }
}