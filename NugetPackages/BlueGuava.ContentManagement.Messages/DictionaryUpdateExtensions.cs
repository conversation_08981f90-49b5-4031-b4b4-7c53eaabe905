﻿using System.Collections.Generic;
using System.Linq;

namespace BlueGuava.ContentManagement.Messages;

internal static class DictionaryUpdateExtensions
{
    public static void OverwriteLists<TKey, TItem>(this Dictionary<TKey, List<TItem>> referenceItems,
        Dictionary<TKey, List<TItem>> referencedIds) where TKey : notnull
    {
        foreach (var kvp in referencedIds)
            referenceItems[kvp.Key] = kvp.Value;
    }

    public static void AddToLists<TKey, TItem>(this Dictionary<TKey, List<TItem>> referenceItems,
        Dictionary<TKey, List<TItem>> referencedIds, IEqualityComparer<TItem>? comparer = null) where TKey : notnull
    {
        comparer ??= EqualityComparer<TItem>.Default;
        foreach (var kvp in referencedIds)
        {
            if (!referenceItems.TryGetValue(kvp.Key, out var list))
                referenceItems[kvp.Key] = list = new List<TItem>();

            list.AddRange(kvp.Value.Where(id => !list.Any(x => comparer.Equals(x, id))));
        }
    }

    public static void DelFromLists<TKey, TItem>(this Dictionary<TKey, List<TItem>> referenceItems,
        Dictionary<TKey, List<TItem>> referencedIds, IEqualityComparer<TItem>? comparer = null) where TKey : notnull
    {
        comparer ??= EqualityComparer<TItem>.Default;
        foreach (var kvp in referencedIds)
            if (!referenceItems.TryGetValue(kvp.Key, out var list))
                list?.RemoveAll(id => kvp.Value.Any(x => comparer.Equals(x, id)));
    }

    public static void AddRange<TKey, TValue>(this Dictionary<TKey, TValue>? dictionary,
        IEnumerable<KeyValuePair<TKey, TValue>> updates) where TKey : notnull
    {
        dictionary ??= new Dictionary<TKey, TValue>();
        foreach (var item in updates)
            if (!dictionary!.ContainsKey(item.Key))
                dictionary.Add(item.Key, item.Value);
    }

    public static void RemoveRange<TKey, TValue>(this Dictionary<TKey, TValue> dictionary,
        IEnumerable<KeyValuePair<TKey, TValue>> updates) where TKey : notnull
    {
        foreach (var item in updates) dictionary.Remove(item.Key);
    }

    public static void Append<T>(this List<T> list, IEnumerable<T> itemList, IEqualityComparer<T>? comparer = null)
    {
        comparer ??= EqualityComparer<T>.Default;
        foreach (var item in itemList)
            if (!list.Contains(item, comparer))
                list.Add(item);
    }

    public static void Extract<T>(this List<T> list, IEnumerable<T> itemList, IEqualityComparer<T>? comparer = null)
    {
        comparer ??= EqualityComparer<T>.Default;
        foreach (var item in itemList) list.RemoveAll(entry => comparer.Equals(item, entry));
    }
}