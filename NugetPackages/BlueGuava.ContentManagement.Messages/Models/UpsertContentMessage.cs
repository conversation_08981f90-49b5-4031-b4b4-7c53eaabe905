﻿using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class UpsertContentMessage : UpdateMessage
{
    public UpsertContentMessage()
    {
    }

    public UpsertContentMessage(Content content)
    {
        Id = content.Id.ToString();
        ContentId = Id;
        Content = content;
    }

    public Content? Content { get; set; }

    internal override Content? UpdateEntity(Content? entity)
    {
        return Content;
    }
}