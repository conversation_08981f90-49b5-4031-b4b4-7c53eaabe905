﻿using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class UpdatePropertiesMessage : UpdateMessage
{
    public ListUpdateStrategy UpdateStrategy { get; set; }

    public Dictionary<string, string>? Properties { get; set; }


    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        if (!(Properties?.Count > 0)) return entity; // nothing to do
        entity.Properties ??= new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        if (entity.Properties.Comparer != StringComparer.OrdinalIgnoreCase)
            entity.Properties = entity.Properties // define comparer, and hope that it will not fail
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value, StringComparer.OrdinalIgnoreCase);

        switch (UpdateStrategy)
        {
            case ListUpdateStrategy.Overwrite:
                entity.Properties = Properties;
                break;
            case ListUpdateStrategy.Add:
                entity.Properties.AddRange(Properties);
                break;
            case ListUpdateStrategy.Remove:
                entity.Properties.RemoveRange(Properties);
                break;
            default: throw new NotSupportedException("Not supported update strategy: " + UpdateStrategy);
        }

        return entity;
    }
}