﻿using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class AssetBatchUpdateMessage : UpdateMessage
{
    public List<AssetUpdateMessage> AssetUpdateMessages { get; set; } = new();

    internal override Content? UpdateEntity(Content? entity)
    {
        foreach (var assetUpdateMessage in AssetUpdateMessages) assetUpdateMessage.UpdateEntity(entity);

        return entity;
    }
}