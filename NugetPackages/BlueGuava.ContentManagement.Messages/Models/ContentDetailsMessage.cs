using System;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public class ContentDetailsMessage : UpdateMessage
{
    public ContentDetailsMessage(Guid contentId)
    {
        ContentId = contentId.ToString();
    }

    public Guid? SourceId { get; set; }
    public string? Relation { get; set; }

    internal override Content? UpdateEntity(Content? entity)
    {
        return entity;
    }
}