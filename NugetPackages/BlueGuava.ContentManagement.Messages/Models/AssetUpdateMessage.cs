﻿using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using AssetSubType = BlueGuava.ContentManagement.Packages.Entities.V2.Enums.SubType;
using Microsoft.Extensions.Logging;
namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class AssetUpdateMessage : UpdateMessage
{
    /*private static readonly ILogger Logger = LoggerFactory.Create(builder => builder
        .SetMinimumLevel(LogLevel.Information)
        .AddConsole())
        .CreateLogger<AssetUpdateMessage>();*/

    public Guid? AssetId { get; set; }

    public AssetType? Type { get; set; }

    public SubType? SubType { get; set; }

    public string? Locale { get; set; }

    public int? Duration { get; set; }

    public WorkflowStatus? WorkflowStatus { get; set; }

    public List<string>? LifeCyclePolicies { get; set; }

    public string? PublicUrl { get; set; }

    public string? ObjectUrl { get; set; }

    public string? RestoredObjectUrl { get; set; }

    public string? ErrorMessage { get; set; }

    public bool? IsDeleted { get; set; }

    public long? FileSize { get; set; }

    public string? IpfsHash { get; set; }

    public string? FileName { get; set; }

    public bool? IsPublic { get; set; }

    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        if (IsDeleted.HasValue && AssetId == default)
            throw new InvalidOperationException("The Asset message does not have primary key (AssetId).");

        if (string.IsNullOrEmpty(Locale)) Locale = entity.OriginalLanguage ?? string.Empty;

        entity.Assets ??= new List<Asset>();
        var currentAsset = AssetId.HasValue
            ? entity.Assets.SingleOrDefault(x => x.Id == AssetId)
            : !string.IsNullOrEmpty(ObjectUrl) //try to get asset by object url for AI Workflow updates
                ? entity.Assets.SingleOrDefault(x => x.ObjectUrl == ObjectUrl && !x.IsDeleted)
                : null;

        if (currentAsset == null)
        {
            //Logger.LogWarning("UpdateEntity 1 Asset not found for update");

            currentAsset = entity.Assets.FirstOrDefault(x
                => x.Type == Type && x.SubType == SubType
                                  && (x.Locale ?? string.Empty) == (Locale ?? string.Empty)
                                  && x.IsDeleted == (IsDeleted ?? false));
        }
        if (currentAsset == null)
        {
            //Logger.LogInformation("UpdateEntity 2 Asset not found for update");

            // create and add for further update
            entity.Assets.Add(currentAsset = new Asset
            {
                //CreatedDate = DateTime.UtcNow
            });
        }
        if (currentAsset.WorkflowStatus == Packages.Entities.DrmEntities.WorkflowStatus.Succeeded
            && WorkflowStatus == Packages.Entities.DrmEntities.WorkflowStatus.Failed)
            return entity;

        if (AssetId.HasValue) currentAsset.Id = AssetId;
        currentAsset.Id ??= Guid.NewGuid();

        currentAsset.Type = Type ?? currentAsset.Type;
        currentAsset.SubType = SubType ?? currentAsset.SubType;
        currentAsset.Duration = Duration ?? currentAsset.Duration;
        currentAsset.LifeCyclePolicies = LifeCyclePolicies ?? currentAsset.LifeCyclePolicies;
        currentAsset.Locale = Locale ?? currentAsset.Locale;
        currentAsset.ObjectUrl = ObjectUrl ?? currentAsset.ObjectUrl;
        currentAsset.PublicUrl = PublicUrl ?? currentAsset.PublicUrl;
        currentAsset.RestoredObjectUrl = RestoredObjectUrl ?? currentAsset.RestoredObjectUrl;
        currentAsset.WorkflowStatus = WorkflowStatus ?? Packages.Entities.DrmEntities.WorkflowStatus.Unknown;
        currentAsset.ModifiedDate = DateTime.UtcNow;
        currentAsset.IsDeleted = IsDeleted ?? false;
        currentAsset.FileSize = FileSize ?? currentAsset.FileSize;
        currentAsset.IpfsHash = IpfsHash ?? currentAsset.IpfsHash;
        currentAsset.FileName = FileName ?? currentAsset.FileName;
        currentAsset.CreatedDate = currentAsset.ModifiedDate;
        currentAsset.IsPublic = IsPublic ?? currentAsset.IsPublic;
        currentAsset.UploaderUserId = OwnerId == default ? null : OwnerId.ToString();

        // set content duration to asset duration if not yet set
        if (currentAsset.Duration > 0 && entity.Duration <= 0) entity.Duration = currentAsset.Duration;
        // or overwrite current, if the asset is the new original (eg: chime screen recording)
        if (currentAsset.Duration > 0 && currentAsset.SubType == AssetSubType.Original)
            entity.Duration = currentAsset.Duration;

        return entity;
    }
}