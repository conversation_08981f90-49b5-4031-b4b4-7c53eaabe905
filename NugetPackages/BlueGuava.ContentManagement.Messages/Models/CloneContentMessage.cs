﻿using System;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class CloneContentMessage : UpdateMessage
{
    /// <summary> The identifier of the new content; generates a new one, of none was given </summary>
    /// <remarks> <b>Important</b>: In case of ID collision the content will be overwritten! </remarks>
    public Guid? DesiredId { get; set; }

    /// <summary> Suffix for 'OriginalTitle' of the new content object </summary>
    /// <remarks> If none given, defaults to '<c> -- Copy {DateTime}</c>' </remarks>
    public string? NameSuffix { get; set; }

    /// <summary> Detemines if the new content will keep the assets of the original </summary>
    /// <remarks> The default value is <see langword="false"/> (meaning: clear the asset list) </remarks>
    public bool KeepAssets { get; set; }

    /// <summary> The type of the copy of the content </summary>
    /// <remarks> Keeps the original, if invalid or not specified </remarks>
    public ContentType? NewType { get; set; }

    /// <summary> The <see cref="Content.ExternalId"/> of the new content </summary>
    /// <remarks> Always replaces the value received from the original object </remarks>
    public string? ExternalId { get; set; }


    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        var content = entity.RecursiveCopy();

        content.Published = false;
        content.CreatedDate = DateTime.UtcNow;
        content.LastModifiedDate = DateTime.UtcNow;
        content.Id = DesiredId ?? Guid.NewGuid();
        content.ExternalId = ExternalId;

        if (NewType > 0 && Enum.IsDefined(typeof(ContentType), NewType))
            content.Type = NewType.Value;

        if (OwnerId.HasValue && OwnerId != Guid.Empty)
            content.OwnerId = OwnerId.Value;
        content.LastModifiedBy = OwnerId ?? Guid.Empty;

        if (string.IsNullOrEmpty(NameSuffix)) // we must have a suffix
            NameSuffix += $"-- Copy {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}";
        content.OriginalTitle += " " + NameSuffix.Trim();

        if (KeepAssets) return content;

        content.Assets?.Clear();
        content.Duration = 0;

        return content;
    }
}