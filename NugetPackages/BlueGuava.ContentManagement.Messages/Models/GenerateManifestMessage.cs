﻿using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class GenerateManifestMessage : UpdateMessage
{
    public GenerateManifestMessage()
    {
    }

    public GenerateManifestMessage(string contentId)
    {
        ContentId = contentId;
    }

    internal override Content? UpdateEntity(Content? entity)
    {
        //do nothing
        return entity;
    }
}