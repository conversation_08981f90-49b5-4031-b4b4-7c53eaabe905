using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class UpdateRelationsMessage : UpdateMessage
{
    public UpdateRelationsMessage()
    {
    }

    public UpdateRelationsMessage(Guid customerId, ListUpdateStrategy updateStrategy)
    {
        Id = customerId.ToString();
        UpdateStrategy = updateStrategy;
    }

    private ListUpdateStrategy UpdateStrategy { get; }
    public List<string>? Relations { get; set; }

    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        if (!(Relations?.Count > 0)) return entity; // nothing to do
        entity.Relations ??= new List<string>();

        switch (UpdateStrategy)
        {
            case ListUpdateStrategy.Overwrite:
                entity.Relations = Relations;
                break;
            case ListUpdateStrategy.Add:
                entity.Relations.Append(Relations);
                break;
            case ListUpdateStrategy.Remove:
                entity.Relations.Extract(Relations);
                break;
            default: throw new NotSupportedException("Not supported update strategy: " + UpdateStrategy);
        }

        return entity;
    }
}