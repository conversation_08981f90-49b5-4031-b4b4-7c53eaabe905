﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class BaseDataMessage : UpdateMessage
{
    public ContentType? Type { get; set; }

    public string? OriginalFileName { get; set; }

    public string? OriginalTitle { get; set; }

    public string? Color { get; set; }

    public DateTime? PublishedDate { get; set; }

    public DateTime? ReleaseDate { get; set; }

    public DateTime? ForceIndexed { get; set; }

    public bool? Duplicate { get; set; }

    public bool? Downloadable { get; set; }

    public bool? AllowMinting { get; set; }
    public bool? AllowEmailNotification { get; set; }
    public bool? AllowRemix { get; set; }

    public bool? AllowComments { get; set; }

    public bool? AllowUserRating { get; set; }

    public bool? AllowChat { get; set; }

    public bool? AllowSideshow { get; set; }

    public bool? Published { get; set; }
    public bool? IsDrmEnabled { get; set; }

    public Guid? LastModifiedBy { get; set; }

    public DateTime? LastModifiedDate { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? ExternalId { get; set; }

    public string? ReferenceId { get; set; }

    public int? Duration { get; set; }

    public string? OriginalLanguage { get; set; }

    public string? OriginalTranscript { get; set; }

    public string? ArchivalPolicy { get; set; }

    public string? DeletionPolicy { get; set; }

    [Obsolete("Use UpdateEntitiesMessage instead")]
    public Dictionary<EntityType, List<string>>? Entities { get; set; }

    public int? InternalPrice { get; set; }
    public double? TokenPrice { get; set; }
    public string? TokenCurrency { get; set; }
    public ProcessingStatus? ProcessingStatus { get; set; }
    public Visibility? Visibility { get; set; }
    public bool? HasTags { get; set; }
    public bool? HasSubtitle { get; set; }
    public Dictionary<string, string>? Properties { get; set; }
    public PublishingRule? PublishingRule { get; set; }
    public ContentNotification? Notification { get; set; }

    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        /* current.Id = this.Id; // IDs must be the same */
        entity.Type = Type ?? entity.Type;

        entity.AllowUserRating = AllowUserRating ?? entity.AllowUserRating;
        entity.AllowComments = AllowComments ?? entity.AllowComments;
        entity.AllowMinting = AllowMinting ?? entity.AllowMinting;
        entity.AllowEmailNotification = AllowEmailNotification ?? entity.AllowEmailNotification;
        entity.AllowChat = AllowChat ?? entity.AllowChat;
        entity.AllowRemix = AllowRemix ?? entity.AllowRemix;
        entity.AllowSideshow = AllowSideshow ?? entity.AllowSideshow;
        entity.Color = Color ?? entity.Color;
        entity.Downloadable = Downloadable ?? entity.Downloadable;
        entity.Duplicate = Duplicate ?? entity.Duplicate;
        entity.Duration = Duration ?? entity.Duration;
        entity.IsDrmEnabled = IsDrmEnabled ?? entity.IsDrmEnabled;
        entity.ExternalId = ExternalId ?? entity.ExternalId;
        entity.LastModifiedBy = OwnerId ?? Guid.Empty;
        entity.LastModifiedDate = DateTime.UtcNow;
        entity.OriginalFileName = OriginalFileName ?? entity.OriginalFileName;
        entity.OriginalTitle = OriginalTitle ?? entity.OriginalTitle;
        entity.OwnerId = OwnerId ?? entity.OwnerId;
        entity.Published = Published ?? entity.Published;
        entity.PublishedDate = PublishedDate ?? entity.PublishedDate;
        entity.ReferenceId = ReferenceId ?? entity.ReferenceId;
        entity.ReleaseDate = ReleaseDate ?? entity.ReleaseDate;
        entity.OriginalLanguage = OriginalLanguage ?? entity.OriginalLanguage;
        entity.OriginalTranscript = OriginalTranscript ?? entity.OriginalTranscript;
        entity.ArchivalPolicy = ArchivalPolicy ?? entity.ArchivalPolicy;
        entity.DeletionPolicy = DeletionPolicy ?? entity.DeletionPolicy;
#pragma warning disable CS0618 // Type or member is obsolete
        entity.Entities = Entities ?? entity.Entities;
#pragma warning restore CS0618 // Type or member is obsolete

        entity.InternalPrice = InternalPrice ?? entity.InternalPrice;
        entity.TokenPrice = TokenPrice ?? entity.TokenPrice;
        entity.TokenCurrency = TokenCurrency ?? entity.TokenCurrency;
        entity.ProcessingStatus = ProcessingStatus ?? entity.ProcessingStatus;
        entity.Visibility = Visibility ?? entity.Visibility;
        entity.PublishingRule = PublishingRule ?? entity.PublishingRule;
        entity.Notification = Notification ?? entity.Notification;
        entity.Properties ??= new Dictionary<string, string>();

        Properties ??= new Dictionary<string, string>();
        foreach (var kvp in Properties) entity.Properties[kvp.Key] = kvp.Value;

        return entity;
    }
}