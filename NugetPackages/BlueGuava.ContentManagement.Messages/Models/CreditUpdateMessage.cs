﻿using System;
using System.Collections.Generic;
using System.Linq;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class CreditUpdateMessage : UpdateMessage
{
    public string? Name { get; set; }
    public List<Role> Roles { get; set; } = new();
    public string? Character { get; set; }


    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        entity.Credits ??= new List<Credit>();
        var entry = FindCreditsEntry(entity);

        if (entry == null) // create and add for update
            entity.Credits.Add(entry = new Credit());

        entry.Character = Character;
        entry.Name = Name;
        entry.Roles = Roles;

        return entity;
    }

    private Credit? FindCreditsEntry(Content? entity)
    {
        if (entity == null) return null;
        var cmp = StringComparer.InvariantCultureIgnoreCase;

        var byName = entity.Credits?
            .Where(c => !string.IsNullOrEmpty(c.Name))
            .Where(c => cmp.Equals(c.Name, Name))
            .Take(2).ToArray();
        if (byName?.Length == 1) return byName[0];

        var byCharacter = entity.Credits?
            .Where(c => !string.IsNullOrEmpty(c.Character))
            .Where(c => cmp.Equals(c.Character, Character))
            .Take(2).ToArray();
        if (byCharacter?.Length == 1) return byCharacter[0];

        return null; // or throw?
    }
}