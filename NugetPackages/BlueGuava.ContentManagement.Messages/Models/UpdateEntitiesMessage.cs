﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class UpdateEntitiesMessage : UpdateMessage
{
    public ListUpdateStrategy UpdateStrategy { get; set; }

    public Dictionary<EntityType, List<string>>? Entities { get; set; }


    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        if (!(Entities?.Count > 0)) return entity; // nothing to do
        entity.Entities ??= new Dictionary<EntityType, List<string>>();

        switch (UpdateStrategy)
        {
            case ListUpdateStrategy.Overwrite:
                entity.Entities.OverwriteLists(Entities);
                break;
            case ListUpdateStrategy.Add:
                entity.Entities.AddToLists(Entities);
                break;
            case ListUpdateStrategy.Remove:
                entity.Entities.DelFromLists(Entities);
                break;
            default: throw new NotSupportedException("Not supported update strategy: " + UpdateStrategy);
        }

        return entity;
    }
}