using System;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Messages.Models;

public class ContentIngestMessage : IngestContentRequestV2, IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();

    public string? UserData { get; set; }

    string? IQueueItem.Receipt { get; set; }

    string? IQueueItem.GroupId
    {
        get => $"{Id}";
        set => _ = value;
    }

    string? IQueueItem.DeduplicationId
    {
        get => $"{Id}";
        set => _ = value;
    }

    public string? Sender { get; set; }
}