﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class LocalizationMessage : UpdateMessage
{
    public string? LanguageCode { get; set; }

    public Dictionary<string, string>? Localizations { get; set; }


    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        if (string.IsNullOrEmpty(LanguageCode))
            LanguageCode = "--";

        entity.Localizations ??= new Dictionary<string, Localization>();
        if (!entity.Localizations.TryGetValue(LanguageCode, out var localization))
            localization = entity.Localizations[LanguageCode] = new Localization();

        var classType = typeof(Localization);
        if (Localizations == null) return entity;

        foreach (var loc in Localizations)
        {
            var property = classType.GetProperty(loc.Key) // find by name or throw exception
                           ?? throw new InvalidOperationException($"Unknown localization property {loc.Key}");
            property.SetValue(localization, loc.Value);
        }

        return entity;
    }
}