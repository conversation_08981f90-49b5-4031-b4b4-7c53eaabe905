﻿using System;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class ReindexContentMessage : UpdateMessage
{
    public bool MigrateChapterMarkers { get; set; }
    public bool CalculateLabels { get; set; }
    public bool FixDuration { get; set; }
    public bool SKUPackageSetup { get; set; }
    public Guid? AuthGroupId { get; set; }
    public bool CleanDeletedFile { get; set; }

    public ReindexContentMessage()
    {
    }

    public ReindexContentMessage(Guid contentId)
    {
        Id = contentId.ToString();
        ContentId = Id;
    }

    internal override Content? UpdateEntity(Content? entity)
    {
        return entity;
    }
}