using System;
using System.Text.Json.Serialization;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Messages.Models;

public class JoinProjectMessage : IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();

    [JsonConverter(typeof(JsonStringEnumConverter))]
    public ProjectActivityType ActivityType { get; set; }

    public string? Token { get; set; }
    public string? Receipt { get; set; }

    public string? GroupId
    {
        get => $"{ActivityType}";
        set => _ = value;
    }

    public string? DeduplicationId
    {
        get => $"{Id}";
        set => _ = value;
    }

    public string? Sender { get; set; }
}