﻿using System;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Messages.Models;

public abstract class UpdateMessage : IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();

    public string? JobId { get; set; }

    /// <summary>
    /// JobType enum for logging and job status change purposes
    /// </summary>
    public int JobType { get; set; } = -1; // JobType.NONE

    /// <summary>
    /// JobSubType enum for logging and job status change purposes
    /// </summary>
    public int JobSubType { get; set; } = -1; // NONE

    public string? ContentId { get; set; }

    public Guid? OwnerId { get; set; }
    public string? OwnerName { get; set; }
    public string? OwnerEmail { get; set; }

    string? IQueueItem.Receipt { get; set; }

    string? IQueueItem.GroupId
    {
        get => $"{ContentId}";
        set => _ = value;
    }

    string? IQueueItem.DeduplicationId
    {
        get => $"{Id}";
        set => _ = value;
    }

    public string? Sender { get; set; }

    internal abstract Content? UpdateEntity(Content? entity);
}