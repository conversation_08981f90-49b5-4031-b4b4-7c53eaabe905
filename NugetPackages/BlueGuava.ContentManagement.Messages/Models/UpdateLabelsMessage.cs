﻿using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Messages.Models;

public sealed class UpdateLabelsMessage : UpdateMessage
{
    public ListUpdateStrategy UpdateStrategy { get; set; }

    public Dictionary<LabelType, List<string>>? Labels { get; set; }


    internal override Content? UpdateEntity(Content? entity)
    {
        if (entity == null) return null;

        if (!(Labels?.Count > 0)) return entity; // nothing to do
        entity.Labels ??= new Dictionary<LabelType, List<string>>();

        switch (UpdateStrategy)
        {
            case ListUpdateStrategy.Overwrite:
                entity.Labels.OverwriteLists(Labels);
                break;
            case ListUpdateStrategy.Add:
                entity.Labels.AddToLists(Labels);
                break;
            case ListUpdateStrategy.Remove:
                entity.Labels.DelFromLists(Labels);
                break;
            default: throw new NotSupportedException("Not supported update strategy: " + UpdateStrategy);
        }

        return entity;
    }
}