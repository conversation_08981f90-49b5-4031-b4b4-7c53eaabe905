﻿<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="../BlueGuava.ContentManagement.PackagesVersion.csproj" />

    <PropertyGroup>
        <TargetFrameworks>net8.0</TargetFrameworks>
        <Description>Content V2 models for asynchronous messaging (SQS)</Description>
        <Authors>Blue Guava Technology</Authors>
        <Company>Blue Guava Technology</Company>
        <Copyright>Blue Guava Technology</Copyright>
        <LangVersion>12</LangVersion>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Packages.Entities\BlueGuava.ContentManagement.Packages.Entities.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Integration\BlueGuava.ContentManagement.Integration.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="BlueGuava.Library.Constants" Version="8.1.4" />
        <PackageReference Include="BlueGuava.MessageQueuing.Abstractions" Version="8.1.1" />
    </ItemGroup>

</Project>