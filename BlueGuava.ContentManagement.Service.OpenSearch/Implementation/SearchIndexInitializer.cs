﻿using System;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;

namespace BlueGuava.ContentManagement.Service.OpenSearch.Implementation;

internal class SearchIndexInitializer : IHostedService
{
    private readonly ILogger<SearchIndexInitializer> logger;
    private readonly ISearchAdapter searchAdapter;
    private readonly IFeatureManager featureManager;

    public SearchIndexInitializer(
        ILogger<SearchIndexInitializer> logger,
        ISearchAdapter searchAdapter,
        IFeatureManager featureManager)
    {
        this.logger = logger;
        this.searchAdapter = searchAdapter;
        this.featureManager = featureManager;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            await searchAdapter.InitializeIndex(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.Standards(null, nameof(SearchIndexInitializer), nameof(StartAsync))
                .Log(LogLevel.Critical, ex, "Search index initialization error");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}