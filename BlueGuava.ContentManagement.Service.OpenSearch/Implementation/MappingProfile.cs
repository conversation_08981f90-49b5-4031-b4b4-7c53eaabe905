using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AutoMapper;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AWS.OpenSearch.Models.Mapping.V2;
using BlueGuava.Extensions.AWS.OpenSearch.Models.Mapping.V2.NestedTypes;
using BlueGuava.Library;
using BlueGuava.Library.Interop;
using OpenSearch.Client;
using Profile = AutoMapper.Profile;

namespace BlueGuava.ContentManagement.Service.OpenSearch.Implementation;

public class ContentV3SearchMappingProfile : Profile
{
    public ContentV3SearchMappingProfile()
    {
        CreateMap<Content, ContentSearchItem>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.ToString()))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => (int)src.Type))
            .ForMember(dest => dest.ContentId, opt => opt.MapFrom(src => src.Id.ToString()))
            .ForMember(dest => dest.ReleaseDate,
                opt => opt.MapFrom(src => MapDateTime(src.ReleaseDate ?? DateTime.MinValue)))
            .ForMember(dest => dest.CreatedDate, opt => opt.MapFrom(src => MapDateTime(src.CreatedDate)))
            .ForMember(dest => dest.LastModifiedDate, opt => opt.MapFrom(src => MapDateTime(src.LastModifiedDate)))
            .ForMember(dest => dest.PublishedDate,
                opt => opt.MapFrom(src => MapDateTime(src.PublishedDate ?? DateTime.MinValue)))
            .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => (src.Duration ?? 0).ToString("D10")))
            .ForMember(dest => dest.Keywords, opt => opt.MapFrom(src => src.OriginalTitle))
            .ForMember(dest => dest.Title,
                opt => opt.MapFrom(src => $"{src.OriginalTitle} {ExtractGlobalization(src, l => l.Name)}"))
            .ForMember(dest => dest.OriginalTitle, opt => opt.MapFrom(src => Normalize(src.OriginalTitle)))
            .ForMember(dest => dest.ShortInfo, opt => opt.MapFrom(src => ExtractGlobalization(src, l => l.ShortInfo)))
            .ForMember(dest => dest.Description,
                opt => opt.MapFrom(src => ExtractGlobalization(src, l => l.Description)))
            .ForMember(dest => dest.LastModifiedBy, opt => opt.MapFrom(src => src.LastModifiedBy.ToString()))
            .ForMember(dest => dest.OwnerId, opt => opt.MapFrom(src => src.OwnerId.ToString()))
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => MapTags(src)))
            .ForMember(dest => dest.Visibility, opt => opt.MapFrom(src => src.Visibility.ToString()))
            .ForMember(dest => dest.SquareImageUrl, opt => opt.MapFrom(src => MapSquareImageUrl(src)))
            .ForMember(dest => dest.Properties, opt => opt.MapFrom(src => MapProperties(src)))
            .ForMember(dest => dest.Countries, opt => opt.MapFrom(src => MapCountries(src)))
            .ForMember(dest => dest.Cities, opt => opt.MapFrom(src => MapCities(src)))
            .ForMember(dest => dest.Location, opt => opt.MapFrom(src => MapLocation(src)))
             .AfterMap((src, dest) =>
             {
                 dest.Properties ??= new Dictionary<string, object>();
                 dest.Properties[$"{Constants.CONTENT_ID}"] = src.Id;
                 dest.Properties[$"{Constants.CONTENT_TITLE}"] = src.OriginalTitle;
                 dest.Properties[$"{Constants.CONTENT_COLOR}"] = src.Color;
                 dest.Properties[$"{Constants.CONTENT_TYPE}"] = "Content";
                 dest.Properties[$"{Constants.CONTENT_SUBTYPE}"] = $"{src.Type}";
                 dest.Properties[$"{Constants.CONTENT_DURATION}"] = src.Duration;
                 dest.Properties[$"{Constants.CONTENT_RELEASEDATE}"] = src.ReleaseDate;
                 dest.Properties[$"{Constants.CONTENT_CREATEDDATE}"] = src.CreatedDate;
                 dest.Properties[$"{Constants.CONTENT_PUBLISHEDDATE}"] = src.PublishedDate;
                 dest.Properties[$"{Constants.CONTENT_EXTERNALID}"] = src.ExternalId;
                 dest.Properties[$"{Constants.CONTENT_REFERENCEID}"] = src.ReferenceId;
                 dest.Properties[$"{Constants.CONTENT_ORIGINAL_LANGUAGE}"] = src.OriginalLanguage;
                 dest.Properties[$"{Constants.CONTENT_ORIGINAL_TITLE}"] = src.OriginalTitle;
                 dest.Properties[$"{Constants.CONTENT_OWNER_ID}"] = src.OwnerId;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_DOWNLOAD}"] = src.Downloadable;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_MINTING}"] = src.AllowMinting;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_EMAIL_NOTIFICATION}"] = src.AllowEmailNotification;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_REMIX}"] = src.AllowRemix;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_COMMENT}"] = src.AllowComments;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_RATINGS}"] = src.AllowUserRating;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_CHAT}"] = src.AllowChat;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_SIDESHOW}"] = src.AllowSideshow;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_LYRICS}"] = src.AllowLyrics;
                 dest.Properties[$"{Constants.CONTENT_ALLOW_UPCOMING}"] = src.AllowUpcoming;
                 dest.Properties[$"Point{Constants.PRICE_AMOUNT}"] = src.InternalPrice;
                 dest.Properties[$"Point{Constants.PRICE_CURRENCY}"] = "PTS";
                 dest.Properties[$"Coin{Constants.PRICE_AMOUNT}"] = src.TokenPrice;
                 dest.Properties[$"Coin{Constants.PRICE_CURRENCY}"] = src.TokenCurrency;
                 dest.Properties[$"{Constants.CONTENT_VISIBILITY}"] = $"{src.Visibility}";
                 dest.Properties[$"{Constants.CONTENT_PUBLISHING_RULE}"] = $"{src.PublishingRule}";
                 dest.Properties[$"{Constants.CONTENT_NOTIFICATION}"] = $"{src.Notification}";
             });
        ;

        CreateMap<ContentSearchItem, Content>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => new Guid(src.ContentId)))
            .ForMember(dest => dest.Type, opt => opt.MapFrom<TypeEnumValueResolver>())
            .ForMember(dest => dest.Duration, opt => opt.MapFrom(src => TryParseDuration(src)))
            .ForMember(dest => dest.LastModifiedBy, opt => opt.MapFrom(src => MapGuid(src.LastModifiedBy)))
            .ForMember(dest => dest.OwnerId, opt => opt.MapFrom(src => MapGuid(src.OwnerId)))
            .ForMember(dest => dest.Visibility, opt => opt.MapFrom<VisibilityEnumValueResolver>())
            .ForMember(dest => dest.OriginalTitle,
                opt => opt.MapFrom(src => src.Keywords)) //this is intentional because the original title is normalized
            .AfterMap((src, dest) =>
            {
                var organizations = ConvertOrganizations(src);
                var entities = new Dictionary<EntityType, List<string>>();
                if (organizations != null) entities.Add(EntityType.ORGANIZATION, organizations.ToList()!);

                dest.Entities = entities;
            })
            .AfterMap((src, dest) =>
            {
                dest.Labels = src.Tags?
                    .Where(x =>
                        Enum.TryParse(x.Type.ToString(), out LabelType _)
                        && x?.Values?.Count > 0
                    )?
                    .ToDictionary(
                        x => Enum.TryParse(x.Type.ToString(), out LabelType labelType) ? labelType : LabelType.None,
                        y => y.Values.ToList());
            })
            .AfterMap((src, dest) =>
            {
                dest.Assets ??= new List<Asset>();
                if (!string.IsNullOrEmpty(src.SquareImageUrl))
                    dest.Assets.Add(new Asset()
                    {
                        Type = AssetType.Image,
                        SubType = SubType.Square,
                        PublicUrl = src.SquareImageUrl
                    });
            })
            ;
    }

    private object MapCities(Content src)
    {
        if (src.ExhibitionWindow == null) return null;

        return src.ExhibitionWindow.Where(city => city.Value.Cities != null).SelectMany(eh => eh.Value.Cities)
            ?.ToList();
    }

    private object MapCountries(Content src)
    {
        if (src.ExhibitionWindow == null) return null;

        return src.ExhibitionWindow.Select(ew => ew.Key).ToList();
    }

    private object MapLocation(Content src)
    {
        if (src.Properties != null &&
            src.Properties.TryGetValue("Content:Address:Lat", out var latStr) &&
            src.Properties.TryGetValue("Content:Address:Lng", out var lngStr) &&
            double.TryParse(latStr, out double lat) &&
            double.TryParse(lngStr, out double lon))
        {
            return new GeoLocation(lat, lon);
        }
        return null;
    }

    private static Dictionary<string, object>? MapProperties(Content src)
    {
        if (src.Properties == null || !src.Properties.Any())
            return new Dictionary<string, object>();

        string[] dateFormats = new[] { "MM/dd/yyyy", "M/d/yyyy", "yyyy-MM-dd", "yyyy-MM-ddTHH:mm:ss", "yyyy-MM-ddTHH:mm:ss.ffffff", "yyyy-MM-ddTHH:mm:ss.fffffff" };

        return src.Properties.ToDictionary(
            kvp => kvp.Key,
            kvp =>
            {
                if (kvp.Key.IndexOf("date", StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    if (DateTime.TryParseExact(kvp.Value, dateFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate))
                    {
                        return parsedDate;
                    }
                    if (DateTime.TryParse(kvp.Value, out parsedDate))
                    {
                        return parsedDate;
                    }
                }

                if (kvp.Key.Contains(":Stat:"))
                    return double.TryParse(kvp.Value, out var dblVal) ? (object)dblVal : (object)0;
                return (object)kvp.Value;
            });
    }
    private static List<TagNestedItem> MapTags(Content src)
    {
        var tags = new List<TagNestedItem>();

        src.Labels?.ForEach(label =>
        {
            if (label.Value?.Count > 0)
                tags.Add(new TagNestedItem()
                {
                    Values = label.Value.Where(x => !string.IsNullOrEmpty(x)).Select(x => x.ToString().ToLower())
                        .ToList(),
                    Type = label.Key.ToString()
                });
        });

        return tags;
    }

    private static string ExtractGlobalization(Content entity, Func<Localization, string?> selector)
    {
        return entity.Localizations != null && entity.Localizations.Count > 0
            ? string.Join(" ", entity.Localizations.Values.Select(selector))
            : string.Empty;
    }

    private static string MapSquareImageUrl(Content src)
    {
        return src.Assets?.FirstOrDefault(x => x is { Type: AssetType.Image, SubType: SubType.Square })?.PublicUrl ??
               string.Empty;
    }

    private static IEnumerable<string>? ConvertOrganizations(ContentSearchItem src)
    {
        return src.Relations != null && src.Relations.Any(x => x.Contains("Content_Organization_"))
            ? src.Relations.Where(x => x.Contains("Content_Organization_"))
                .Select(x => x.Split("Content_Organization_")[1])
            : null;
    }

    private static int TryParseDuration(ContentSearchItem src)
    {
        return int.TryParse(src.Duration, out var dur) ? dur : 0;
    }

    private static Guid MapGuid(string guid)
    {
        return string.IsNullOrEmpty(guid)
            ? Guid.Empty
            : new Guid(guid);
    }

    private static string? Normalize(string? str)
    {
        return string.IsNullOrEmpty(str) ? string.Empty : str.RemoveDiacritics()?.ToLower();
    }

    private static string? MapDateTime(DateTime dateTime)
    {
        return dateTime.ToString(Constants.DateTimeFormat);
    }
}

public class TypeEnumValueResolver : IValueResolver<ContentSearchItem, Content, ContentType>
{
    public ContentType Resolve(ContentSearchItem source, Content destination, ContentType destMember,
        ResolutionContext context)
    {
        if (Enum.TryParse<ContentType>(source.Type.ToString(), out var t))
            return t;

        return ContentType.None;
    }
}

public class VisibilityEnumValueResolver : IValueResolver<ContentSearchItem, Content, Visibility?>
{
    public Visibility? Resolve(ContentSearchItem source, Content destination, Visibility? destMember,
        ResolutionContext context)
    {
        if (Enum.TryParse<Visibility>(source.Visibility, out var vis))
            return vis;

        return Visibility.Public;
    }
}