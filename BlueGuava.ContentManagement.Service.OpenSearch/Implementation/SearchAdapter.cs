using System;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Exceptions;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.AWS.OpenSearch.Models.Indexing.V2;
using BlueGuava.Extensions.AWS.OpenSearch.Models.Mapping.V2;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using OpenSearch.Client;
using Polly;
using Polly.Bulkhead;
using Polly.Wrap;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Service.OpenSearch.Implementation;

public class SearchAdapter : ISearchAdapter
{
    private readonly AsyncPolicyWrap wrappedPolicy;
    private readonly ILogger<SearchAdapter> logger;
    private readonly IFeatureManager featureManager;
    private readonly IOpenSearchClient client;
    private readonly IMapper mapper;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IndexDefinition indexDefinition = new ContentIndexDefinition();

    public SearchAdapter
    (
        ILogger<SearchAdapter> logger,
        IOptionsMonitor<CommunicationSettings> options,
        IFeatureManager featureManager,
        IOpenSearchClient client,
        IMapper mapper,
        ICorrelationContextAccessor correlationContextAccessor,
        AsyncBulkheadPolicy bulkheadPolicy)
    {
        this.logger = logger;
        this.featureManager = featureManager;
        this.client = client;
        this.mapper = mapper;
        this.correlationContextAccessor = correlationContextAccessor;

        wrappedPolicy = bulkheadPolicy.WrapAsync(Policy.Handle<NetworkConnectionException>()
            .WaitAndRetryAsync(options.CurrentValue.CircuitBreakerThreshold,
                i => options.CurrentValue.CircuitBreakerDuration,
                (ex, span) => logger.LogError("Circuit breaker open for {Span} because {Message}", span, ex.Message)
            ));
    }

    public async Task InitializeIndex(CancellationToken cancellationToken = default)
    {
        try
        {

            await wrappedPolicy.ExecuteAsync(async (cancel) =>
            {
                var response = await client.Indices.ExistsAsync(indexDefinition.Name, null, cancel);

                if (!response.ApiCall.SuccessOrKnownError)
                    throw new NetworkConnectionException(
                        $"Elastic Search Index Exists response status: {response.IsValid} DebugInformation: {response.DebugInformation}");

                if (!response.Exists)
                {
                    logger.LogWarning(
                        "Elastic Search Index create response status: {Status} Creating Index {IndexName} because debugInformation: {Message}",
                        response.IsValid, indexDefinition.Name, response.DebugInformation);

                    var createIndexResponse = await indexDefinition.Create(client);

                    if (logger.IsEnabled(LogLevel.Trace))
                        logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(InitializeIndex))
                            .Log(LogLevel.Trace,
                                "Elastic Search Index create response status: {Status} DebugInformation: {Message}",
                                createIndexResponse.IsValid, createIndexResponse.DebugInformation);

                    if (!createIndexResponse.ApiCall.SuccessOrKnownError)
                        throw new NetworkConnectionException(
                            $"Elastic Search Index create response status: {createIndexResponse.IsValid} DebugInformation: {createIndexResponse.DebugInformation}");
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(InitializeIndex))
                .Log(LogLevel.Critical, ex, "Search index initialization error");
            throw;
        }
    }

    public async Task DeleteIndex(CancellationToken cancellationToken = default)
    {
        try
        {
            await wrappedPolicy.ExecuteAsync(async (cancel) =>
            {
                var response = await client.Indices.DeleteAsync(new DeleteIndexRequest(indexDefinition.Name),
                    cancel);

                if (!response.ApiCall.SuccessOrKnownError)
                    throw new NetworkConnectionException(
                        $"Elastic Delete Index response status: {response.IsValid} DebugInformation: {response.DebugInformation}");
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(DeleteIndex))
                .Log(LogLevel.Critical, ex, "Search index initialization error");
            throw;
        }
    }

    public async Task Save(Content entity, CancellationToken cancellationToken = default)
    {
        try
        {
            await wrappedPolicy.ExecuteAsync(async (cancel) =>
            {
                try
                {
                    var searchItem = mapper.Map<ContentSearchItem>(entity);
                    //NULL to prevent update from this service, because it is updated from the collection-service
                    searchItem.Relations = entity.Relations;
                    var response = await Exists(searchItem, cancel);

                    if (response.Exists)
                    {
                        var updateResponse = await client.IndexAsync<ContentSearchItem>(
                            searchItem, b => b.Index(indexDefinition.Name).Id(searchItem.Id), cancel);

                        if (!updateResponse.ApiCall.SuccessOrKnownError)
                            throw new NetworkConnectionException(
                                $"Elastic Search Item Create response status: {updateResponse.IsValid} DebugInformation: {updateResponse.DebugInformation}");

                        if (logger.IsEnabled(LogLevel.Trace))
                            logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(Save))
                                .Log(LogLevel.Trace,
                                    "Elastic Search Item Update response status: {Status} DebugInformation: {Message}",
                                    updateResponse.IsValid, updateResponse.DebugInformation);
                    }
                    else
                    {
                        var indexResponse =
                            await client.IndexAsync(searchItem, b => b.Index(indexDefinition.Name), cancel);

                        if (!indexResponse.ApiCall.SuccessOrKnownError)
                            throw new NetworkConnectionException(
                                $"Elastic Search Item Create response status: {indexResponse.IsValid} DebugInformation: {indexResponse.DebugInformation}");

                        if (logger.IsEnabled(LogLevel.Trace))
                            logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(Save))
                                .Log(LogLevel.Trace,
                                    "Elastic Search Item Create response status: {Status} DebugInformation: {Message}",
                                    indexResponse.IsValid, indexResponse.DebugInformation);
                    }
                }
                catch (Exception ex)
                {
                    logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(Save))
                        .Log(LogLevel.Error, ex, "Search index initialization error");
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(Save))
                .Log(LogLevel.Error, ex, "Search index initialization error");
        }
    }

    public async Task Delete(Guid id, CancellationToken cancellationToken = default)
    {
        await wrappedPolicy.ExecuteAsync(async (cancel) =>
        {
            var response = await client.DeleteByQueryAsync<SearchItemDocumentBase>(q => q
                .Index(Indices.Index(new[] { indexDefinition.Name }))
                .Query(x => x.Match(m => m.Field(f => f.Id).Query(id.ToString()))), cancel);

            if (logger.IsEnabled(LogLevel.Trace))
                logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(Delete))
                    .Log(LogLevel.Trace,
                        "Elastic Search Deletion response status: {Status} DebugInformation:  {Message}",
                        response.IsValid, response.DebugInformation);

            if (!response.ApiCall.Success)
                throw new NetworkConnectionException(
                    $"Elastic Search Deletion response status: {response.IsValid} DebugInformation: {response.DebugInformation}");
        }, cancellationToken);
    }

    private async Task<ExistsResponse> Exists(ContentSearchItem searchItem,
        CancellationToken cancellationToken = default)
    {
        var response = await client.DocumentExistsAsync<ContentSearchItem>(
            searchItem, b => b.Index(indexDefinition.Name), cancellationToken);

        if (!response.ApiCall.SuccessOrKnownError)
            throw new NetworkConnectionException(
                $"Elastic Search Exists response status: {response.IsValid} DebugInformation: {response.DebugInformation}");

        if (logger.IsEnabled(LogLevel.Trace))
            logger.Standards(correlationContextAccessor, nameof(SearchAdapter), nameof(Exists))
                .Log(LogLevel.Trace, "Elastic Search Exists response status: {Status} DebugInformation:  {Message}",
                    response.IsValid, response.DebugInformation);

        return response;
    }

    public async Task<bool> IsHealthy()
    {
        try
        {
            var response = await client.Indices.ExistsAsync(indexDefinition.Name, null, CancellationToken.None);

            if (!response.ApiCall.SuccessOrKnownError)
                throw new NetworkConnectionException(
                    $"Elastic Search Index Exists response status: {response.IsValid} DebugInformation: {response.DebugInformation}");

            return response.Exists;
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Failed to connect to OpenSearch");
            return false;
        }
    }
}