using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Service.OpenSearch.Implementation;
using BlueGuava.Extensions.AWS.OpenSearch;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Polly;
using System;

namespace BlueGuava.ContentManagement.Service.OpenSearch;

public static class OpenSearchServiceSetup
{
    public static IServiceCollection AddContentOpenSearch(this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure enhanced Bulkhead Policy with circuit breaker and rate limiting
        services.AddSingleton(provider =>
        {
            // Create circuit breaker policy to prevent cascading failures
            var circuitBreakerPolicy = Policy
                .Handle<Exception>()
                .CircuitBreakerAsync(
                    handledEventsAllowedBeforeBreaking: 5,
                    durationOfBreak: TimeSpan.FromSeconds(30),
                    onBreak: (exception, duration) =>
                    {
                        // Log circuit breaker opening
                        var logger = provider.GetService<Microsoft.Extensions.Logging.ILogger<OpenSearchService>>();
                        logger?.LogWarning("OpenSearch circuit breaker opened for {Duration} due to: {Exception}",
                            duration, exception.Message);
                    },
                    onReset: () =>
                    {
                        // Log circuit breaker closing
                        var logger = provider.GetService<Microsoft.Extensions.Logging.ILogger<OpenSearchService>>();
                        logger?.LogInformation("OpenSearch circuit breaker reset");
                    });

            // Create bulkhead policy with reduced concurrency to prevent CPU spikes
            // Reduced from 10 to 5 concurrent operations, max 50 queued operations
            var bulkheadPolicy = Policy.BulkheadAsync(5, 50);

            // Combine circuit breaker with bulkhead for comprehensive protection
            var combinedPolicy = Policy.WrapAsync(circuitBreakerPolicy, bulkheadPolicy);

            return combinedPolicy;
        });

        services.AddFeatureManagement();
        services.AddHostedService<SearchIndexInitializer>();
        services.Configure<CommunicationSettings>(configuration.GetSection("CommunicationSettings"));
        services.Configure<BoostFactors>(configuration.GetSection("SearchBoostFactors"));
        services.AddScoped<IOpenSearchService, OpenSearchService>();
        services.AddSingleton<ISearchAdapter, SearchAdapter>();
        services.AddOpenSearchClient(configuration);
        return services;
    }
}