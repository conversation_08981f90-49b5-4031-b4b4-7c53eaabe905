using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Service.OpenSearch.Implementation;
using BlueGuava.Extensions.AWS.OpenSearch;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Polly;
using System;

namespace BlueGuava.ContentManagement.Service.OpenSearch;

public static class OpenSearchServiceSetup
{
    public static IServiceCollection AddContentOpenSearch(this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure enhanced Bulkhead Policy with circuit breaker and rate limiting
        services.AddSingleton(provider =>
        {
            // Create retry policy with exponential backoff to handle transient failures
            var retryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        var logger = provider.GetService<Microsoft.Extensions.Logging.ILogger<OpenSearchService>>();
                        logger?.LogWarning("OpenSearch retry attempt {RetryCount} after {Delay}ms due to: {Exception}",
                            retryCount, timespan.TotalMilliseconds, outcome.Exception?.Message);
                    });

            // Create bulkhead policy with reduced concurrency to prevent CPU spikes
            // Reduced from 10 to 5 concurrent operations, max 50 queued operations
            var bulkheadPolicy = Policy.BulkheadAsync(5, 50);

            // Combine retry with bulkhead for comprehensive protection
            var combinedPolicy = Policy.WrapAsync(retryPolicy, bulkheadPolicy);

            return combinedPolicy;
        });

        services.AddFeatureManagement();
        services.AddHostedService<SearchIndexInitializer>();
        services.Configure<CommunicationSettings>(configuration.GetSection("CommunicationSettings"));
        services.Configure<BoostFactors>(configuration.GetSection("SearchBoostFactors"));
        services.AddScoped<IOpenSearchService, OpenSearchService>();
        services.AddSingleton<ISearchAdapter, SearchAdapter>();
        services.AddOpenSearchClient(configuration);
        return services;
    }
}