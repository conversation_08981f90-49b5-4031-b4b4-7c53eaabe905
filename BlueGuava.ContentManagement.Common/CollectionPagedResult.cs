using System.Collections.Generic;
namespace BlueGuava.ContentManagement.Common;

public class CollectionPagedResult<TData>
{
    public CollectionPagedResult(List<TData> data, string token)
    {
        this.PageContent = data;
        this.PagingToken = token;
    }

    public List<TData> PageContent { get; set; }

    public string PagingToken { get; set; }

    public bool Finished => string.IsNullOrEmpty(this.PagingToken);
}
