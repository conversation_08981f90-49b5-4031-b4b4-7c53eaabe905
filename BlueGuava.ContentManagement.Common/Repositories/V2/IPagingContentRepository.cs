﻿using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.Paging;

namespace BlueGuava.ContentManagement.Common.Repositories.V2;

public interface IPagingContentRepository
{
    Task<PagedResult<Content?>> StartPaging(int itemLimit);
    Task<PagedResult<Content?>> GetNextPage(string pagingToken);
    Task<PagedResult<ContentPoll>> StartPagingByReference(string referenceId, int itemLimit);
    Task<PagedResult<ContentPoll>> GetNextPageByReference(string referenceId, string token);
}