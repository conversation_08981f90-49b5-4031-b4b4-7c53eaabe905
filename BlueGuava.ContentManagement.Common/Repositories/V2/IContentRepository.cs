﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.HealthReporting;
using Asset = BlueGuava.ContentManagement.Packages.Entities.V2.Asset;
using Content = BlueGuava.ContentManagement.Packages.Entities.V2.Content;

namespace BlueGuava.ContentManagement.Common.Repositories.V2;

public interface IContentRepository : IHealthProvider
{
    Task Create(Content content);
    Task Update(Content content);
    Task<Content?> Retrieve(Guid contentId);
    Task<Content?> Retrieve(string externalId);
    Task Delete(Guid contentId);
    IAsyncEnumerable<Guid> ScanThrough(ContentSearch args);
    IAsyncEnumerable<Guid> ScanThroughWithType(int type);
    Task<bool> DoesExist(Content content);
    Task<bool> DoesExist(string? externalId);
    IAsyncEnumerable<Content?> RetrieveAll();
    IAsyncEnumerable<Guid> RetrieveAllIds();
    Task<IEnumerable<Content?>?> BatchGet(List<Guid> args);
    Task BatchSave(List<Content> contents);
    Task<Asset?> RetrieveAsset(Guid contentId, Guid assetId);
    Task<IEnumerable<ContentPoll>> RetrievePollsByExternalIdAndReferenceId(string externalId, string referenceId);
    Task<IEnumerable<ContentPoll>> RetrievePollsByReferenceId(string referenceId);
    Task SavePoll(ContentPoll poll);
    Task BatchSavePolls(IEnumerable<ContentPoll> polls);
}