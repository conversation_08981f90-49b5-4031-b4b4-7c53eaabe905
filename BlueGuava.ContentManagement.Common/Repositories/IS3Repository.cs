﻿namespace BlueGuava.ContentManagement.Common.Repositories;
// public interface IS3Repository
// {
//     Task PutFileAsync<T>(T payload, string bucketName, string key, string folderSuffix, Dictionary<string, string> tags = null) where T : class;
//     Task PutFileAsync(string payload, string bucketName, string fileKey, string typeTag, Dictionary<string, string> tags = null);
//     string GeneratePreSignedURL(double duration, string bucketName, string fileKey);
//     Task<HttpStatusCode> PutTagAsync(string bucketName, string fileKey, Dictionary<string, string> tags);
//     Task<HttpStatusCode> RemoveTagAsync(string bucketName, string fileKey, Dictionary<string, string> tags);
//     Task<(string Id, long Size)> GetFileSize(string bucketName, string fileKey, string id);
// }