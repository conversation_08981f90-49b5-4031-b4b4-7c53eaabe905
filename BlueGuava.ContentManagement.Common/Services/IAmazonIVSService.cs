﻿using System;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.IVS;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IAmazonIvsService
{
    /// <summary> Creates a live stream and a stream key to begin streaming </summary>
    /// <returns> connection information (channel and stream key) about the Amazon IVS live stream </returns>
    Task<LiveStreamInfo> CreateLiveStream(Guid contentId, bool record, IVSChannelLatencyMode channelLatencyMode,
        IVSChannelType channelType);

    /// <summary> Returns the live stream and key information associated with <paramref name="contentId"/> </summary>
    /// <returns> connection information (channel and stream key) or <see langword="null"/> </returns>
    Task<LiveStreamInfo> TryGetLiveStream(Guid contentId);

    /// <summary> Sets the specified <paramref name="metadata"/> for the Amazon IVS channel for <paramref name="channelArn"/> </summary>
    /// <remarks> Metadata can only be added to a currently broadcasting Amazon IVS live stream </remarks>
    /// <returns> <see langword="true"/> if the metadata was successfully added; otherwise <see langword="false"/> </returns>
    Task<bool> PutMetadata(string channelArn, string metadata);

    /// <summary> Tell Amazon IVS to stop the live stream </summary>
    /// <returns> whether the stream has already stopped </returns>
    Task<bool> StopLiveStream(string channelArn);

    /// <summary> Deletes the Amazon IVS live stream altogether </summary>
    Task RemoveLiveStream(string channelArn);


    /// <summary> Creates a stream key to a live stream to allow streaming again </summary>
    /// <returns> Streaming connection information (stream key) for the Amazon IVS live stream </returns>
    Task<StreamKeyInfo> CreateStreamKey(string channelArn);

    /// <summary> Deletes all stream keys from the live stream, so no new streams may be started </summary>
    /// <remarks> Stream keys are a way to allow users to start streaming on a channel </remarks>
    Task RemoveStreamKeys(string channelArn);


    /// <summary> Creates a chat room with <paramref name="contentId"/> for name </summary>
    /// <returns> chat room information </returns>
    Task<ChatRoomInfo> CreateChatRoom(Guid contentId);

    /// <summary> Returns a short-lived token enabling the <paramref name="user"/> to enter to the chat room for <paramref name="roomArn"/> </summary>
    /// <remarks> By default the user has receive and send message capabilities. By specifying <c><paramref name="moderator"/>: <see langword="true"/></c> the user will also be able to disconnect users, and delete messages </remarks>
    /// <returns> chat room connection token, and expiration dates </returns>
    Task<RoomKeyInfo> CreateRoomKey(string roomArn, ClaimsPrincipal user, bool moderator);

    /// <summary> Deletes the chat room <paramref name="roomArn"/> </summary>
    Task RemoveChatRoom(string roomArn);


    Task SendMessage(string roomArn, ClaimsPrincipal user, string? message = null);
    Task RemoveMessage(string roomArn, string messageId, string contentId, string ipAddress, string? reason = null);
    Task BanUser(string roomArn, string userId, string? reason = null);

    string RetrieveSocketUri();

}