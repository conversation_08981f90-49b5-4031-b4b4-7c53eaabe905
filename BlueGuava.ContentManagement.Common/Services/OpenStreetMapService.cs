﻿using BlueGuava.ContentManagement.Common.Models.OpenStreetMap;
using Newtonsoft.Json;
using System.Net.Http;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IOpenStreetMapService
{
    Task<LocationResponse> GetLocation(double latitude, double longitude);
}

public class OpenStreetMapService : IOpenStreetMapService
{
    private readonly HttpClient httpClient;

    public OpenStreetMapService(HttpClient httpClient)
    {
        this.httpClient = httpClient;
    }

    public async Task<LocationResponse> GetLocation(double latitude, double longitude)
    {
        var client = httpClient;
        var request = new HttpRequestMessage(HttpMethod.Get, $"?format=json&lat={latitude}&lon={longitude}");
        request.Headers.Add("accept", "application/json, text/plain, */*");
        request.Headers.Add("accept-language", "en-US,en;q=0.9");
        request.Headers.Add("user-agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36");
        var response = await client.SendAsync(request);
        response.EnsureSuccessStatusCode();
        return JsonConvert.DeserializeObject<LocationResponse>(await response.Content.ReadAsStringAsync());
    }
}