﻿using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.HttpRepository;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IImageProcessorService
{
    Task<ImageProcessor?> GenerateImage(string text, string contentId, CancellationToken cancellation = default);
}

public class ImageProcessorService : IImageProcessorService
{
    private readonly IHttpRepository imageHttpRepository;

    public ImageProcessorService(
        IHttpRepositoryProvider httpRepositoryProvider)
    {
        imageHttpRepository = httpRepositoryProvider.CreateHttpRepository("ImageProcessor");
    }

    public async Task<ImageProcessor?> GenerateImage(string text, string contentId,
        CancellationToken cancellation = default)
    {
        return await imageHttpRepository.CreateAsync<ImageProcessor>("/GenerateImageAsset",
            new GenerateImageRequest { Text = text, ContentId = contentId }, null, cancellation);
    }
}