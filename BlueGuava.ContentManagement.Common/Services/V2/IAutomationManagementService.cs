using System;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.Collections.Common;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Common.Services.V2;

public interface IAutomationManagementService
{
    /// <summary>
    /// Handles content creation automations
    /// <list type="number">
    ///     <item>
    ///         <term>Creates Chatroom (<see cref="BlueGuava.ContentManagement.Packages.Entities.V2.Enums.ContentType.LiveChat"/>) for the <paramref name="sourceContent"/> via <see cref="IAmazonIvsService"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Creates a WatchParty (<see cref="BlueGuava.ContentManagement.Packages.Entities.V2.Enums.ContentType.CameraCapture"/>) for the <paramref name="sourceContent"/> via <see cref="IAmazonIvsService"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    /// <param name="sourceContent"></param>
    /// <param name="user"></param>
    Task OnContentCreated(Content? sourceContent, ClaimsPrincipal user);

    /// <summary>
    /// Creates a chatroom for a <see cref="BlueGuava.ContentManagement.Packages.Entities.V2.Enums.ContentType.LiveStream"/>
    /// <list type="number">
    ///     <item>
    ///         <term>Creates a Chatroom (<see cref="BlueGuava.ContentManagement.Packages.Entities.V2.Enums.ContentType.LiveChat"/>) via <see cref="IAmazonIvsService"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links the <paramref name="liveSessionContent"/> with the <paramref name="user"/> via <see cref="CustomerRelation.Chat"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links the <paramref name="liveSessionContent"/> with the <paramref name="user"/> via <see cref="CustomerRelation.Activity"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    /// <param name="liveSessionContent"></param>
    /// <param name="user"></param>
    Task OnIVSLiveCreated(Content liveSessionContent, ClaimsPrincipal user);

    /// <summary>
    /// Handles user connections to a particular chat room
    /// <list type="number">
    ///     <item>
    ///         <term>Links <paramref name="customerId"/> to <paramref name="contentId"/> via <see cref="CustomerRelation.Chat"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links <paramref name="customerId"/> to <paramref name="contentId"/> via <see cref="CustomerRelation.Activity"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    Task OnChatConnect(Guid contentId, Guid customerId);

    /// <summary>
    /// Handles user connections to a particular chat room
    /// <list type="number">
    ///     <item>
    ///         <term>Unlinks <paramref name="customerId"/> and <paramref name="contentId"/> via <see cref="CustomerRelation.Chat"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Unlinks <paramref name="customerId"/> and <paramref name="contentId"/> via <see cref="CustomerRelation.Activity"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    Task OnChatDisconnect(Guid contentId, Guid customerId);

    /// <summary>
    /// Handles user connections to a particular watch party
    /// <list type="number">
    ///     <item>
    ///         <term>Links <paramref name="customerId"/> to <paramref name="contentId"/> via <see cref="CustomerRelation.Watchparty"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links <paramref name="customerId"/> to <paramref name="contentId"/> via <see cref="CustomerRelation.Activity"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    Task OnChimeConnect(Guid contentId, Guid customerId);

    /// <summary>
    /// Handles user connections to a particular watch party
    /// <list type="number">
    ///     <item>
    ///         <term>Unlinks <paramref name="customerId"/> and <paramref name="contentId"/> via <see cref="CustomerRelation.Watchparty"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Unlinks <paramref name="customerId"/> and <paramref name="contentId"/> via <see cref="CustomerRelation.Activity"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    Task OnChimeDisconnect(Guid contentId, Guid customerId);


    /// <summary>
    ///  adds the auto tags (Month and year) to content tags
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="createdDate"></param>
    /// <returns></returns>
    Task UpdateContentTags(Guid contentId, DateTime createdDate);
}