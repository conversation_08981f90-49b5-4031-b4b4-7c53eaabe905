﻿using System;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Common.Services.V2;

public interface IContentArchival
{
    Task<bool> ArchiveAllAsset(Content entity, ClaimsPrincipal user);

    Task<bool> AddArchiveTag(Guid entityId, Asset? asset, string lifeCycleKey, string lifeCycleTag,
        ClaimsPrincipal user);

    Task<bool> RemoveArchiveTag(Guid entityId, Asset? asset, string lifeCycleKey, string lifeCycleTag,
        ClaimsPrincipal user);
}