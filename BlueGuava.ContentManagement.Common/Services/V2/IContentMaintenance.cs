﻿using System.Collections.Generic;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Common.Services.V2;

/// <summary>
/// Common Content maintenance tasks
/// </summary>
public interface IContentMaintenance
{
    /// <summary>
    /// Updates various <see cref="Asset"/> fields of the <paramref name="content"/>'s <see cref="Content.Assets"/>
    /// </summary>
    /// <returns>property changed property names if fields were changed; otherwise, empty</returns>
    Task<IEnumerable<string>> UpdateContentFields(Content content);
}