using System.Threading;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common.Services.V3;

public interface IDeliveryService
{
    Task ReleaseContent(BlueGuava.ContentManagement.Packages.Entities.V2.Content? entity, bool recalculateCatalogs,
        CancellationToken cancellation = default);

    Task UnreleaseContent(BlueGuava.ContentManagement.Packages.Entities.V2.Content? entity,
        CancellationToken cancellation = default);
}