using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Integration.V2;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IContentIngestService
{
    Task<Content?> Ingest(IngestContentRequestV2 ingest, ClaimsPrincipal user);

    Task PostProcess(Content result, string url, ClaimsPrincipal user);

}