﻿using System;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.HealthReporting;

namespace BlueGuava.ContentManagement.Common.Services;

public interface ISearchAdapter : IHealthProvider
{
    Task Delete(Guid id, CancellationToken cancellationToken = default);
    Task InitializeIndex(CancellationToken cancellationToken = default);
    Task Save(Content entity, CancellationToken cancellationToken = default);
    Task DeleteIndex(CancellationToken cancellationToken = default);
}