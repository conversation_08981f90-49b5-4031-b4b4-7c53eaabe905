using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IDynamicManifestGenerator
{
    /// <summary>
    /// Generates manifests for the given content ID.
    /// </summary>
    /// <param name="contentId"></param>
    /// <returns></returns>
    Task GenerateManifests(string contentId);

    /// <summary>
    /// Triggers manifest generation for the given playlist.
    /// </summary>
    /// <param name="playlist"></param>
    /// <returns></returns>
    Task TriggerManifestGeneration(Content playlist);
}