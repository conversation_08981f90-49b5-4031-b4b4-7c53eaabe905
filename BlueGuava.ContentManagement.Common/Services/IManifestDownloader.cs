using System.Collections.Generic;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IManifestDownloader
{
    /// <summary>
    /// Downloads a manifest from the given URL.
    /// </summary>
    /// <param name="manifestUrl"></param>
    /// <returns></returns>
    Task<string?> DownloadManifestAsync(string manifestUrl);

    /// <summary>
    /// Downloads multiple manifests from the given URLs.
    /// </summary>
    /// <param name="manifestUrls"></param>
    /// <returns></returns>
    Task<Dictionary<string, string>> DownloadMultipleManifestsAsync(List<string> manifestUrls);
}