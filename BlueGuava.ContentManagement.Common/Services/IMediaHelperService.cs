﻿using BlueGuava.Extensions.Logging;
using BlueGuava.HttpRepository;
using CorrelationId;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common.Services
{
    public interface IMediaHelperService
    {
        Task<SkuResponse> GetSku(string id);
    }
    public class MediaHelperServiceClient : IMediaHelperService
    {
        private readonly IMemoryCache memoryCache;
        private readonly IHttpRepository mediaHelperRepository;
        private readonly ILogger<MediaHelperServiceClient> logger;
        private readonly ICorrelationContextAccessor correlationContextAccessor;

        public MediaHelperServiceClient(IHttpRepositoryProvider repositoryProvider, IMemoryCache memoryCache, ILogger<MediaHelperServiceClient> logger, ICorrelationContextAccessor correlationContextAccessor)
        {
            this.memoryCache = memoryCache;
            this.logger = logger;
            mediaHelperRepository = repositoryProvider.CreateHttpRepository(ServiceNames.MediaHelper);
            this.correlationContextAccessor = correlationContextAccessor;
        }

        public async Task<SkuResponse> GetSku(string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id)) throw new ApplicationException("Sku id should not be null");

                var uri = $"/api/v1.0/Sku/{id}";
                var retVal = await mediaHelperRepository.RetrieveAsync<SkuResponse>(uri, default);
                return retVal;
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(MediaHelperServiceClient), nameof(GetSku))
                .Log(LogLevel.Critical, ex, "Error while calling Sku api");
                return null;
            }
        }
    }
    public class SkuResponse
    {
        public Guid Id { get; set; }
        public int Index { get; set; }
        public string Name { get; set; }
        public string Color { get; set; }
        public string Type { get; set; }
        public string Store { get; set; }
        public string ProductId { get; set; }
    }
}
