﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.OrderedSearchResult;
using Content = BlueGuava.ContentManagement.Packages.Entities.V2.Content;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IOpenSearchService
{
    //   Task<SearchResult<Content>> PublicSearch(PublicContentSearch searchArgs, int pageSize, int pageIndex,
    //       CancellationToken cancellationToken = default);

    Task<SearchResult<Content>?> Search(ContentSearch? searchArgs,
        int pageSize,
        int pageIndex,
        string? sortBy,
        CancellationToken cancellationToken = default);

    Task<(SearchResult<Content>? Hits, IReadOnlyDictionary<string, Dictionary<string, long>> GroupCounts)> Search(
        ContentSearch? searchArgs,
        DecisionRules? decisionRules,
        OrderRules? orderingRules,
        int pageSize, int pageIndex,
        CancellationToken cancellationToken = default,
        IList<string>? groupByFields = null
    );

    Task<GroupPageResult> SearchGroupsOnly(
        ContentSearch? searchArgs,
        DecisionRules? decisionRules,
        OrderRules? orderingRules,
        int pageSize,
        int pageIndex,
        CancellationToken cancellationToken = default,
        IList<string>? groupByFields = null);

    Task<SyncResult> SyncMustacheScriptsFromFile();

    Task<SearchResult<Content>?> ExecuteMustache(string scriptId, IDictionary<string, object> parameters, int pageSize, int pageIndex, CancellationToken cancel);
}