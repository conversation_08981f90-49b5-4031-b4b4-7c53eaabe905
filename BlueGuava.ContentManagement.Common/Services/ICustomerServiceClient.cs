﻿using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.CustomerManagement.Entities;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.Interfaces;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.Models;
using BlueGuava.HttpRepository;
using CorrelationId;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common.Services;

public interface ICustomerService
{
    /// <summary> Returns the <see cref="Customer"/> by the specified <paramref name="customerId"/> </summary>
    public Task<CustomerV2Response?> Get(Guid customerId);

    public class CustomerServiceClient : ICustomerService
    {
        private readonly ILogger<CustomerServiceClient> logger;
        private readonly ICorrelationContextAccessor correlationContextAccessor;
        private readonly IObjectProvider customerProvider;

        public CustomerServiceClient(ILogger<CustomerServiceClient> logger,
            ICorrelationContextAccessor correlationContextAccessor,
            IObjectProvider customerProvider)
        {
            this.logger = logger;
            this.correlationContextAccessor = correlationContextAccessor;
            this.customerProvider = customerProvider;
        }

        public async Task<CustomerV2Response?> Get(Guid customerId)
        {
            return await customerProvider.Retrieve<CustomerV2Response>(new Identity("customer", customerId.ToString()));
        }
    }
}
