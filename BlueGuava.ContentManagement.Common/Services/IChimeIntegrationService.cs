﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.Chime;
using BlueGuava.ContentManagement.Common.Entities.IVS;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IChimeIntegrationService
{
    /// <summary> creates an aws chime meeting for the specified <paramref name="contentId"/> </summary>
    /// <returns> information about the aws chime meeting </returns>
    Task<MeetingInfo> CreateMeeting(Guid contentId);

    /// <summary> loads the aws chime meeting for the specified <paramref name="meetingId"/> </summary>
    /// <returns> information about the meeting </returns>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting does not exist </exception>
    Task<MeetingInfo> GetMeeting(string meetingId);

    /// <summary> deletes the aws chime meeting of <paramref name="meetingId"/> </summary>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting does not exist </exception>
    Task DeleteMeeting(string meetingId);


    /// <summary> Allows the user with <paramref name="userId"/> to connect to the aws chime meeting of <paramref name="meetingId"/></summary>
    /// <returns> information about the aws chime meeting attendee </returns>
    /// <remarks> if the meeting already has an attendee for this user, no new attendee is created </remarks>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting does not exist </exception>
    Task<AttendeeInfo> ConnectAttendee(string meetingId, Guid userId);

    /// <summary> Disconnects the user with <paramref name="userId"/> from the aws chime meeting of <paramref name="meetingId"/></summary>
    /// <returns> information about the aws chime meeting attendee </returns>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting does not exist </exception>
    Task<AttendeeInfo> DisconnectAttendee(string meetingId, Guid userId);


    /// <summary> starts live transcription of the aws chime meeting </summary>
    /// <param name="meetingId"></param>
    /// <param name="locale"> the language of the comminication; use <see langword="null"/> to auto-detect </param>
    Task StartTranscription(string meetingId, string? locale = null);

    /// <summary> stops the live transcription of the aws chime meeting </summary>
    Task StopTranscription(string meetingId);


    /// <summary> Starts recording of the aws chime meeting </summary>
    /// <returns> information about the aws chime media capture pipeline </returns>
    /// <remarks> always creates a new aws chime meeting media pipeline </remarks>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting does not exist </exception>
    Task<CaptureInfo> StartRecording(string meetingId);

    /// <summary> Looks up information about the <paramref name="captureId"/> capture pipeline </summary>
    /// <returns> information about the aws chime media capture pipeline; otherwise <see langword="null"/> </returns>
    Task<CaptureInfo?> TryGetRecording(string? captureId);

    /// <summary> Stops recording the aws chime meeting, and returns information about the stopped capture </summary>
    /// <returns> information about the aws chime media capture pipeline </returns>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting or the capture pipeline does not exist </exception>
    Task<CaptureInfo> StopRecording(string captureId);


    /// <summary> Starts streaming the aws chime meeting </summary>
    /// <returns> information about the chime media live connector </returns>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting or the connector pipeline does not exist </exception>
    Task<LiveSinkInfo> StartStreaming(string meetingId, LiveStreamInfo endpoint, ViewConfigurations viewConfigurations);

    /// <summary> Looks up information about the <paramref name="liveSinkId"/> live connector </summary>
    /// <returns> information about the chime media live connector; otherwise <see langword="null"/> </returns>
    Task<LiveSinkInfo?> TryGetStreaming(string liveSinkId);

    /// <summary> Stops streaming the aws chime meeting, and returns information about the stopped stream </summary>
    /// <returns> information about the chime media live connector </returns>
    /// <exception cref="ResourceNotFoundException"> when the aws chime meeting or the connector pipeline does not exist </exception>
    Task<LiveSinkInfo> StopStreaming(string liveSinkId);

    Task<IEnumerable<Amazon.ChimeSDKMeetings.Model.Attendee?>> ListAttendees(string meetingId);
}