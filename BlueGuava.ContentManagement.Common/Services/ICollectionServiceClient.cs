﻿using BlueGuava.HttpRepository;
using CorrelationId;
using Microsoft.Extensions.Logging;
using BlueGuava.ContentManagement.Common.Models;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.Extensions.Logging;
using BlueGuava.Collections.Messaging;
using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Services;

public interface ICollectionServiceClient
{
    Task<Relation?> CreateRelation(RelationRequest request, CancellationToken cancel);
    Task<List<Relation>> GetSourceRelations(string sourceId, string type, string relation, CancellationToken cancel);
    Task<List<Relation>> GetTargetRelations(string targetId, string type, string relation, CancellationToken cancel);
}

public class CollectionServiceClient : ICollectionServiceClient
{
    private readonly ILogger<CollectionServiceClient> logger;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IHttpRepository collectionHttpRepository;

    public CollectionServiceClient(ILogger<CollectionServiceClient> logger,
        ICorrelationContextAccessor correlationContextAccessor, IHttpRepositoryProvider httpRepositoryProvider)
    {
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
        collectionHttpRepository = httpRepositoryProvider.CreateHttpRepository(ServiceNames.Collections);
    }

    public async Task<Relation?> CreateRelation(RelationRequest request, CancellationToken cancel)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(CollectionServiceClient), nameof(CreateRelation))
                .Log(LogLevel.Debug, "Request: {Request}", request.ToJson());

        return await collectionHttpRepository.CreateAsync<Relation>($"api/v3.0", request, cancel);
    }

    public async Task<List<Relation>> GetSourceRelations(string sourceId, string type, string relation,
        CancellationToken cancel)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(CollectionServiceClient), nameof(GetSourceRelations))
                .Log(LogLevel.Debug, "SourceId: {SourceId} , Type: {Type} , Relation: {Relation}", sourceId, type,
                    relation);

        var pagingToken = $"api/v3.0/{sourceId}/{type.ToLower()}/{relation.ToLower()}?orderBy=CreatedDate";

        var resp = await collectionHttpRepository.RetrieveAsync<PagedResult<Relation>>(pagingToken, cancel);

        return resp?.PageContent ?? new List<Relation>();
    }

    public async Task<List<Relation>> GetTargetRelations(string targetId, string type, string relation,
        CancellationToken cancel)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(CollectionServiceClient), nameof(GetTargetRelations))
                .Log(LogLevel.Debug, "SourceId: {SourceId} , Type: {Type} , Relation: {Relation}", targetId, type,
                    relation);

        var pagingToken = $"api/v3.0/{targetId}/{type.ToLower()}/{relation.ToLower()}/Lookup?reverse=true";

        var resp = await collectionHttpRepository.RetrieveAsync<PagedResult<Relation>>(pagingToken, cancel);

        return resp?.PageContent ?? new List<Relation>();
    }
}