﻿using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using ContentV2 = BlueGuava.ContentManagement.Packages.Entities.V2.Content;
using LogAction = BlueGuava.UserLog.Enums.Action;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IContentIntegration
{
    /// <summary> Crates a published content based on <paramref name="entity"/> referencing an AWS Chime SDK Meeting </summary>
    Task<ContentV2?> CreateChimeContent(ContentV2 entity, ClaimsPrincipal user);

    /// <summary> Crates a published content based on <paramref name="entity"/> referencing an Amazon IVS Channel </summary>
    Task<ContentV2?> CreateIVSLiveContent(ContentV2 entity, ClaimsPrincipal user,
        IVSChannelLatencyMode channelLatencyMode, IVSChannelType channelType);

    /// <summary> Crates a published content based on <paramref name="entity"/> referencing an Amazon IVS Chat Room </summary>
    Task<ContentV2?> CreateIVSChatContent(ContentV2 entity, ClaimsPrincipal user);

    /// <summary> Logs an <paramref name="actionType"/> action (about <paramref name="currentContent"/> -&gt; <paramref name="updatedContent"/>) in the audit log </summary>
    Task EnqueueLogEntry(LogAction action, ContentV2 currentContent, ContentV2 updatedContent, ClaimsPrincipal user,
        string? notes = null);

    /// <summary> Creates a <paramref name="description"/> trace message for the specified <paramref name="contentId"/> and <paramref name="correlationId"/> </summary>
    Task TracelogAction(Guid contentId, Guid? correlationId, string description);

    /// <summary>
    /// Starts streaming for the specified <paramref name="content"/> and <paramref name="streamId"/> <br/>
    /// Sends a content update message to the update processor about the stopped streaming
    /// </summary>
    /// <param name="liveContentId"></param>
    /// <param name="content"></param>
    /// <param name="user"></param>
    /// <returns></returns>
    Task StopStream(ContentV2 content, Guid liveContentId, ClaimsPrincipal? user = null);
}