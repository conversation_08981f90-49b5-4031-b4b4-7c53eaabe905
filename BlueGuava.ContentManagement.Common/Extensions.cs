﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.Common;

public static class Extensions
{

    private const string TargetFormat = "yyyy-MM-dd'T'HH:mm:ss";
    private static readonly CultureInfo Invariant = CultureInfo.InvariantCulture;
    private static readonly DateTimeStyles ParseStyles = DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal;
    public static T? JsonClone<T>(this T obj)
    {
        if (obj == null) return default;
        var json = JsonConvert.SerializeObject(obj);
        return JsonConvert.DeserializeObject<T>(json);
    }

    public static List<string>? TrimItems(this List<string>? list)
    {
        if (list == null || list.Count == 0) return list;
        for (var i = 0; i < list.Count; ++i)
            if (!string.IsNullOrEmpty(list[i]))
                list[i] = list[i].Trim();

        list.RemoveAll(string.IsNullOrEmpty);
        return list;
    }

    public static List<Guid>? TrimItems(this List<Guid>? list)
    {
        if (list == null || list.Count == 0) return list;
        list.RemoveAll(id => id == Guid.Empty);
        return list;
    }

    public static List<TValue>? TrimItems<TValue>(this List<TValue>? list) where TValue : class?
    {
        if (list == null || list.Count == 0) return list;
        list.RemoveAll(v => v == null);
        return list;
    }

    public static Dictionary<TKey, TValue>? TrimItems<TKey, TValue>(this Dictionary<TKey, TValue>? dictionary)
        where TValue : class where TKey : notnull
    {
        if (dictionary == null || dictionary.Count == 0) return dictionary;
        dictionary.Where(kvp => kvp.Value == null).Select(kvp => kvp.Key)
            .ToList().ForEach(key => dictionary.Remove(key));
        return dictionary;
    }


    public static T? NullIfEmpty<T>(this T? collection)
        where T : class, System.Collections.ICollection
    {
        return collection?.Count > 0 ? collection : null;
    }

    public static IEnumerable<TSource> DistinctBy<TSource, TKey>(this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector)
    {
        HashSet<TKey> seenKeys = new();
        foreach (var element in source)
            if (seenKeys.Add(keySelector(element)))
                yield return element;
    }

    public static TimeSpan Round(this TimeSpan time, TimeSpan roundingInterval, MidpointRounding roundingType)
    {
        var ticksByInterval = time.Ticks / (decimal)roundingInterval.Ticks;
        var roundedValue = Convert.ToInt64(Math.Round(ticksByInterval, roundingType));
        return new TimeSpan(roundedValue * roundingInterval.Ticks);
    }

    public static TimeSpan Round(this TimeSpan time, TimeSpan roundingInterval)
    {
        return Round(time, roundingInterval, MidpointRounding.ToEven);
    }

    public static DateTime Round(this DateTime datetime, TimeSpan roundingInterval)
    {
        return new DateTime((datetime - DateTime.MinValue).Round(roundingInterval).Ticks);
    }

    /// <summary> 
    /// Rounds the specified <paramref name="dateTime"/> to the next <paramref name="interval"/> mark
    /// </summary>
    /// <param name="dateTime"> the date to round up </param>
    /// <param name="interval"> the interval to round up to </param>
    /// <returns> The input rounded up to the next interval. </returns>
    public static DateTime RoundUp(this DateTime dateTime, TimeSpan interval)
    {
        var modTicks = dateTime.Ticks % interval.Ticks;
        var delta = modTicks != 0 ? interval.Ticks - modTicks : 0;
        return new DateTime(dateTime.Ticks + delta, dateTime.Kind);
    }

    public static IEnumerable<List<T>?> SplitList<T>(this IEnumerable<T> entities, int nSize = 30)
    {
        using var enumerator = entities.GetEnumerator();

        List<T>? result = null;
        while (enumerator.MoveNext())
        {
            if (result == null) result = new List<T>();
            result.Add(enumerator.Current);
            if (result.Count < nSize) continue;
            yield return result;
            result = null;
        }

        if (result != null)
            yield return result;
    }

    public static Uri ConvertInputFileLocationToS3(this string inputFileLocation, out string bucket, out string path)
    {
        bucket = string.Empty;
        path = string.Empty;
        if (string.IsNullOrEmpty(inputFileLocation)) return new Uri("s3://");

        if (inputFileLocation.StartsWith("s3://"))
        {
            var uri = new Uri(inputFileLocation);
            bucket = inputFileLocation.Split(new char[1] { '/' })[2];
            var regex = new Regex(Regex.Escape("/"));
            path = regex.Replace(uri.AbsolutePath, string.Empty, 1);
            return uri;
        }

        if (!Uri.TryCreate(inputFileLocation, UriKind.Absolute, out var result))
            throw new InvalidOperationException("BadRequest: Bad URL format");

        var host = result.Host;
        if (host.IndexOf(".s3.", StringComparison.OrdinalIgnoreCase) > 0)
        {
            bucket = host.Split(new char[1] { '.' })[0];
            var absolutePath = result.AbsolutePath;
            var regex2 = new Regex(Regex.Escape("/"));
            path = regex2.Replace(result.AbsolutePath, string.Empty, 1);
            return new Uri("s3://" + bucket + absolutePath);
        }

        string[] array = result.AbsolutePath.Split(new char[1] { '/' });
        bucket = array[1];
        path = result.AbsolutePath.Replace("/" + bucket + "/", string.Empty);
        return new Uri("s3://" + bucket + "/" + path);
    }

    public static string? CleanGenre(this string? name)
    {
        if (string.IsNullOrEmpty(name)) return name;

        List<string> genre = name.Split("#").ToList();
        return genre.FirstOrDefault();
    }

    public static string? RemoveDiacritics(this string? text)
    {
        if (string.IsNullOrEmpty(text)) return null;

        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();

        foreach (var c in normalizedString.EnumerateRunes())
        {
            var unicodeCategory = Rune.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark) stringBuilder.Append(c);
        }

        return stringBuilder.ToString().Replace("_", " ").Normalize(NormalizationForm.FormC);
    }

    public static string ToTargetDateFormat(this string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return input;

        if (DateTime.TryParse(input, Invariant, ParseStyles, out var dt))
            return dt.ToString(TargetFormat, Invariant);

        if (DateTimeOffset.TryParse(input, Invariant, ParseStyles, out var dto))
            return dto.UtcDateTime.ToString(TargetFormat, Invariant);

        return input;
    }
}