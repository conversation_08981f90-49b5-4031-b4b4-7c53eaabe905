﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common
{
    public class MustacheConfig
    {
        public List<MustacheScriptDefinition> Scripts { get; set; } = new();
    }

    public class MustacheScriptDefinition
    {
        public string Id { get; set; } = "";
        public string? Description { get; set; }
        public Dictionary<string, ParamRule>? ParamRules { get; set; }
        public string? Source { get; set; }
    }

    public class ParamRule
    {
        public string? Type { get; set; }
        public bool Required { get; set; }
        public string? Regex { get; set; }
        public int? MinLength { get; set; }
        public int? MaxLength { get; set; }
        public double? Min { get; set; }
        public double? Max { get; set; }
    }

    public class SyncResult
    {
        public int ScriptCount { get; set; }
        public List<string> Scripts { get; set; } = new();
    }

    public class GroupBucket
    {
        public string? Field { get; init; }
        public string? Key { get; init; }
        public Dictionary<string, string>? Keys { get; init; }
        public long Count { get; init; }
    }

    public class GroupPageResult
    {
        public IReadOnlyList<GroupBucket> Items { get; init; } = Array.Empty<GroupBucket>();
        public int PageIndex { get; init; }
        public int PageSize { get; init; }

        public long? EstimatedTotal { get; init; }

        public IReadOnlyDictionary<string, object>? AfterKey { get; init; }
    }
}
