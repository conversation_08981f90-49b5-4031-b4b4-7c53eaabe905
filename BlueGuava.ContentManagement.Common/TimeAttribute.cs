﻿using Microsoft.AspNetCore.Mvc.Filters;
using System.Diagnostics;
using System.Threading.Tasks;
using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Common.Filters
{
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class TimeCheckAttribute : Attribute, IAsyncActionFilter
    {
        private readonly string tag;

        public TimeCheckAttribute(string tag)
        {
            this.tag = tag;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var stopwatch = Stopwatch.StartNew();
            var resultContext = await next();
            stopwatch.Stop();

            var elapsedMs = stopwatch.ElapsedMilliseconds;

            var logger = context.HttpContext.RequestServices.GetService<ILogger<TimeCheckAttribute>>();

            if (logger != null)
            {
                logger.LogInformation("[{Tag}] Execution Time for {Action}: {Elapsed} ms", tag, context.ActionDescriptor.DisplayName, elapsedMs);
            }
        }
    }

}
