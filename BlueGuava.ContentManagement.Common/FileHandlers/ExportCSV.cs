﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text;
using BlueGuava.ContentManagement.Packages.Entities;

namespace BlueGuava.ContentManagement.Common;

public class ExportCSV
{
    private const string DELIMITER = ",";

    public string Write<T>(IList<T> list, bool includeHeader = true)
    {
        var sb = new StringBuilder();
        var type = typeof(T);
        PropertyInfo[] properties = type.GetProperties().Where(prop => Attribute.IsDefined(prop, typeof(ExportData)))
            .ToArray();

        if (includeHeader) sb.AppendLine(CreateCsvHeaderLine(properties));

        foreach (var item in list) sb.AppendLine(CreateCsvLine(item, properties));

        return sb.ToString();
    }

    private string CreateCsvHeaderLine(PropertyInfo[] properties)
    {
        List<string> propertyValues = new();

        foreach (var prop in properties)
        {
            var stringformatString = string.Empty;
            var value = prop.Name;

            var attribute = prop.GetCustomAttribute(typeof(DisplayAttribute));
            if (attribute != null) value = (attribute as DisplayAttribute).Name;

            CreateCsvStringItem(propertyValues, value);
        }

        return CreateCsvLine(propertyValues);
    }

    private string CreateCsvLine<T>(T item, PropertyInfo[] properties)
    {
        List<string> propertyValues = new();

        foreach (var prop in properties)
        {
            var stringformatString = string.Empty;
            var value = prop.GetValue(item, null);

            if (prop.PropertyType == typeof(string))
                CreateCsvStringItem(propertyValues, value);
            else if (prop.PropertyType == typeof(string[]))
                CreateCsvStringArrayItem(propertyValues, value);
            else if (prop.PropertyType == typeof(List<string>))
                CreateCsvStringListItem(propertyValues, value);
            else
                CreateCsvItem(propertyValues, value);
        }

        return CreateCsvLine(propertyValues);
    }

    private string CreateCsvLine(IList<string> list)
    {
        return string.Join(DELIMITER, list);
    }

    private void CreateCsvItem(List<string> propertyValues, object value)
    {
        if (value != null)
            propertyValues.Add(value.ToString());
        else
            propertyValues.Add(string.Empty);
    }

    private void CreateCsvStringListItem(List<string> propertyValues, object value)
    {
        var formatString = "\"{0}\"";
        if (value != null)
        {
            value = CreateCsvLine((List<string>)value);
            propertyValues.Add(string.Format(formatString, ProcessStringEscapeSequence(value)));
        }
        else
        {
            propertyValues.Add(string.Empty);
        }
    }

    private void CreateCsvStringArrayItem(List<string> propertyValues, object value)
    {
        var formatString = "\"{0}\"";
        if (value != null)
        {
            value = CreateCsvLine(((string[])value).ToList());
            propertyValues.Add(string.Format(formatString, ProcessStringEscapeSequence(value)));
        }
        else
        {
            propertyValues.Add(string.Empty);
        }
    }

    private void CreateCsvStringItem(List<string> propertyValues, object value)
    {
        var formatString = "\"{0}\"";
        if (value != null)
            propertyValues.Add(string.Format(formatString, ProcessStringEscapeSequence(value)));
        else
            propertyValues.Add(string.Empty);
    }

    private string ProcessStringEscapeSequence(object value)
    {
        return value.ToString().Replace("\"", "\"\"");
    }
}