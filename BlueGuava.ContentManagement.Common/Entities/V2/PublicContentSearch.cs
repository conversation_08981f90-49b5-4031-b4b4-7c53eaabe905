﻿using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Entities.V2;

public class PublicContentSearch : IContentSearch
{
    public IEnumerable<ContentType>? Types { get; set; }
    public string? Locale { get; set; }
    public string? Keyword { get; set; }
    public string[] Cities { get; set; }
    public string[] Countries { get; set; }

    public virtual string ToString()
    {
        return base.ToString() + ", Locale: " + Locale + ", Keyword: " + Keyword;
    }
}