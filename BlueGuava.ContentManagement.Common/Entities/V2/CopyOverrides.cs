﻿using System;

namespace BlueGuava.ContentManagement.Common.Entities.V2;

public class CopyOverrides
{
    public Guid? OwnerId { get; set; }
    public string? NameSuffix { get; set; }
    public bool KeepAssets { get; set; }
    public bool KeepRelationships { get; set; }
    public string? NamePrefix { get; set; }
    public bool DefaultPrefixAndSuffix { get; set; }
    public bool Published { get; set; }
}