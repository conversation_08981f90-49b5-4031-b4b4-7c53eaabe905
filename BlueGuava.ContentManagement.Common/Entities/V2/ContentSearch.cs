﻿using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.MarkerManagement.Models.Abstraction.Enums;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BlueGuava.ContentManagement.Common.Entities.V2;

public class ContentSearch : IContentSearch
{
    public ContentSearch()
    {
    }

    public ContentSearch(
        string? query,
        IEnumerable<ContentType>? types,
        int? minDuration,
        int? maxDuration,
        string? externalId,
        string? referenceId,
        string? genre,
        string? category,
        DateTime? createdFrom,
        DateTime? createdUntil,
        DateTime? modifiedFrom,
        DateTime? modifiedUntil,
        bool? published,
        List<MarkerFilter>? markerFilters,
        string? sortExpr,
        string? ownerId = null,
        string[]? cities = null,
        string[]? countries = null,
        double? longitude = null,
        double? latitude = null,
        int radius = 5)
    {
        Query = query?.Trim();
        Types = types;
        MinDuration = minDuration;
        MaxDuration = maxDuration;
        ExternalId = externalId;
        ReferenceId = referenceId;
        CreatedFrom = createdFrom;
        CreatedUntil = createdUntil;
        ModifiedFrom = modifiedFrom;
        ModifiedUntil = modifiedUntil;
        Published = published;
        MarkerFilters = markerFilters;
        SortExpr = sortExpr;
        OwnerId = ownerId;
        Cities = cities;
        Countries = countries;
        Genre = genre;
        Category = category;
        Longitude = longitude;
        Latitude = latitude;
        Radius = radius;
    }

    public string? Query { get; set; }
    public IEnumerable<ContentType>? Types { get; set; }
    public int? MinDuration { get; set; }
    public int? MaxDuration { get; set; }
    public string? ExternalId { get; set; }
    public string? ReferenceId { get; set; }
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedUntil { get; set; }
    public DateTime? ModifiedFrom { get; set; }
    public DateTime? ModifiedUntil { get; set; }
    public bool? Published { get; set; }

    public List<MarkerFilter>? MarkerFilters { get; set; }
    public IEnumerable<string>? OrganizationFilters { get; set; }
    public string? SortExpr { get; set; }

    public string? OwnerId { get; set; }

    public string[] Cities { get; set; }
    public string[] Countries { get; set; }
    public string? Genre { get; set; }
    public string? Category { get; set; }
    public double? Longitude { get; set; }
    public double? Latitude { get; set; }
    public int Radius { get; set; }

    public static string LogFormat =>
        "Name: {Query}, Type: {Type}, MinDuration: {MinDuration}, MaxDuration: {MaxDuration}, ExternalId: {ExternalId}, ReferenceId: {ReferenceId}, CreatedFrom: {createdFrom}, CreatedUntil: {CreatedUntil}, ModifiedFrom: {modifiedFrom}, ModifiedUntil: {modifiedUntil}, Published: {Published}, Genre: {Genre}, Category: {Category}, Longitude: {Longitude}, {Latitude}: Latitude, Radius: {Radius}";

    public object[] GetValues()
    {
        return new object[]
        {
            Query ?? string.Empty, Types?.Count() > 0 ? string.Join(",", Types) : string.Empty, MinDuration!,
            MaxDuration!, ExternalId!, ReferenceId!, CreatedFrom!, CreatedUntil!, ModifiedFrom!, ModifiedUntil!,
            Published!, Genre!, Category!, Longitude!, Latitude!, Radius!
        };
    }

    public override string ToString()
    {
        var orgs = OrganizationFilters != null ? string.Join(", ", OrganizationFilters) : string.Empty;
        var cities = Cities?.Length > 0 ? string.Join(", ", Cities) : string.Empty;
        var countries = Countries?.Length > 0 ? string.Join(", ", Countries) : string.Empty;

        return base.ToString() + $" Query: {Query}, " +
               $"Types: {string.Join(", ", Types ?? ArraySegment<ContentType>.Empty)}, " +
               $"MinDuration: {MinDuration}, " +
               $"MaxDuration: {MaxDuration}, " +
               $"ExternalId: {ExternalId}, " +
               $"ReferenceId: {ReferenceId}, " +
               $"Genre: {Genre}, " +
               $"Category: {Category}, " +
               $"CreatedFrom: {CreatedFrom}, " +
               $"CreatedUntil: {CreatedUntil}, " +
               $"ModifiedFrom: {ModifiedFrom}, " +
               $"ModifiedUntil: {ModifiedUntil}, " +
               $"Published: {Published}, " +
               $"Organizations: {orgs}" +
               $"OwnerId: {OwnerId}" +
               $"ShortExpr: {SortExpr}" +
               $"Cities: {cities}" +
               $"Countries: {countries}" +
               $"Longitude: {Longitude}" +
               $"Latitude: {Latitude}" +
               $"Radius: {Radius}"
               ;
    }
}

public class MarkerFilter
{
    public MarkerType MarkerType { get; set; }
    public string? Value { get; set; }
}