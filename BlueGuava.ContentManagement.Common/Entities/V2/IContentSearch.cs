﻿using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Entities.V2;

public interface IContentSearch
{
    string[] Cities { get; set; }
    string[] Countries { get; set; }
    IEnumerable<ContentType>? Types { get; set; }

    public virtual string ToString()
    {
        return
            $"Cities: {string.Join(", ", Cities)}, Countries: {string.Join(", ", Countries)}, Types: {string.Join(", ", Types)}";
    }
}