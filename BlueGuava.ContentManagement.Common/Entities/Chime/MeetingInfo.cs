﻿using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Entities.Chime;

public class MeetingInfo : MeetingRef
{
    public string? ExternalMeetingId { get; set; }
    public MediaPlacement? MediaPlacement { get; set; }
    public string? MediaRegion { get; set; }
    public MeetingFeatures? MeetingFeatures { get; set; }
    public string? MeetingHostId { get; set; }
    public string? PrimaryMeetingId { get; set; }
    public List<string>? TenantIds { get; set; }
}

public class MediaPlacement
{
    public string? AudioFallbackUrl { get; set; }
    public string? AudioHostUrl { get; set; }
    public string? EventIngestionUrl { get; set; }
    public string? ScreenDataUrl { get; set; }
    public string? ScreenSharingUrl { get; set; }
    public string? ScreenViewingUrl { get; set; }
    public string? SignalingUrl { get; set; }
    public string? TurnControlUrl { get; set; }
}

public class MeetingFeatures
{
    public MeetingAudio? Audio { get; set; }
}

public class MeetingAudio
{
    public string? EchoReduction { get; set; }
}