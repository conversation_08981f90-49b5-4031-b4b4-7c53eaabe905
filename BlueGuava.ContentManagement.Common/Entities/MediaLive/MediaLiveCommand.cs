﻿using System;

namespace BlueGuava.ContentManagement.Common.Entities.MediaLive;

public abstract class MediaLiveCommand
{
    public Guid ContentId { get; set; }
}

public class CreateChannel : MediaLiveCommand
{
    public string? ObjectUrl { get; set; }
    public int Duration { get; set; }
    public DateTime StartTime { get; set; }
    public Guid CustomerId { get; set; }
}

public class StartStreaming : MediaLiveCommand
{
    public DateTime StartTime { get; set; }
}

public class StopStreaming : MediaLiveCommand
{
}

public class DeleteChannel : MediaLiveCommand
{
}