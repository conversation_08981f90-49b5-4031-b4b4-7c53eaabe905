﻿using System;
using System.Text;

namespace BlueGuava.ContentManagement.Common;

public static class Base64Extension
{
    public static string ToBase64(this string text)
    {
        return Convert.ToBase64String(Encoding.UTF8.GetBytes(text)).TrimEnd('=').Replace('+', '-')
            .Replace('/', '_');
    }

    public static string FromBase64(this string base64)
    {
        base64 = base64.Replace('_', '/').Replace('-', '+');
        switch (base64.Length % 4)
        {
            case 2:
                base64 += "==";
                break;
            case 3:
                base64 += "=";
                break;
        }
        return Encoding.UTF8.GetString(Convert.FromBase64String(base64));
    }
}