﻿using AutoMapper;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.CustomerManagement.Entities;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.Models;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BlueGuava.Library.Interop.v1;

namespace BlueGuava.ContentManagement.Common
{
    public class ObjectProvider : CachingObjectProvider
    {
        private readonly IMapper mapper;

        public ObjectProvider(
                ILogger<CachingObjectProvider> logger,
                IOptionsMonitor<CachingProviderSettings> settingsMonitor,
                IMemoryCache contentCache,
                IS3Repository s3Repository,
                IOptionsMonitor<AssetSettings> bucketSettings,
                IMapper mapper
                ) : base(logger, settingsMonitor, contentCache, s3Repository, bucketSettings)
        {
            this.mapper = mapper;
        }

        /// <summary>
        /// Override this method if you want to support multiple type of items
        /// </summary>
        /// <param name="rawItem"></param>
        /// <param name="result"></param>
        /// <typeparam name="TItem"></typeparam>
        /// <returns></returns>
        protected override TItem? MapItem<TItem>(string? rawItem, TItem? result) where TItem : class
        {
            if (string.IsNullOrEmpty(rawItem)) return result;

            if (typeof(TItem) == typeof(Content))
                return mapper.Map<TItem>(
                    SerializationBase.FromJson<BlueGuava.ContentManagement.Packages.Entities.V2.Content>(rawItem));

            if (typeof(TItem) == typeof(CustomerV2))
                return mapper.Map<TItem>(
                    SerializationBase.FromJson<BlueGuava.CustomerManagement.Entities.CustomerV2>(rawItem));

            /*
            if(typeof(TItem) == typeof(SKU))
                            return mapper.Map<TItem>(
                                SerializationBase.FromJson<BlueGuava.MediaHelper.Entities.SKU>(rawItem));
            */
            return SerializationBase.FromJson<TItem>(rawItem);
        }
    }
}
