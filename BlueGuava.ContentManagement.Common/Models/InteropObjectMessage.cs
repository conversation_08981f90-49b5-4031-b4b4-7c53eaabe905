using System;
using BlueGuava.Library;
using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Common.Models;

public class InteropObjectMessage : IQueueItem
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    string? IQueueItem.Receipt { get; set; }

    string? IQueueItem.GroupId
    {
        get => $"{Payload[Constants.CONTENT_ID]}";
        set => _ = value;
    }

    string? IQueueItem.DeduplicationId
    {
        get => $"{Id}";
        set => _ = value;
    }

    public string? Sender { get; set; }
    public Library.Interop.v2.Object Payload { get; }

    public InteropObjectMessage(Library.Interop.v2.Object payload)
    {
        if (string.IsNullOrEmpty(payload[Constants.CONTENT_ID]))
            throw new InvalidOperationException($"{Constants.CONTENT_ID} is null or empty");
        Payload = payload;
    }
}