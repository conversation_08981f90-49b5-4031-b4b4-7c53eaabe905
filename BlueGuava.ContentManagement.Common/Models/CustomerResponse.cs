﻿using BlueGuava.CustomerManagement.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Common.Models
{
    public class CustomerV2Response
    {
        /// <summary>
        /// The internal unique identifier of the customer
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Type of the customer, see <see cref="Entities.CustomerType"/>
        /// </summary>
        public string? CustomerType { get; set; }

        /// <summary>
        /// Amazon Region name for personal data
        /// </summary>
        public string? HomeRegion { get; set; }

        /// <summary>
        /// Date and time the customer record was created
        /// </summary>
        public DateTime RegistrationDate { get; set; }

        /// <summary>
        /// Date and time of the recorded last customer activity
        /// </summary>
        public DateTime LastSeenDate { get; set; }

        /// <summary>
        /// Date and time the customer was last modified 
        /// </summary>
        public DateTime LastModifiedDate { get; set; }

        /// <summary>
        /// Date and time when the customer record was deleted
        /// </summary>
        public DateTime? DeleteDate { get; set; }

        /// <summary>
        /// Signals whether the customer record is deleted
        /// </summary>
        public bool IsDeleted => DeleteDate.HasValue;

        /// <summary>
        /// Date and time when the end user license agreement (EULA) was accepted
        /// </summary>
        public DateTime LicenseAccepted { get; set; }

        /// <summary>
        /// Date and time when the data privacy policy was accepted
        /// </summary>
        public DateTime DataPolicyAccepted { get; set; }

        /// <summary>
        /// Free form custom properties
        /// <list type="table">
        ///     <listheader>
        ///         <term>name</term>
        ///         <description>value</description>
        ///     </listheader>
        ///     <item>
        ///         <term>Custom:SocialIds:*</term>
        ///         <description>various social login identifiers (eg: Google, OAuth2:Echelon)</description>
        ///     </item>
        ///     <item>
        ///         <term>Custom:User:*</term>
        ///         <description>customer personal data (eg: Name, Avatar)</description>
        ///     </item>
        ///     <item>
        ///         <term>Custom:ExternalIds:*</term>
        ///         <description>various external identifiers (eg: Stripe)</description>
        ///     </item>
        ///     <item>
        ///         <term>Settings:*</term>
        ///         <description>persisted application settings (eg: AutoPlay, DarkMode)</description>
        ///     </item>
        ///     <item>
        ///         <term>*</term>
        ///         <description>any other attached property</description>
        ///     </item>
        /// </list>
        /// </summary>
        public Dictionary<string, string>? Properties { get; set; }

        /// <summary>
        /// Tags attached to the customer
        /// <list type="table">
        ///     <listheader>
        ///         <term><see cref="LabelType">key</see></term>
        ///         <description>value</description>
        ///     </listheader>
        ///     <item>
        ///         <term><see cref="LabelType.Genre"/></term>
        ///         <description><c>"Crime", "Monitoring", "Dream"</c></description>
        ///     </item>
        ///     <item>
        ///         <term><see cref="LabelType.Organization"/></term>
        ///         <description><c>"Org A Id","Org B Id","Org C Id"</c></description>
        ///     </item>
        /// </list>
        /// </summary>
        public Dictionary<LabelType, List<string>>? Labels { get; set; }

        /// <summary>
        /// Parental control settings, or parental control reminder suppression settings
        /// </summary>
        public ParentalControl? ParentalControl { get; set; }

        /// <summary>
        /// Default language of the customer
        /// </summary>
        public string? DefaultLocale { get; set; }

        /// <summary>
        /// Nick name of the user in our system
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// Nick name of the user in our system
        /// </summary>
        public string? EmailAddress { get; set; }

        /// <summary>
        /// Owner of the user in our system
        /// </summary>
        public string? OwnerId { get; set; }

        /// <summary>
        /// Flattened relations from openserach
        /// </summary>
        public List<string>? Relations { get; set; }

        /// <summary>
        /// Public user name by which customer can be searched from Public API
        /// </summary>
        public string CustomerUsername { get; set; }

        /// <summary>
        /// Invitation Code
        /// </summary>
        public string InvitationCode { get; set; }
    }
}
