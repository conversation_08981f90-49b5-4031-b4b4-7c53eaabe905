namespace BlueGuava.ContentManagement.Common.Models;

public class ContentReleaseTrigger
{
    public bool RecalculateCatalogs { get; }
    public Action Action { get; }
    public BlueGuava.ContentManagement.Packages.Entities.V2.Content? Entity { get; }

    public ContentReleaseTrigger(BlueGuava.ContentManagement.Packages.Entities.V2.Content? entity,
        bool recalculateCatalogs, Action action = Action.Release)
    {
        Entity = entity;
        RecalculateCatalogs = recalculateCatalogs;
        Action = action;
    }
}

public enum Action
{
    Release,
    UnRelease
}