﻿using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Models;

public class ContentValidation
{
    public CheckDuration? CheckDuration { get; set; }
    public CheckImages? CheckImages { get; set; }
    public CheckLocalization? CheckLocalization { get; set; }
    public CheckStreams? CheckStreams { get; set; }
}

public class CheckDuration
{
    public bool Enabled { get; set; }
    public bool ForJobs { get; set; }
    public List<int>? ForTypes { get; set; }
}

public class CheckImages
{
    public bool Enabled { get; set; }
    public Dictionary<int, int>? ImageTypes { get; set; }
}

public class CheckLocalization
{
    public bool Enabled { get; set; }
}

public class CheckStreams
{
    public bool Enabled { get; set; }
    public List<int>? ForTypes { get; set; }
    public Dictionary<int, int>? StreamTypes { get; set; }
}