﻿using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace BlueGuava.ContentManagement.Common.Models;

/// <summary>
/// Generic pagination
/// </summary>
public class Pagination<T>
{
    /// <summary>
    /// Page content
    /// </summary>
    [JsonPropertyName("pageContent")]
    public List<T>? PageContent { get; set; }

    /// <summary>
    /// Paging token for pagination
    /// </summary>
    [JsonPropertyName("pagingToken")]
    public string? PagingToken { get; set; }

    /// <summary>
    /// Property to check if the pagination is finished
    /// </summary>
    [JsonPropertyName("finished")]
    public bool Finished { get; set; }
}