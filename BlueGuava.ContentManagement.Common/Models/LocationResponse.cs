﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Models.OpenStreetMap;

public class Address
{
    public string Suburb { get; set; }
    public string City { get; set; }
    public string Subdistrict { get; set; }
    public string State { get; set; }

    [JsonProperty("ISO3166-2-lvl4")] public string ISO31662lvl4 { get; set; }
    public string Country { get; set; }
    public string Country_code { get; set; }
}

public class LocationResponse
{
    public int Place_id { get; set; }

    public string Licence { get; set; }

    [JsonProperty("osm_type")] public string OsmType { get; set; }
    [JsonProperty("osm_id")] public int OsmId { get; set; }

    public string Lat { get; set; }

    public string Lon { get; set; }

    [JsonProperty("display_name")] public string DisplayName { get; set; }

    public Address Address { get; set; }

    public List<string> Boundingbox { get; set; }
}