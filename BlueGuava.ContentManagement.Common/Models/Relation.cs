﻿using System;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.Common.Models;

/// <summary>
/// Content relation
/// </summary>
public class Relation
{
    /// <summary>
    /// Type of relation
    /// </summary>
    [JsonProperty("type")]
    public string? Type { get; set; }

    /// <summary>
    /// Relation information
    /// </summary>
    [JsonProperty("relation")]
    public string? RelationInfo { get; set; }

    /// <summary>
    /// Relation source id
    /// </summary>
    [JsonProperty("sourceId")]
    public string? SourceId { get; set; }

    /// <summary>
    /// Relation target id
    /// </summary>
    [JsonProperty("targetId")]
    public string? TargetId { get; set; }

    /// <summary>
    /// Relation creation date
    /// </summary>
    [JsonProperty("createdDate")]
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Relation extra data
    /// </summary>
    [JsonProperty("extraData")]
    public string? ExtraData { get; set; }
}