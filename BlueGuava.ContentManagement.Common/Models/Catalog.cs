﻿using System;
using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Models;

public class Catalog
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public bool Enabled { get; set; } = true;
    public CatalogEntityType EntityType { get; set; }
    public List<CatalogProperty> Properties { get; set; } = new();
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
    public Guid OwnerId { get; set; } = Guid.Empty;
    public Guid LastModifiedBy { get; set; } = Guid.Empty;

    public CatalogKind Kind { get; set; }
    public int Index { get; set; }
    public CatalogType Type { get; set; }

    public CatalogObjectType ContentType { get; set; }

    public DataSource? DataSource { get; set; }

    public Guid? ParentId { get; set; }
    public string? ImageUrl { get; set; }

    public bool Protected { get; set; }

    public List<CatalogDecisionRule> DisplayRules { get; set; } = new();
    public List<CatalogAction> Actions { get; set; } = new();
    public List<CatalogDecisionRule> FilterRules { get; set; } = new();

    public List<CatalogOrderingGroup> OrderingGroups { get; set; } = new();
    public Paging? Paging { get; set; }
    public string? CacheControlInSeconds { get; set; }
    public string? AvailabilityFrom { get; set; }
    public string? AvailabilityTo { get; set; }
}

public enum CatalogEntityType
{
    None = 0,
    Catalog = 1,
    DataGroup = 2,
    Feed = 3,
    Plugin = 4
}

public enum CatalogKind
{
    Automatic = 0,
    Manual = 1
}

public enum CatalogType
{
    Folder = 0,
    Group = 1
}

public enum CatalogObjectType
{
    Content,
    Artist,
    Label,
    Partner,
    Festival
}

public enum DataSource
{
    None = 0,
    Catalog = 1,
    Sku = 2,
    Customer = 3,
    Content = 4,
    Search = 5
}

public enum ValueFormat
{
    Number = 0,
    DateTime = 1,
    Boolean = 2,
    Text = 3,
    List = 4
}

public enum ConditionName
{
    Equal = 0,
    NotEqual = 1,
    Greater = 2,
    GreaterOrEqual = 3,
    Less = 4,
    LessOrEqual = 5,
    Contains = 7,
    NotContains = 8,
    NotExists = 9,
    Exists = 10,
    Any = 14,
    None = 15,
    All = 16
}

public enum CatalogStringMatching
{
    Equal = 0,
    StartsWith = 1,
    Contains = 2,
    EndsWith = 3
}

public enum ActionType
{
    Navigate = 0,
    Play = 1,
    Authorize = 2,
    Authenticate = 3,
    Menu = 5,
    Tracking = 6,
    Download = 10,
    NavigateIntoBrowser = 11,
    Subscription = 13,
    Search = 15,
    Interaction = 16,
    GetDetails = 17,
    GetLayout = 18
}

public enum ActionGroup
{
    None = 0,
    Play = 1,
    Sorting = 2,
    Filtering = 3,
    Callback = 4
}

public enum HttpMethod
{
    Get = 0,
    Post = 1
}

public enum CatalogSortDirection
{
    Ascending = 0,
    Descending = 1,
    Randomize = 2
}

public class CatalogProperty
{
    public string Collection { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Value { get; set; }
}

public class CatalogDecisionRule
{
    public int Index { get; set; }
    public string PropertyName { get; set; } = string.Empty;
    public ValueFormat Formatting { get; set; } = ValueFormat.Text;
    public ConditionName Condition { get; set; }
    public string PropertyValue { get; set; } = string.Empty;
    public bool ValueIsReference { get; set; }

    public char? ValueCollectionSeparator { get; set; }
    public char? PropertyCollectionSeparator { get; set; }
    public CatalogStringMatching ContainsCondition { get; set; }
}

public class CatalogAction
{
    public string Name { get; set; } = string.Empty;
    public int Index { get; set; }
    public bool Enabled { get; set; } = true;
    public ActionType ActionType { get; set; }
    public List<CatalogDecisionRule> DisplayRules { get; set; } = new();
    public ActionGroup ActionGroup { get; set; }
    public HttpMethod HttpMethod { get; set; }
    public string Url { get; set; } = string.Empty;
    public List<CatalogProperty> Parameters { get; set; } = new();
}

public class CatalogOrderRule
{
    public int Index { get; set; } = 0;
    public string PropertyName { get; set; } = string.Empty;
    public CatalogSortDirection Direction { get; set; } = CatalogSortDirection.Ascending;
    public ValueFormat ValueFormat { get; set; } = ValueFormat.Text;
    public List<string>? ValueList { get; set; }
}

public class CatalogOrderingGroup
{
    public int Index { get; set; }
    public string Id { get; set; }
    public string Name { get; set; }
    public List<CatalogOrderRule> OrderRules { get; set; }
    public int LimitItem { get; set; }
    public bool Dynamic { get; set; }
}

public class Paging
{
    public int? PageSize { get; set; }
}