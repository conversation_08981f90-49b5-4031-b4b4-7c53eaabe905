﻿using System.Collections.Generic;

namespace BlueGuava.ContentManagement.Common.Models;

// model for the image url
public class Link
{
    public string? Url { get; set; }
}

// model for the DALL E api response
public class ImageProcessor
{
    public long Created { get; set; }
    public List<Link>? Data { get; set; }
    public bool IsError => Error != null;
    public Error? Error { get; set; }
}

public class GenerateImageRequest
{
    public string? Text { get; set; }
    public string? ContentId { get; set; }
}

public class Error
{
    public string? code { get; set; }
    public string? message { get; set; }
    public object? param { get; set; }
    public string? type { get; set; }
}