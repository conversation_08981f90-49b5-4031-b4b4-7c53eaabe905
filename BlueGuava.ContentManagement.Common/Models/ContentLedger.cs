﻿/*
#nullable enable
using Amazon.IonDotnet.Tree;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using BlueGuava.ContentManagement.Packages.Entities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AWS.QDBL.Extensions;
using BlueGuava.Extensions.AWS.QDBL.Models;

namespace BlueGuava.ContentManagement.Common.Models;

public class ContentLedger : LedgerBase
{
    public string? Id { get; set; }

    public string? Type { get; set; }

    public string? Color { get; set; }

    public string? Credits { get; set; }

    public string? Localizations { get; set; }

    public string? ExhibitionWindow { get; set; }

    public string? Themes { get; set; }

    public string? Assets { get; set; }

    public DateTime? PublishedDate { get; set; }

    public DateTime? ReleaseDate { get; set; }

    public bool? Duplicate { get; set; }

    public bool? Downloadable { get; set; }

    public bool? AllowMinting { get; set; }

    public bool? AllowEmailNotification { get; set; }

    public bool? AllowRemix { get; set; }

    public bool? AllowComments { get; set; }

    public bool? AllowUserRating { get; set; }
    public bool? AllowChat { get; set; }

    public bool? AllowSideshow { get; set; }

    public bool? AllowLyrics { get; set; }

    public bool? Published { get; set; }

    public string? OwnerId { get; set; }

    public string? LastModifiedBy { get; set; }

    public DateTime? LastModifiedDate { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string? AuthGroupIds { get; set; }
    public string? Properties { get; set; }

    public string? ExternalId { get; set; }

    public string? ReferenceId { get; set; }

    public string? OriginalTitle { get; set; }

    public string? OriginalFileName { get; set; }

    public int? Duration { get; set; }

    public string? OriginalLanguage { get; set; }

    public string? OriginalTranscript { get; set; }

    public string? Entities { get; set; }

    public string? WhereToWatch { get; set; }

    public int? InternalPrice { get; set; }
    public double? TokenPrice { get; set; }
    public string? TokenCurrency { get; set; }
    public string? Visibility { get; set; }

    public string? Labels { get; set; }

    public string? ArchivalPolicy { get; set; }
    public string? DeletionPolicy { get; set; }

    [BaseData]
    public PublishingRule? PublishingRule { get; set; } =
        ContentManagement.Packages.Entities.V2.Enums.PublishingRule.Upcoming;

    [BaseData] public ContentNotification? Notification { get; set; } = ContentNotification.Followed;


    public static ContentLedger? FromEntity(Content? entity)
    {
        if (entity == null) return null;

        return new ContentLedger
        {
            Id = entity.Id.ToString(),
            Type = entity.Type.ToString(),
            AllowUserRating = entity.AllowUserRating,
            AllowComments = entity.AllowComments,
            AllowMinting = entity.AllowMinting,
            AllowEmailNotification = entity.AllowEmailNotification,
            AllowRemix = entity.AllowRemix,
            AllowChat = entity.AllowChat,
            AllowSideshow = entity.AllowSideshow,
            Assets = ConvertToJson(entity.Assets), //entity.Assets ?? new List<Asset>(),
            AuthGroupIds = ConvertToJson<List<Guid>>(entity.AuthGroupIds ?? new List<Guid>()), // ?? new List<Guid>(),
            Color = entity.Color,
            CreatedDate = entity.CreatedDate,
            Credits = ConvertToJson<List<Credit>>(entity.Credits ?? new List<Credit>()), // ?? new List<Credit>(),
            Downloadable = entity.Downloadable,
            Duplicate = entity.Duplicate,
            Duration = entity.Duration,
            ExhibitionWindow =
                ConvertToJson<Dictionary<string, Availability>>(entity.ExhibitionWindow ??
                                                                new Dictionary<string, Availability>()),
            ExternalId = entity.ExternalId,
            LastModifiedBy = entity.LastModifiedBy.ToString(),
            LastModifiedDate = entity.LastModifiedDate,
            Localizations =
                ConvertToJson<Dictionary<string, Localization>>(entity.Localizations ??
                                                                new Dictionary<string,
                                                                    Localization>()), // ?? new Dictionary<string, Localization>(),
            OriginalFileName = entity.OriginalFileName,
            OriginalTitle = entity.OriginalTitle,
            OwnerId = entity.OwnerId.ToString(),
            Properties =
                ConvertToJson<Dictionary<string, string>>(entity.Properties ??
                                                          new Dictionary<string,
                                                              string>()), // ?? new Dictionary<string, string>(),
            Published = entity.Published,
            PublishedDate = entity.PublishedDate,
            ReferenceId = entity.ReferenceId,
            ReleaseDate = entity.ReleaseDate,
            Themes = ConvertToJson<Dictionary<DesignTypes, ContentDesign>>(entity.Themes ??
                                                                           new Dictionary<DesignTypes,
                                                                               ContentDesign>()),
            OriginalLanguage = entity.OriginalLanguage,
            OriginalTranscript = entity.OriginalTranscript,
            Entities = ConvertToJson<Dictionary<EntityType, List<string>>>(entity.Entities ??
                                                                           new Dictionary<EntityType, List<string>>()),
            WhereToWatch =
                ConvertToJson<List<WhereToWatch>>(entity.WhereToWatch ??
                                                  new List<WhereToWatch>()), // ?? new List<WhereToWatch>(),
            InternalPrice = entity.InternalPrice,
            TokenPrice = entity.TokenPrice,
            TokenCurrency = entity.TokenCurrency,
            Visibility = entity?.Visibility?.ToString(),
            Labels = ConvertToJson<Dictionary<LabelType, List<string>>>(entity?.Labels ??
                                                                        new Dictionary<LabelType,
                                                                            List<
                                                                                string>>()), // ?? new Dictionary<string, List<string>>(),
            PublishingRule = entity?.PublishingRule,
            Notification = entity?.Notification,
            ArchivalPolicy = entity?.ArchivalPolicy,
            DeletionPolicy = entity?.DeletionPolicy
        };
    }

    public static ContentLedger? FromIonValue(IIonValue? ionData)
    {
        if (ionData == null) return null;
        try
        {
            return new ContentLedger
            {
                Id = ionData.ExtractStr(nameof(Id)),
                Type = ionData.ExtractStr(nameof(Type)) ?? "",
                AllowUserRating = ionData.ExtractBool(nameof(AllowUserRating)) ?? false,
                AllowComments = ionData.ExtractBool(nameof(AllowComments)) ?? false,
                AllowMinting = ionData.ExtractBool(nameof(AllowMinting)) ?? false,
                AllowEmailNotification = ionData.ExtractBool(nameof(AllowEmailNotification)) ?? false,
                AllowRemix = ionData.ExtractBool(nameof(AllowRemix)) ?? false,
                AllowChat = ionData.ExtractBool(nameof(AllowChat)) ?? false,
                AllowSideshow = ionData.ExtractBool(nameof(AllowSideshow)) ?? false,
                Assets = ionData.ExtractStr(nameof(Assets)),
                AuthGroupIds = ionData.ExtractStr(nameof(AuthGroupIds)),
                Color = ionData.ExtractStr(nameof(Color)),
                CreatedDate = ionData.ExtractDateTime(nameof(CreatedDate)),
                Credits = ionData.ExtractStr(nameof(Credits)),
                Downloadable = ionData.ExtractBool(nameof(Downloadable)),
                Duplicate = ionData.ExtractBool(nameof(Duplicate)),
                Duration = ionData.ExtractInt(nameof(Duration)),
                ExhibitionWindow = ionData.ExtractStr(nameof(ExhibitionWindow)),
                ExternalId = ionData.ExtractStr(nameof(ExternalId)),
                LastModifiedBy = ionData.ExtractStr(nameof(LastModifiedBy)),
                LastModifiedDate = ionData.ExtractDateTime(nameof(LastModifiedDate)),
                Localizations = ionData.ExtractStr(nameof(Localizations)),
                OriginalFileName = ionData.ExtractStr(nameof(OriginalFileName)),
                OriginalTitle = ionData.ExtractStr(nameof(OriginalTitle)),
                OwnerId = ionData.ExtractStr(nameof(OwnerId)),
                Properties = ionData.ExtractStr(nameof(Properties)),
                Published = ionData.ExtractBool(nameof(Published)),
                PublishedDate = ionData.ExtractDateTime(nameof(PublishedDate)),
                ReferenceId = ionData.ExtractStr(nameof(ReferenceId)),
                ReleaseDate = ionData.ExtractDateTime(nameof(ReleaseDate)),
                Themes = ionData.ExtractStr(nameof(Themes)),
                OriginalLanguage = ionData.ExtractStr(nameof(OriginalLanguage)),
                OriginalTranscript = ionData.ExtractStr(nameof(OriginalTranscript)),
                Entities = ionData.ExtractStr(nameof(Entities)),
                WhereToWatch = ionData.ExtractStr(nameof(WhereToWatch)),
                InternalPrice = ionData.ExtractInt(nameof(InternalPrice)),
                TokenPrice = ionData.ExtractDouble(nameof(TokenPrice)),
                TokenCurrency = ionData.ExtractStr(nameof(TokenCurrency)),
                Visibility = ionData.ExtractStr(nameof(Visibility)) ?? "",
                Labels = ionData.ExtractStr(nameof(Labels)),
                PublishingRule = Enum.TryParse(ionData.ExtractStr(nameof(PublishingRule)),
                    out PublishingRule publishingRule)
                    ? publishingRule
                    : ContentManagement.Packages.Entities.V2.Enums.PublishingRule.Upcoming,
                Notification = Enum.TryParse(ionData.ExtractStr(nameof(Notification)),
                    out ContentNotification notification)
                    ? notification
                    : ContentNotification.Followed,
                ArchivalPolicy = ionData.ExtractStr(nameof(ArchivalPolicy)),
                DeletionPolicy = ionData.ExtractStr(nameof(DeletionPolicy))
            };
        }
        catch
        {
            return null;
        }
    }

    public static Content? ToEntity(ContentLedger? entity)
    {
        if (entity == null) return null;

        return new Content
        {
            Id = Guid.Parse(entity.Id ?? Guid.Empty.ToString()),
            Type = (ContentType)Enum.Parse(typeof(ContentType), entity.Type ?? "None"),
            AllowUserRating = entity.AllowUserRating ?? false,
            AllowComments = entity.AllowComments ?? false,
            AllowMinting = entity.AllowMinting ?? false,
            AllowEmailNotification = entity.AllowEmailNotification ?? false,
            AllowRemix = entity.AllowRemix ?? false,
            AllowChat = entity.AllowChat ?? false,
            AllowSideshow = entity.AllowSideshow ?? false,
            Assets = string.IsNullOrEmpty(entity.Assets)
                ? new List<Asset>()
                : JsonConvert.DeserializeObject<List<Asset>>(entity.Assets), //entity.Assets ?? new List<Asset>(),
            AuthGroupIds = string.IsNullOrEmpty(entity.AuthGroupIds)
                ? new List<Guid>()
                : JsonConvert.DeserializeObject<List<Guid>>(entity.AuthGroupIds), // ?? new List<Guid>(),
            Color = entity.Color,
            CreatedDate = entity.CreatedDate ?? default(DateTime),
            Credits = string.IsNullOrEmpty(entity.Credits)
                ? new List<Credit>()
                : JsonConvert.DeserializeObject<List<Credit>>(entity.Credits), // ?? new List<Credit>(),
            Downloadable = entity.Downloadable ?? false,
            Duplicate = entity.Duplicate ?? false,
            Duration = entity.Duration ?? 0,
            ExhibitionWindow = string.IsNullOrEmpty(entity.ExhibitionWindow)
                ? new Dictionary<string, Availability>()
                : JsonConvert.DeserializeObject<Dictionary<string, Availability>>(entity.ExhibitionWindow),
            ExternalId = entity.ExternalId,
            LastModifiedBy = Guid.Parse(entity.LastModifiedBy ?? Guid.Empty.ToString()),
            LastModifiedDate = entity.LastModifiedDate ?? default(DateTime),
            Localizations = string.IsNullOrEmpty(entity.Localizations)
                ? new Dictionary<string, Localization>()
                : JsonConvert
                    .DeserializeObject<Dictionary<string, Localization>>(entity
                        .Localizations), // ?? new Dictionary<string, Localization>(),
            OriginalFileName = entity.OriginalFileName,
            OriginalTitle = entity.OriginalTitle,
            OwnerId = Guid.Parse(entity.OwnerId ?? Guid.Empty.ToString()),
            Properties = string.IsNullOrEmpty(entity.Properties)
                ? new Dictionary<string, string>()
                : JsonConvert
                    .DeserializeObject<Dictionary<string, string>>(entity
                        .Properties), // ?? new Dictionary<string, string>(),
            Published = entity.Published ?? false,
            PublishedDate = entity.PublishedDate,
            ReferenceId = entity.ReferenceId,
            ReleaseDate = entity.ReleaseDate,
            Themes = string.IsNullOrEmpty(entity.Themes)
                ? new Dictionary<DesignTypes, ContentDesign>()
                : JsonConvert.DeserializeObject<Dictionary<DesignTypes, ContentDesign>>(entity.Themes),
            OriginalLanguage = entity.OriginalLanguage,
            OriginalTranscript = entity.OriginalTranscript,
            Entities = string.IsNullOrEmpty(entity.Entities)
                ? new Dictionary<EntityType, List<string>>()
                : JsonConvert.DeserializeObject<Dictionary<EntityType, List<string>>>(entity.Entities),
            WhereToWatch = string.IsNullOrEmpty(entity.WhereToWatch)
                ? new List<WhereToWatch>()
                : JsonConvert
                    .DeserializeObject<List<WhereToWatch>>(entity.WhereToWatch), // ?? new List<WhereToWatch>(),
            InternalPrice = entity.InternalPrice,
            TokenPrice = entity.TokenPrice,
            TokenCurrency = entity.TokenCurrency,
            Visibility = (Visibility)Enum.Parse(typeof(Visibility), entity.Visibility ?? ""),
            Labels = string.IsNullOrEmpty(entity.Labels)
                ? new Dictionary<LabelType, List<string>>()
                : JsonConvert
                    .DeserializeObject<Dictionary<LabelType, List<string>>>(entity
                        .Labels), // ?? new Dictionary<string, List<string>>(),
            PublishingRule = entity.PublishingRule,
            Notification = entity.Notification,
            ArchivalPolicy = entity.ArchivalPolicy,
            DeletionPolicy = entity.DeletionPolicy
        };
    }

    public override IEnumerable<string> ToLedgerStatementString()
    {
        var list = new List<string>();

        list.Add("'Id' : ?");
        list.Add("'Type' : ?");
        list.Add("'AllowUserRating' : ?");
        list.Add("'AllowComments' : ?");
        list.Add("'AllowMinting' : ?");
        list.Add("'AllowEmailNotification' : ?");
        list.Add("'AllowRemix' : ?");
        list.Add("'AllowChat' : ?");
        list.Add("'AllowSideshow' : ?");
        list.Add("'AllowLyrics' : ?");
        list.Add("'Assets' : ?");
        list.Add("'AuthGroupIds' : ?");
        list.Add("'Color' : ?");
        list.Add("'CreatedDate' : ?");
        list.Add("'Credits' : ?");
        list.Add("'Downloadable' : ?");
        list.Add("'Duplicate' : ?");
        list.Add("'Duration' : ?");
        list.Add("'ExhibitionWindow' : ?");
        list.Add("'ExternalId' : ?");
        list.Add("'LastModifiedBy' : ?");
        list.Add("'LastModifiedDate' : ?");
        list.Add("'Localizations' : ?");
        list.Add("'OriginalFileName' : ?");
        list.Add("'OriginalTitle' : ?");
        list.Add("'OwnerId' : ?");
        list.Add("'Properties' : ?");
        list.Add("'Published' : ?");
        list.Add("'PublishedDate' : ?");
        list.Add("'ReferenceId' : ?");
        list.Add("'ReleaseDate' : ?");
        list.Add("'Themes' : ?");
        list.Add("'OriginalLanguage' : ?");
        list.Add("'OriginalTranscript' : ?");
        list.Add("'Entities' : ?");
        list.Add("'WhereToWatch' : ?");
        list.Add("'InternalPrice' : ?");
        list.Add("'TokenPrice' : ?");
        list.Add("'TokenCurrency' : ?");
        list.Add("'Visibility' : ?");
        list.Add("'Labels' : ?");
        list.Add("'PublishingRule' : ?");
        list.Add("'Notification' : ?");
        list.Add("'ArchivalPolicy' : ?");
        list.Add("'DeletionPolicy' : ?");

        return list;
    }

    public override IEnumerable<object> ToLedgerParameters()
    {
        var list = new List<object?>();

        list.Add(Id);
        list.Add(Type!);
        list.Add(AllowUserRating!);
        list.Add(AllowComments!);
        list.Add(AllowMinting!);
        list.Add(AllowEmailNotification);
        list.Add(AllowRemix!);
        list.Add(AllowChat);
        list.Add(AllowSideshow);
        list.Add(AllowLyrics);
        list.Add(Assets!);
        list.Add(AuthGroupIds!);
        list.Add(Color!);
        list.Add(CreatedDate!);
        list.Add(Credits!);
        list.Add(Downloadable!);
        list.Add(Duplicate!);
        list.Add(Duration!);
        list.Add(ExhibitionWindow!);
        list.Add(ExternalId!);
        list.Add(LastModifiedBy!);
        list.Add(LastModifiedDate!);
        list.Add(Localizations!);
        list.Add(OriginalFileName!);
        list.Add(OriginalTitle!);
        list.Add(OwnerId!);
        list.Add(Properties!);
        list.Add(Published!);
        list.Add(PublishedDate!);
        list.Add(ReferenceId!);
        list.Add(ReleaseDate!);
        list.Add(Themes!);
        list.Add(OriginalLanguage!);
        list.Add(OriginalTranscript!);
        list.Add(Entities!);
        list.Add(WhereToWatch!);
        list.Add(InternalPrice!);
        list.Add(TokenPrice!);
        list.Add(TokenCurrency!);
        list.Add(Visibility!);
        list.Add(Labels!);
        list.Add(PublishingRule!);
        list.Add(Notification!);
        list.Add(ArchivalPolicy!);
        list.Add(DeletionPolicy!);

        //mandatory to add again because of the WHERE clause
        list.Add(Id);
        return list;
    }

    private static string ConvertToJson<T>(T? obj)
    {
        if (obj == null) return "";
        return JsonConvert.SerializeObject(obj);
    }
}
*/