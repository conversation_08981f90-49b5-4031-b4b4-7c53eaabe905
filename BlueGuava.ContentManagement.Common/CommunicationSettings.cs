﻿using System;

namespace BlueGuava.ContentManagement.Common;

public class CommunicationSettings
{
    /// <summary> Number of consecutive failures after the circuit breaker triggers </summary>
    public int CircuitBreakerThreshold { get; set; } = 3;

    /// <summary> Duration until a broken circuit resumes operation </summary>
    public TimeSpan CircuitBreakerDuration { get; set; } = TimeSpan.FromSeconds(10);
}