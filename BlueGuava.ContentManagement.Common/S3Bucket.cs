﻿namespace BlueGuava.ContentManagement.Common;

public class S3Bucket
{
    public string? FileExport { get; set; }

    /// <summary>
    /// Ingest bucket, private for outside guava
    /// </summary>
    public string? Ingest { get; set; }

    /// <summary>
    /// Contents bucket, public for outsiders via CDN
    /// </summary>
    public string? Contents { get; set; }

    /// <summary>
    /// IVS Recording bucket
    /// </summary>
    public string? IVSRecording { get; set; }

    /// <summary>
    /// Chime Recording bucket
    /// </summary>
    public string? ChimeMedia { get; set; }
}