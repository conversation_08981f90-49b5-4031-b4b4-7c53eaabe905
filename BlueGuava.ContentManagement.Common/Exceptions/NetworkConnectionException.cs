﻿using System;
using System.Net;
using System.Runtime.Serialization;

namespace BlueGuava.ContentManagement.Common.Exceptions;

[Serializable]
public class NetworkConnectionException : Exception
{
    public HttpStatusCode StatusCode { get; set; }

    public NetworkConnectionException()
    {
    }

    public NetworkConnectionException(string message) : base(message)
    {
    }

    public NetworkConnectionException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public NetworkConnectionException(string message, HttpStatusCode statusCode, Exception innerException) : base(
        message,
        innerException)
    {
        StatusCode = statusCode;
    }

    protected NetworkConnectionException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }
}