﻿using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Common.Monitoring;

public interface IMetricsCollector
{
    void InitializeCounters();
    void IncrementSuccessCounter(ApiMethod method, ContentType contentType);
    void IncrementErrorCounter(ApiMethod method, ContentType contentType, ErrorCategory category = ErrorCategory.Soft);
    void IncrementSqsMessageFromCounter(params string[] labels);
}