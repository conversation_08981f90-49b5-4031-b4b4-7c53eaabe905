[SERVICE]
    Parsers_File /fluent-bit/parsers/parsers.conf
    Flush 1
    Grace 30

[OUTPUT]
    Name firehose
    Match *
    region ${AWS_REGION}
    delivery_stream ${DELIVERY_STREAM}
    retry_limit 2

# [OUTPUT]
#     Name cloudwatch
#     Match *
#     region ${AWS_REGION}
#     log_key log
#     log_group_name /ecs/${SERVICE_NAME}
#     log_stream_prefix ecs
#     auto_create_group false
#     retry_limit 2

[FILTER]
    Name rewrite_tag
    Match ${SERVICE_NAME}-firelens*
    Rule $log /\"Level\":\"(Information|Warning|Error|Critical|Fatal)\"/ from.filtered_input false
    Emitter_Name re_emitted

[FILTER]
    Name parser
    Match from.filtered_input
    Key_Name log
    Parser json
    Reserve_Data True

[OUTPUT]
    Name datadog
    Match from.filtered_input
    Host http-intake.logs.datadoghq.com
    TLS on
    compress gzip
    dd_service ${SERVICE_NAME}
    dd_source csharp
    dd_tags project:${SERVICE_NAME},env:${ENV}
    provider ecs
    apikey ${DD_API_KEY}

[OUTPUT]
    Name datadog
    Match from.filtered_input
    Host http-intake.logs.datadoghq.eu
    TLS on
    compress gzip
    dd_service ${SERVICE_NAME}
    dd_source csharp
    dd_tags project:${SERVICE_NAME},env:${ENV}
    provider ecs
    apikey ${DD_EU_API_KEY}


