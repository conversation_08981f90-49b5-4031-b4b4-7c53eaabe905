{"containerDefinitions": [{"name": "#-{container_name}-#", "mountPoints": [], "image": "#{aws_account_id}#.dkr.ecr.#{aws_region}#.amazonaws.com/#-{container_name}-#:#-{BUILD_BUILDNUMBER}-#", "portMappings": [{"protocol": "tcp", "containerPort": 80, "hostPort": 80}], "environment": [{"name": "Logging__Console__DisableColors", "value": "true"}, {"name": "ASPNETCORE_HTTP_PORTS", "value": "80"}], "secrets": [{"valueFrom": "/ENT/ContentManagement/S3Bucket__ChimeMedia", "name": "S3Bucket__ChimeMedia"}, {"valueFrom": "/ENT/ContentManagement/S3Bucket__IVSRecording", "name": "S3Bucket__IVSRecording"}, {"valueFrom": "/ENT/ContentManagement/CDNURL", "name": "CDNURL"}, {"valueFrom": "/ENT/ContentManagement/S3Bucket__OBJECT_CACHE_PRIVATE", "name": "OBJECT_CACHE_PRIVATE"}, {"valueFrom": "/ENT/ContentManagement/S3Bucket__OBJECT_CACHE_PUBLIC", "name": "OBJECT_CACHE_PUBLIC"}, {"valueFrom": "/ENT/S3Bucket__Ingest", "name": "S3Bucket__Ingest"}, {"valueFrom": "/ENT/S3Bucket__Contents", "name": "S3Bucket__Contents"}], "logConfiguration": {"logDriver": "awsfire<PERSON>s"}, "essential": true, "volumesFrom": [], "ulimits": [{"name": "nofile", "softLimit": 20480, "hardLimit": 30720}]}, {"essential": true, "image": "public.ecr.aws/aws-observability/aws-for-fluent-bit:stable", "name": "log_router", "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/config/fluentbit.conf"}}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fluentbit/#-{container_name}-#", "awslogs-region": "#{aws_region}#", "awslogs-create-group": "true", "awslogs-stream-prefix": "ecs", "awslogs-multiline-pattern": "^trce: |^dbug: |^info: |^warn: |^fail: |^crit: "}}, "environment": [{"name": "SERVICE_NAME", "value": "#-{container_name}-#"}, {"name": "DELIVERY_STREAM", "value": "logs-to-s3"}, {"name": "AWS_REGION", "value": "#{aws_region}#"}], "secrets": [{"valueFrom": "/bg-jm/ParameterStore/Env", "name": "ENV"}, {"valueFrom": "/ENT/datadog_api_key", "name": "DD_API_KEY"}, {"valueFrom": "/ENT/datadog_eu_api_key", "name": "DD_EU_API_KEY"}], "volumesFrom": [{"sourceContainer": "#-{container_name}-#", "readOnly": true}]}, {"essential": true, "image": "public.ecr.aws/aws-observability/aws-otel-collector:latest", "name": "aws-otel-collector", "command": ["--config=/config/otel-collector-config.yaml"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/fluentbit/#-{container_name}-#", "awslogs-region": "#{aws_region}#", "awslogs-create-group": "true", "awslogs-stream-prefix": "ecs"}}, "portMappings": [{"hostPort": 8889, "protocol": "tcp", "containerPort": 8889}], "volumesFrom": [{"sourceContainer": "#-{container_name}-#", "readOnly": true}]}], "family": "#-{container_name}-#", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "#{container_cpu}#", "memory": "#{container_memory}#"}