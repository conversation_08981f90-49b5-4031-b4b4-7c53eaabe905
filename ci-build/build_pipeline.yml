variables:
  project: "code/BlueGuava.ContentManagement.Api/BlueGuava.ContentManagement.Api.csproj"
  maintenance_project: "code/BlueGuava.ContentManagement.Maintenance/BlueGuava.ContentManagement.Maintenance.csproj"
  nuget_config: "code/nuget.config"
  dotnet_version: "8.0.x"
  container_name: contentmanagement

resources:
  repositories:
    - repository: templates
      type: git
      name: JE_Backend/ent-pipelines
      ref: 'refs/heads/develop'
    - repository: helm
      type: git
      name: JE_Backend/eks-workloads
      ref: 'refs/heads/main'

trigger:
  branches:
    include:
      - develop
      - main
      - master
      - release/*
      - fix/*


jobs:
- job: Build_service
  pool:
    vmImage: 'ubuntu-latest'
  steps:
  - checkout: self
    fetchDepth: 0
    path: s/code
  - checkout: helm
    path: s/tool

  - template: template_versioning.yml@templates
    parameters:
      ci_folder: "code/ci-build"
      solution_folder: "$(Agent.BuildDirectory)/s/code"

  - template: template_dotnet_build_v2.yml@templates
    parameters:
      project: ${{ variables.project }}
      dotnet_version: ${{ variables.dotnet_version }}
      nuget_config: ${{ variables.nuget_config }}
      output_folder: '$(Build.SourcesDirectory)/code/app/'
      publish_folder: '$(Build.SourcesDirectory)/code/app/publish/'

  - template: template_dotnet_build_v2.yml@templates
    parameters:
      project: ${{ variables.maintenance_project }}
      dotnet_version: ${{ variables.dotnet_version }}
      nuget_config: ${{ variables.nuget_config }}
      output_folder: '$(Build.SourcesDirectory)/code/app/'
      publish_folder: '$(Build.SourcesDirectory)/code/maintenance/publish/'

  - template: template_maintenance_artifact_v2.yml@templates
    parameters:
      publish_folder: "$(Build.SourcesDirectory)/code/maintenance/publish/"

  - template: template_create_docker_image_v3.yml@templates
    parameters:
      container_name: ${{ variables.container_name }}
      tag: $(setVersion.service_version)
      ci_folder: "code/ci-build"
      context_folder: "code"

  - template: template_create_helm_package.yml@templates
    parameters:
      container_name: ${{ variables.container_name }}
      tag: $(Build.BuildNumber)
      helmChartDirPath: tool/apps/charts/${{ variables.container_name }}
