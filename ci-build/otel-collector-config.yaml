receivers:
  prometheus:
    config:
      global:
        scrape_interval: 30s
        scrape_timeout: 10s
      scrape_configs:
      - job_name: "otel-collector"
        static_configs:
        - targets: [ 0.0.0.0:80 ]
  awsecscontainermetrics:
    collection_interval: 30s

exporters:
  prometheus:
    endpoint: "0.0.0.0:8889"

processors:
  batch:

service:
  pipelines:
    metrics:
      receivers: [prometheus,awsecscontainermetrics]
      processors: [batch]
      exporters: [prometheus]