# Gitflow
mode: ContinuousDelivery
tag-prefix: '[vV]'
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(fix|patch)'
no-bump-message: '\+semver:\s?(none|skip)'
commit-message-incrementing: Enabled
assembly-versioning-scheme: MajorMinorPatch
assembly-file-versioning-scheme: MajorMinorPatch
assembly-informational-format: '{MajorMinorPatch}{PreReleaseTagWithDash}-{ShortSha}'
branches:
  main:
    regex: ^master$|^main$
    mode: ContinuousDelivery
    label: ''
    increment: Patch
    source-branches:
    - develop
    - release
    tracks-release-branches: false
    is-release-branch: false
  develop:
    regex: ^dev(elop)?(ment)?$
    mode: ContinuousDeployment
    label: alpha
    increment: Patch
    tracks-release-branches: true
  release:
    regex: ^release?[/-]
    mode: ContinuousDelivery
    label: beta
    increment: None
    source-branches:
    - develop
    - main
    - release
    is-release-branch: true
  feature:
    regex: ^features?[/-]
    mode: ContinuousDelivery
    label: useBranchName
    increment: Inherit
    source-branches:
    - develop
    - main
    - release
    - feature
    - hotfix
  pull-request:
    mode: ContinuousDelivery
    label: '' # PullRequest
    increment: Inherit
    regex: ^(pull|pull\-requests|pr)[/-]
    source-branches:
    - develop
    - main
    - release
    - feature
    - hotfix
    tracks-release-branches: false
    is-release-branch: false
  hotfix:
    regex: ^hotfix(es)?[/-]
    mode: ContinuousDelivery
    label: beta
    increment: Patch
    source-branches:
    - develop
    - main
    tracks-release-branches: false
    is-release-branch: false
ignore:
  sha: []
commit-date-format: yyyy-MM-dd
merge-message-formats: {}
update-build-number: true