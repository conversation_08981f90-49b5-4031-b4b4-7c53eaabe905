﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.EnumUtils;
using BlueGuava.Extensions.Logging;
using BlueGuava.JobManagement.Common.Entities.Enums;
using CorrelationId;
using Microsoft.Extensions.Logging;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Service.V2;

public class ContentArchival : IContentArchival
{
    private readonly ILogger<ContentService> logger;
    private readonly IS3Repository s3Repository;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public ContentArchival(ILogger<ContentService> logger, IS3Repository s3Repository,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.s3Repository = s3Repository;
        this.correlationContextAccessor = correlationContextAccessor;
    }


    public async Task<bool> ArchiveAllAsset(Content entity, ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(ArchiveAllAsset))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", entity?.Id);

        if (entity == null) return false;

        var tasks = new List<Task<bool>>();
        if (entity.Assets == null) return true;
        foreach (var asset in entity.Assets)
        {
            tasks.Add(AddArchiveTag(entity.Id, asset, "lifecycle", entity.ArchivalPolicy, user));
            tasks.Add(AddArchiveTag(entity.Id, asset, "deletion", entity.DeletionPolicy, user));
        }

        var results = await Task.WhenAll(tasks);
        if (results.All(x => x)) return true;

        // a tagging task failed => remove tags
        foreach (var asset in entity.Assets)
        {
            tasks.Add(RemoveArchiveTag(entity.Id, asset, "lifecycle", entity.ArchivalPolicy, user));
            tasks.Add(RemoveArchiveTag(entity.Id, asset, "deletion", entity.DeletionPolicy, user));
        }

        return false;
    }

    public async Task<bool> AddArchiveTag(Guid entityId, Asset? asset, string lifeCycleKey, string? lifeCycleTag,
        ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(AddArchiveTag))
                .Log(LogLevel.Debug,
                    "ContentId: {ContentId} AssetId: {AssetId} LifeCycleKey: {LifeCycleKey} LifeCycleTag: {LifeCycleTag}",
                    entityId, asset?.Id, lifeCycleKey, lifeCycleTag);

        // don't try to add invalid tag to S3 object
        var invalidOp = string.IsNullOrEmpty(lifeCycleKey)
                        || string.IsNullOrEmpty(lifeCycleTag)
                        || lifeCycleTag == LifecycleJobType.None.GetEnumValue()
                        || lifeCycleTag == LifecycleJobType.Delete_No.GetEnumValue();

        if (invalidOp) return true;
        if (asset == null) return false;

        var tags = new Dictionary<string, string>();
        tags.Add(lifeCycleKey, lifeCycleTag ?? string.Empty);

        ContentService.ConvertUrlToS3(asset.ObjectUrl, out var bucketName, out var fileKey);
        return await s3Repository.PutTagAsync(bucketName, fileKey, tags) == HttpStatusCode.OK;
    }

    public async Task<bool> RemoveArchiveTag(Guid entityId, Asset? asset, string lifeCycleKey, string? lifeCycleTag,
        ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(RemoveArchiveTag))
                .Log(LogLevel.Debug,
                    "ContentId: {ContentId} AssetId: {AssetId} LifeCycleKey: {LifeCycleKey} LifeCycleTag: {LifeCycleTag}",
                    entityId, asset?.Id, lifeCycleKey, lifeCycleTag);

        if (asset == null) return false;

        var tags = new Dictionary<string, string>();
        tags.Add(lifeCycleKey, lifeCycleTag ?? string.Empty);

        ContentService.ConvertUrlToS3(asset.ObjectUrl, out var bucketName, out var fileKey);
        return await s3Repository.RemoveTagAsync(bucketName, fileKey, tags) == HttpStatusCode.OK;
    }
}