﻿using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.EventStream.Common.Model.Classification.Content;
using BlueGuava.JwtToken;
using BlueGuava.MarkerManagement.Models.Abstraction.Enums;
using BlueGuava.MessageQueuing;
using BlueGuava.Webhook.Common.Entities.Enums;
using BlueGuava.Webhook.Messaging.Entities;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;
using System.Linq;
using System.Text.RegularExpressions;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ItemsProcessing;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Packages.Entities.Validation;
using BlueGuava.Reporting.Messages.Entities;
using Microsoft.AspNetCore.Http;
using MaxMind.GeoIP2;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.MarkerManagement.Models.Abstraction;
using Action = BlueGuava.ContentManagement.Common.Models.Action;
using BlueGuava.ContentManagement.Delivery.Models;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.AspNetCore.IpAddress;
using BlueGuava.Extensions.AWS.EventStream.Producer;
//using BlueGuava.Extensions.AWS.QDBL;
//using BlueGuava.Extensions.AWS.QDBL.Models;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Logging;
using BlueGuava.MarkerManagement.Models;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using BlueGuava.ContentManagement.Delivery.Conversion;
using BlueGuava.ContentManagement.Service.EventBridge.Scheduler;
using Amazon.S3;
using EventType = BlueGuava.EventStream.Common.Model.Classification.Content.EventType;
using Amazon.S3.Model;
using S3Bucket = BlueGuava.ContentManagement.Common.S3Bucket;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Common.Entities;
using System.IO;
using MethodTimer;
using BlueGuava.ContentManagement.Common.Filters;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.Models;
using M3U8Parser.ExtXType;

namespace BlueGuava.ContentManagement.Service.V2
{
    public class ContentService : IContentService
    {
        private readonly string[] localizations = { "hu-HU", "en-US", "ro-RO", "de-DE" };

        //private readonly Guid brandId;
        private readonly ILogger<ContentService> logger;
        private readonly IContentRepository repository;
        private readonly IContentMaintenance contentMaintenance;
        private readonly IS3Repository s3Repository;
        private readonly IDataProducer<DataContract> dataProducer;
        private readonly ISearchAdapter contentSearch;
        private readonly IMessageQueue<WebHookEvent> webhookQueue;
        private readonly IMessageQueue<MarkerMessage> markerMessaging;
        private readonly IMessageQueue<RelationshipUpdate> relationshipUpdates;
        private readonly ICorrelationContextAccessor correlationContextAccessor;
        //private readonly ILedgerRepository<ContentLedger> ledgerRepository;
        private readonly IFeatureManager featureManager;
        private readonly IItemProcessor<ContentReleaseTrigger> contentDeliveryQueue;
        private readonly IMessageQueue<ContentCreationMetricMessage> contentCreationMetricsMessages;
        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IGeoIP2Provider geoIpProvider;
        private readonly IMessageQueue<JobRequest> jobManagerQueue;
        private readonly ValidationOptions validationOptions;
        private readonly Globalization globalization;
        private readonly IItemProcessor<ContentTag> contentTagProcessor;
        private readonly IEventBridgeSchedulerService eventBridgeSchedulerService;
        private readonly ICollectionServiceClient collectionServiceClient;
        private readonly IConfiguration configuration;
        private readonly IConverter<Content> converter;
        private readonly IOptionsMonitor<S3Bucket> bucketSettings;
        private readonly IAmazonS3 s3Client;
        private readonly ContentReleaseOption contentReleaseOption;
        private readonly IJobManager jobManager;
        private readonly IMessageQueue<JobUpdate> jobUpdateMessagingService;


        public ContentService(
            IConfiguration configuration,
            ILogger<ContentService> logger,
            //IOptions<BrandConfig> options,
            IContentRepository repository,
            IContentMaintenance contentMaintenance,
            IS3Repository s3Repository,
            IDataProducer<DataContract> dataProducer,
            ISearchAdapter contentSearch,
            IMessageQueue<WebHookEvent> webhookQueue,
            IMessageQueue<MarkerMessage> markerMessaging,
            IMessageQueue<RelationshipUpdate> relationshipUpdates,
            ICorrelationContextAccessor correlationContextAccessor,
            //ILedgerRepository<ContentLedger> ledgerRepository,
            IFeatureManager featureManager,
            IItemProcessor<ContentReleaseTrigger> contentDeliveryQueue,
            IMessageQueue<ContentCreationMetricMessage> contentCreationMetricsMessages,
            IHttpContextAccessor httpContextAccessor,
            IGeoIP2Provider geoIpProvider,
            IMessageQueue<JobRequest> jobManagerQueue,
            IOptionsMonitor<ValidationOptions> validation,
            IOptionsMonitor<Globalization> globalization,
            IItemProcessor<ContentTag> contentTagProcessor,
            IEventBridgeSchedulerService eventBridgeSchedulerService,
            ICollectionServiceClient collectionServiceClient,
            IConverter<Content> converter,
            IOptionsMonitor<S3Bucket> bucketSettings,
            IAmazonS3 s3Client,
            IOptionsMonitor<ContentReleaseOption> contentReleaseOption,
            IJobManager jobManager,
            IMessageQueue<JobUpdate> jobUpdateMessagingService)
        {
            this.logger = logger;
            this.repository = repository;
            this.contentMaintenance = contentMaintenance;
            this.s3Repository = s3Repository;
            this.dataProducer = dataProducer;
            this.contentSearch = contentSearch;
            this.webhookQueue = webhookQueue;
            this.markerMessaging = markerMessaging;
            this.relationshipUpdates = relationshipUpdates;
            this.correlationContextAccessor = correlationContextAccessor;
            //brandId = options.Value.BrandId;
            //this.ledgerRepository = ledgerRepository;
            this.featureManager = featureManager;
            this.contentDeliveryQueue = contentDeliveryQueue;
            this.contentCreationMetricsMessages = contentCreationMetricsMessages;
            this.httpContextAccessor = httpContextAccessor;
            this.geoIpProvider = geoIpProvider;
            this.jobManagerQueue = jobManagerQueue;
            this.globalization = globalization.CurrentValue;
            this.validationOptions = validation.CurrentValue;
            this.contentTagProcessor = contentTagProcessor;
            this.eventBridgeSchedulerService = eventBridgeSchedulerService;
            this.collectionServiceClient = collectionServiceClient;
            this.configuration = configuration;
            this.converter = converter;
            this.bucketSettings = bucketSettings;
            this.s3Client = s3Client;
            this.contentReleaseOption = contentReleaseOption.CurrentValue;
            this.jobManager = jobManager;
            this.jobUpdateMessagingService = jobUpdateMessagingService;
        }

        //[Time("PERFORMANCE")]
        //[TimeCheck("Retrieve-ContentService")]
        public async Task<Content?> Retrieve(Guid contentId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Retrieve))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            return await repository.Retrieve(contentId);
        }

        public async Task<Content?> Retrieve(string externalId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Retrieve))
                    .Log(LogLevel.Debug, "ExternalId: {ExternalId}", externalId);

            return await repository.Retrieve(externalId);
        }


        public async Task<Content?> Create(Content content, ClaimsPrincipal user, bool isLocationSet = false, List<string> organizations = null)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Create))
                    .Log(LogLevel.Information, "Content: {@Content}", content);

            var conflict = await repository.Retrieve(content.Id);
            if (conflict != null)
            {
                logger.LogWarning("Content with id {ContentId} already exists", content.Id);
                return null;
            }

            if (await repository.DoesExist(content.ExternalId))
            {
                logger.LogWarning("Content with id {ExternalId} already exists", content.ExternalId);
                return null;
            }

            var userId = user.GetCustomerId();
            if (userId != Guid.Empty) content.OwnerId = userId;
            ExtractGenre(content);
            await contentMaintenance.UpdateContentFields(content);

            var isPublished = content.Published;

            //logger.LogInformation("Content Creation: {@Content}",content);

            if (isPublished) content.Publish(globalization?.Languages!, validationOptions);
            if ((content.AutoPublishDate != null && content.AutoPublishDate != DateTime.MinValue) || (content.AutoUnPublishDate != null && content.AutoUnPublishDate != DateTime.MinValue))
                await ScheduleAutoPublish(content, true);

            if (!isLocationSet) ResolveIpAddress(content);

            //if (content.Type == ContentType.RemixV2) await RemixV2PlaylistAndLocalization(content, organizations);

            //            if (content.Type == ContentType.Collection && isPublished) content.Published = true;
            //            if (content.Type == ContentType.Playlist && isPublished) content.Published = true;

            if (content.Type == ContentType.Image)
            {
                var original = content.Assets.FirstOrDefault(x => !x.IsDeleted && x.SubType == SubType.Original);

                var landscape = original.ShallowCopy();
                landscape.Id = Guid.NewGuid();
                landscape.Locale = "--";
                landscape.SubType = SubType.Landscape;
                content.Assets.Add(landscape);

                var portrait = original.ShallowCopy();
                portrait.Id = Guid.NewGuid();
                portrait.Locale = "--";
                portrait.SubType = SubType.Portrait;
                content.Assets.Add(portrait);

                var square = original.ShallowCopy();
                square.Id = Guid.NewGuid();
                square.Locale = "--";
                square.SubType = SubType.Square;
                content.Assets.Add(square);
            }
            await repository.Create(content);
            await contentSearch.Save(content);
            await contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(content, content.Published)));


            if (content.Published)
            {
                _ = ContentGenreUpdate(content);
            }

            if (content.ReleaseDate.HasValue)
            {
                _= Notify(content.ReleaseDate.Value.AddDays(-1),
                             content,
                             string.IsNullOrEmpty(contentReleaseOption.Subject) ? content.OriginalTitle ?? "" : contentReleaseOption.Subject.Replace("{title}", content.OriginalTitle).Replace("{releaseDate}", String.Format("{0:F}", content.ReleaseDate)),
                             string.IsNullOrEmpty(contentReleaseOption.Message) ? $"content release will be on {String.Format("{0:F}", content.ReleaseDate)}" : contentReleaseOption.Message.Replace("{title}", content.OriginalTitle).Replace("{releaseDate}", String.Format("{0:F}", content.ReleaseDate)));
            }


            _ = SendReport(EventName.ContentCreate);
            _ = SendWebhook(content, WebhookEventType.CONTENT_CREATION);
            _ = SendContentCreationMetricMessage(content, ContentAction.Create);

            /*
             Managed by the Workflow
            var originalAsset = content.Assets?.FirstOrDefault(s => s.SubType == SubType.Original && !s.IsDeleted);

            if (content.Type != ContentType.LiveStream && originalAsset != null)
            {
                await jobManagerQueue.Enqueue(new MediaProcessorJobRequest(user.GetUserId(), user.GetUserEmail())
                {
                    ReferenceObjectId = content.Id.ToString(),
                    MediaProcessingType = JobManagement.Common.Entities.Enums.MediaProcessingType.MEDIA_INFO,
                    InputFileLocation = originalAsset.ObjectUrl ?? throw new ArgumentException("Original asset not found"),
                }, 10);
            }*/

            return content;
        }

        private async Task Notify(DateTime scheduleTime, Content content, string subject, string message)
        {
            await jobManagerQueue.Enqueue(new SchedulingJobRequest
            {
                Id = content.Id.ToString(),
                Name = "SchedulingJob_" + content.Id.ToString(),
                Scheduled = scheduleTime,
                OwnerId = content.OwnerId,
                EmailTemplateId = MessageCenter.Common.EventName.ContentReminder.ToString(),
                QueryUrl = $"{content.Id.ToString()}/Content/Access/Notify?Status=Approved&EmailTemplateId=ContentReminder",
                Subject = content.OriginalTitle,
                Message = content.Id.ToString(),
                ServiceType = ServiceType.CollectionService,
                Repeat = false,
                RepeatAfterDays = 0,
                DeleteJob = content.Published ? false : true,
            });
        }

        private async Task ScheduleAutoPublish(Content content, bool onlyCreate = false)
        {
            var autoPublishId = $"ContentPublish_{content.Id}";
            var autoUnPublishId = $"ContentUnPublish_{content.Id}";
            if (onlyCreate)
            {
                if (content.AutoPublishDate != null && content.AutoPublishDate != DateTime.MinValue)
                    await eventBridgeSchedulerService.ScheduleOrUpdateNewRule(autoPublishId, content.AutoPublishDate, JsonConvert.SerializeObject(new { Id = content.Id.ToString(), publish = true }));
                if (content.AutoUnPublishDate != null && content.AutoUnPublishDate != DateTime.MinValue)
                    await eventBridgeSchedulerService.ScheduleOrUpdateNewRule(autoUnPublishId, content.AutoUnPublishDate, JsonConvert.SerializeObject(new { Id = content.Id.ToString(), publish = false }));
            }
            else
            {
                await eventBridgeSchedulerService.ScheduleOrUpdateNewRule(autoPublishId, content.AutoPublishDate, JsonConvert.SerializeObject(new { Id = content.Id.ToString(), publish = true }));
                await eventBridgeSchedulerService.ScheduleOrUpdateNewRule(autoUnPublishId, content.AutoUnPublishDate, JsonConvert.SerializeObject(new { Id = content.Id.ToString(), publish = false }));
            }
        }

        public async Task<Content?> Copy(Guid contentId, CopyOverrides? overrides, ClaimsPrincipal user)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Create))
                        .Log(LogLevel.Information, "ContentId: {ContentId}, Overrides: {@Overrides}", contentId,
                            overrides);

            var content = await repository.Retrieve(contentId);
            if (content == null)
            {
                logger.LogWarning("Content with id {ContentId} is not exists", contentId);
                return null;
            }

            var originalOwnerId = content.OwnerId;
            content.Id = Guid.NewGuid();
            while (await repository.Retrieve(content.Id) != null)
                content.Id = Guid.NewGuid();

            if (overrides?.Published != null)
            {
                content.Published = overrides?.Published ?? false;
            }

            if (!string.IsNullOrEmpty(overrides?.NameSuffix))
            {
                content.OriginalTitle += " " + overrides?.NameSuffix.Trim();
            }

            if (!string.IsNullOrEmpty(overrides?.NamePrefix))
            {
                content.OriginalTitle = overrides?.NamePrefix + " " + content.OriginalTitle;
            }

            if (!overrides?.KeepAssets == true) content.Assets = new List<Asset>();
            content.OwnerId = overrides?.OwnerId ?? user.GetCustomerId();
            content.LastModifiedBy = content.OwnerId;
            content.CreatedDate = DateTime.UtcNow;
            content.LastModifiedDate = content.CreatedDate;
            content.Type = ContentType.Remix; //TO-DO

            await contentMaintenance.UpdateContentFields(content);

            if (content.Published)
            {
                content.Publish(globalization.Languages, validationOptions);
            }
            /*if (content.Type == ContentType.RemixV2)
            {
                var organizations = await collectionServiceClient.GetSourceRelations(contentId.ToString(), "Content", nameof(ContentRelation.Organization), default);
                await RemixV2PlaylistAndLocalization(content, organizations?.Select(s => s?.TargetId)?.ToList());
            }*/

            content.OriginalOwnerId = originalOwnerId;
            content.Properties ??= new Dictionary<string, string>();
            if (content.Properties.ContainsKey("Content:OriginalOwnerId"))
            {
                content.Properties["Content:OriginalOwnerId"] = originalOwnerId.ToString();
            }
            else
            {
                content.Properties.Add("Content:OriginalOwnerId", originalOwnerId.ToString());
            }
            await repository.Create(content);
            await contentSearch.Save(content);

            if (overrides?.KeepRelationships == true)
            {
                _ = relationshipUpdates.Enqueue(new MultiRelationshipUpdate()
                {
                    SourceId = contentId.ToString(),
                    ClonedSourceId = content.Id.ToString(),
                    UpdateStrategy = UpdateStrategy.CloneAll,
                    Relation = ContentRelation.Reference
                });
            }
            _ = SendReport(EventName.ContentCreate);
            _ = SendWebhook(content, WebhookEventType.CONTENT_CREATION);

            _ = contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(content, content.Published)));

            if (content.Published) _= ContentGenreUpdate(content);


            return content;
        }

        public async Task<Content?> Update(Content content, ClaimsPrincipal user, bool contentAutomation = false)
        {
            bool releaseDateUpdated = false;
            var entity = await repository.Retrieve(content.Id);
            if (entity == null) return null;

            if (user == null)
                throw new UnauthorizedAccessException();

            if (!IsNullAutoPublishDates(content, entity))
                await ScheduleAutoPublish(content, false);

            var currentEntity = entity.ShallowCopy();
            var isContentTypeToBeChanged = entity.Type != content.Type;

            if (content.ReleaseDate.HasValue)
            {
                var contentReleaseUnspecified = DateTime.SpecifyKind(content.ReleaseDate.Value, DateTimeKind.Unspecified);
                var entityReleaseUnspecified = DateTime.SpecifyKind(entity.ReleaseDate ?? DateTime.MinValue, DateTimeKind.Unspecified);
                releaseDateUpdated = entityReleaseUnspecified != contentReleaseUnspecified;
            }

            var previouslyPublished = entity.Published;

            var userId = user.GetCustomerId();
            entity.UpdateWith(content, userId != Guid.Empty ? userId : content.OwnerId, false);
            var changedFields = await contentMaintenance.UpdateContentFields(entity);

            if (content.Published) content.Publish(globalization.Languages, validationOptions);

            await repository.Update(entity);
            await contentSearch.Save(entity);
            await contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(content, IsTriggerRecalculate(content, isContentTypeToBeChanged, changedFields, previouslyPublished))));

            if (previouslyPublished != entity.Published)
            {
                var eventName = entity.Published ? EventName.ContentPublish : EventName.ContentUnPublish;
                _ = SendReport(eventName);
            }

            if (isContentTypeToBeChanged)
            {
                _ = SendContentCreationMetricMessage(content, ContentAction.ChangeType);
            }

            _ = SendReport(EventName.ContentEdit);
            _ = SendWebhook(content, WebhookEventType.CONTENT_UPDATE);
            _= ContentGenreUpdate(content, currentEntity);

            if (releaseDateUpdated)
            {
                _= Notify(content.ReleaseDate.Value.AddDays(-1),
                             content,
                             string.IsNullOrEmpty(contentReleaseOption.Subject) ? content.OriginalTitle ?? "" : contentReleaseOption.Subject.Replace("{title}", content.OriginalTitle).Replace("{releaseDate}", String.Format("{0:F}", content.ReleaseDate)),
                             string.IsNullOrEmpty(contentReleaseOption.Message) ? $"content release will be on {String.Format("{0:F}", content.ReleaseDate)}" : contentReleaseOption.Message.Replace("{title}", content.OriginalTitle).Replace("{releaseDate}", String.Format("{0:F}", content.ReleaseDate)));
            }

            return entity;
        }

        private bool IsNullAutoPublishDates(Content content, Content entity)
            => (entity.AutoPublishDate == null || entity.AutoPublishDate <= DateTime.MinValue) &&
                (content.AutoPublishDate == null || content.AutoPublishDate <= DateTime.MinValue) &&
                (entity.AutoUnPublishDate == null || entity.AutoUnPublishDate <= DateTime.MinValue) &&
                (content.AutoUnPublishDate == null || content.AutoUnPublishDate <= DateTime.MinValue);

        public async Task<bool> Remove(Guid contentId, ClaimsPrincipal user)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Remove))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            var entity = await repository.Retrieve(contentId);
            if (entity == null) return false;

            await repository.Delete(contentId);
            await contentSearch.Delete(contentId);

            _ = SendReport(EventName.ContentDelete);
            _ = SendWebhook(entity, WebhookEventType.CONTENT_DELETION);
            _ = contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(entity, entity.Published, Action.UnRelease)));

            if (entity.Published) _= ContentGenreUpdate(entity);

            _ = relationshipUpdates.Enqueue(new DeleteRelations(contentId.ToString()));
            _ = SendContentCreationMetricMessage(entity, ContentAction.Delete);

            return true; // successfully deleted
        }

        public async Task<bool> SoftRemove(Guid contentId, ClaimsPrincipal user)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(SoftRemove))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            var entity = await repository.Retrieve(contentId);
            if (entity == null) return false;

            entity.IsDeleted = true;
            await repository.Update(entity);
            await contentSearch.Delete(contentId);

            _ = SendReport(EventName.ContentDelete);
            _ = SendWebhook(entity, WebhookEventType.CONTENT_DELETION);

            _ = contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(entity, entity.Published, Action.UnRelease)));

            if (entity.Published) _= ContentGenreUpdate(entity);

            _ = SendContentCreationMetricMessage(entity, ContentAction.Delete);

            return true; // successfully deleted
        }


        public IAsyncEnumerable<Guid> ScanThrough(ContentSearch searchArgs)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(ScanThrough))
                    .Log(LogLevel.Debug, ContentSearch.LogFormat, searchArgs.GetValues());

            return repository.ScanThrough(searchArgs);
        }

        public IAsyncEnumerable<Guid> ScanThroughWithType(int type)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(ScanThroughWithType))
                    .Log(LogLevel.Debug, $"Type: {type}");

            return repository.ScanThroughWithType(type);
        }

        public async Task<bool> Exist(Content content)
        {

            return await repository.DoesExist(content);
        }

        public IAsyncEnumerable<Content?> RetrieveAll()
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(RetrieveAll))
                    .Log(LogLevel.Debug);

            return repository.RetrieveAll();
        }

        public async Task<string?> GeneratePublicAssetUrl(Guid contentId, Guid assetId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Retrieve))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}, AssetId: {AssetId}", contentId, assetId);

            var asset = await repository.RetrieveAsset(contentId, assetId);

            if (asset != null && !string.IsNullOrEmpty(asset?.ObjectUrl) &&
                ConvertUrlToS3(asset.ObjectUrl, out string bucketName, out string fileKey))
            {
                string utf8Filename = Uri.EscapeDataString(asset.FileName);
                var overrides = new ResponseHeaderOverrides
                {
                    ContentDisposition = $"attachment; filename=\"{utf8Filename}\""
                };
                return s3Repository.GeneratePreSignedUrl(
                    24,
                    bucketName,
                    fileKey, // 👇 Set response headers
                    overrides);
            }

            return asset?.ObjectUrl;
        }

        public async Task<Asset?> RetrieveAsset(Guid contentId, Guid assetId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(RetrieveAsset))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}, AssetId: {AssetId}", contentId, assetId);

            return await repository.RetrieveAsset(contentId, assetId);
        }


        public async Task<Content?> Save(Content? content, Guid userId, bool maintenance, bool fromJob = false, bool fromWriteBack = false)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(Save))
                    .Log(LogLevel.Debug, "Content: {Content}, UserId: {UserId}", content.ToJson(), userId);

            if (content == null) return null;
            var entity = await repository.Retrieve(content.Id);
            bool releaseDateUpdated = false;
            var previouslyPublished = entity?.Published ?? false;
            var isContentTypeToBeChanged = entity?.Type != content.Type;

            if (content.ReleaseDate.HasValue)
            {
                var contentReleaseUnspecified = DateTime.SpecifyKind(content.ReleaseDate.Value, DateTimeKind.Unspecified);
                var entityReleaseUnspecified = DateTime.SpecifyKind(entity.ReleaseDate ?? DateTime.MinValue, DateTimeKind.Unspecified);
                releaseDateUpdated = entityReleaseUnspecified != contentReleaseUnspecified;
            }

            var isUpdate = entity != null;
            Content currentEntity;

            if (entity == null)
            {
                entity = content;
                currentEntity = entity.ShallowCopy();
                EnsureAuditInfo(entity, userId, maintenance);
            }
            else
            {
                currentEntity = entity.ShallowCopy();
                entity.UpdateWith(content, userId, maintenance);
            }

            var changedFields = await contentMaintenance.UpdateContentFields(entity);

            if (content.Published && !fromJob)
                content.Publish(globalization.Languages ?? new List<string>(), validationOptions);

            await repository.Update(entity);

            _ = contentSearch.Save(entity);

            if (maintenance) return entity;

            if (previouslyPublished != entity.Published)
            {
                var eventName = entity.Published ? EventName.ContentPublish : EventName.ContentUnPublish;
                _ = SendReport(eventName);
            }

            _ = SendReport(isUpdate ? EventName.ContentEdit : EventName.ContentCreate);
            _ = SendWebhook(entity,
                isUpdate ? WebhookEventType.CONTENT_UPDATE : WebhookEventType.CONTENT_CREATION);

            if (!fromWriteBack)
            {
                _ = contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(content,
                    IsTriggerRecalculate(content, isContentTypeToBeChanged, changedFields, previouslyPublished))));
            }

            _ = ContentGenreUpdate(entity, currentEntity);

            if (releaseDateUpdated)
            {
                await Notify(content.ReleaseDate.Value.AddDays(-1),
                             content,
                             string.IsNullOrEmpty(contentReleaseOption.Subject) ? content.OriginalTitle ?? "" : contentReleaseOption.Subject.Replace("{title}", content.OriginalTitle).Replace("{releaseDate}", String.Format("{0:F}", content.ReleaseDate)),
                             string.IsNullOrEmpty(contentReleaseOption.Message) ? $"content release will be on {String.Format("{0:F}", content.ReleaseDate)}" : contentReleaseOption.Message.Replace("{title}", content.OriginalTitle).Replace("{releaseDate}", String.Format("{0:F}", content.ReleaseDate)));
            }

            return entity;
        }

        public async Task UpdateForceIndex(Content content)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(UpdateForceIndex))
                    .Log(LogLevel.Information, "ContentId: {ContentId}", content.Id);

            //handle content error separately to support search update if it is possible
            try
            {
                // perform automatic metadata correction
                var changedFields = await contentMaintenance.UpdateContentFields(content);
                if (changedFields.Any())
                    await repository.Update(content);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(UpdateForceIndex))
                    .Log(LogLevel.Error, ex, "Content: {Content}", content.ToJson());
            }

            // refresh complete search document
            await contentSearch.Save(content);
            await RequestMarkerRecalculate(content.Id);
            await RequestRelationsRefresh(content.Id);

            if (await featureManager.IsEnabledAsync("Send_ContentCreationMessage_On_Content_Reindex"))
            {
                await SendContentCreationMetricMessage(content, ContentAction.Create);
            }

            await contentDeliveryQueue.Process(new WorkItemContext<ContentReleaseTrigger>(new ContentReleaseTrigger(content, false)));
        }

        /// <summary>
        /// Receives relations from collection service and updates the search index
        /// </summary>
        /// <param name="content"></param>
        public async Task UpdateContentRelations(Content content)
        {
            await contentSearch.Save(content);
        }

        /// <summary>
        /// Triggers an <see cref="InvalidationMark"/> based on ContentId <br/>
        /// this will trigger a migration of all chapter markers for the content into a new marker format
        /// </summary>
        /// <param name="contentId"></param>
        /// <param name="language"></param>
        public async Task RequestChapterMigration(Guid contentId, string? language)
        {
            if (contentId == Guid.Empty)
            {
                logger.LogWarning("ContentId is empty, skipping chapter migration");
                return;
            }

            var languageCode = string.IsNullOrEmpty(language)
                ? (globalization?.Languages?.Any() ?? false ? globalization.Languages[0] : "en-US")
                : language;

            await markerMessaging.Enqueue(new MarkerMessage()
            {
                Type = MarkerType.Chapter,
                ContentId = contentId.ToString(),
                Markers = new List<Marker>()
                {
                    new InvalidationMark(MarkerType.Chapter)
                    {
                        ContentId = contentId.ToString(),
                        JobId = "-",
                        Migrate = true,
                        Lang = languageCode,
                    }
                }
            });
        }

        /// <summary>
        /// Parses an S3 native or HTTP URI (<paramref name="s3PrivateUrl"/>)
        /// to <paramref name="bucket"/> and <paramref name="fileKey"/> parts
        /// </summary>
        public static bool ConvertUrlToS3(string? s3PrivateUrl, out string bucket, out string fileKey)
        {
            bucket = string.Empty;
            fileKey = string.Empty;

            if (string.IsNullOrEmpty(s3PrivateUrl)) return false;

            if (s3PrivateUrl.StartsWith("s3://"))
            {
                if (!Uri.TryCreate(s3PrivateUrl, UriKind.Absolute, out var uri))
                    return false;

                bucket = uri.Host;
                fileKey = uri.AbsolutePath.TrimStart('/');
                return true;
            }

            if (!s3PrivateUrl.Contains("amazonaws.com")) return false;

            if (!Uri.TryCreate(s3PrivateUrl, UriKind.Absolute, out var contentUrl))
                throw new InvalidOperationException("BadRequest: Bad URL format");

            bucket = contentUrl.Host.Split('.')[0];
            fileKey = contentUrl.AbsolutePath.TrimStart('/');
            return true;
        }


        public async Task UpdateAssetsCDNUrl(Content? content, ClaimsPrincipal user, string oldUrl, string newUrl)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(UpdateAssetsCDNUrl))
                    .Log(LogLevel.Debug, "Content: {@Content}", content);

            bool isChanged = false;
            foreach (var item in content?.Assets)
            {
                if (!string.IsNullOrEmpty(item.PublicUrl))
                {
                    item.PublicUrl = item.PublicUrl.Replace(oldUrl, newUrl);
                    isChanged = true;
                }

                if (!string.IsNullOrEmpty(item.TokenizedCdnUrl))
                {
                    item.TokenizedCdnUrl = item.TokenizedCdnUrl.Replace(oldUrl, newUrl);
                    isChanged = true;
                }
            }
            if (isChanged)
                await Update(content, user);
        }

        public async Task S3CleanupAsync()
        {
            var bucketNames = new[]
            {
                bucketSettings.CurrentValue.FileExport,
                bucketSettings.CurrentValue.ChimeMedia,
                bucketSettings.CurrentValue.IVSRecording
            };

            try
            {
                await Task.WhenAll(bucketNames.Select(DeleteAllObjectsAsync));
                logger.LogInformation("S3 cleanup completed successfully for buckets: {BucketNames}", string.Join(", ", bucketNames));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "S3 cleanup failed.");
            }
        }

        public async Task S3CopyObjectCleanupAsync()
        {
            try
            {
                await DeleteS3ObjectsAsync(bucketSettings.CurrentValue.Ingest, "CopyObjectProcess");
                await DeleteS3OrphanObjectsAsync(bucketSettings.CurrentValue.Ingest);
                logger.LogInformation("S3 cleanup completed successfully for bucket: {BucketName}, Path: CopyObjectProcess", bucketSettings.CurrentValue.Ingest);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "S3 cleanup failed.");
            }
        }

        public async Task DeleteS3ObjectsAsync(string bucketName, string folderPrefix)
        {
            if (string.IsNullOrWhiteSpace(bucketName) || string.IsNullOrWhiteSpace(folderPrefix))
            {
                logger.LogWarning("Bucket name or folder prefix is null or empty. Skipping cleanup.");
                return;
            }

            try
            {
                logger.LogInformation("Starting deletion for bucket: {BucketName} folder: {FolderPrefix}", bucketName, folderPrefix);
                var listRequest = new ListObjectsV2Request { BucketName = bucketName, Prefix = folderPrefix };

                do
                {
                    var listResponse = await s3Client.ListObjectsV2Async(listRequest);
                    if (!listResponse.S3Objects.Any())
                    {
                        logger.LogInformation("No objects found in bucket: {BucketName} with prefix: {FolderPrefix}", bucketName, folderPrefix);
                        break;
                    }

                    var deleteRequest = new DeleteObjectsRequest
                    {
                        BucketName = bucketName,
                        Objects = listResponse.S3Objects.Select(obj => new KeyVersion { Key = obj.Key }).ToList()
                    };

                    var deleteResponse = await s3Client.DeleteObjectsAsync(deleteRequest);
                    logger.LogInformation("Deleted {ObjectCount} objects from bucket: {BucketName} with prefix: {FolderPrefix}", deleteResponse.DeletedObjects.Count, bucketName, folderPrefix);

                    listRequest.ContinuationToken = listResponse.NextContinuationToken;

                } while (!string.IsNullOrEmpty(listRequest.ContinuationToken));

                logger.LogInformation("Bucket {BucketName} folder {FolderPrefix} cleanup completed.", bucketName, folderPrefix);
            }
            catch (AmazonS3Exception ex)
            {
                logger.LogError(ex, "AWS S3 error while deleting objects from bucket: {BucketName} folder: {FolderPrefix}. Status Code: {StatusCode}", bucketName, folderPrefix, ex.StatusCode);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while deleting objects from bucket: {BucketName} folder: {FolderPrefix}", bucketName, folderPrefix);
            }
        }

        public async Task DeleteS3OrphanObjectsAsync(string bucketName)
        {
            if (string.IsNullOrEmpty(bucketName))
            {
                logger.LogWarning("Bucket name is null or empty. Skipping cleanup.");
                return;
            }

            try
            {
                logger.LogInformation("Starting deletion for bucket: {BucketName}", bucketName);
                var listRequest = new ListObjectsV2Request { BucketName = bucketName };
                var count = 0;
                var maxConcurrency = 5;
                var runningTasks = new List<Task>();

                do
                {
                    var listResponse = await s3Client.ListObjectsV2Async(listRequest);
                    if (!listResponse.S3Objects.Any())
                    {
                        logger.LogInformation("No objects found in bucket: {BucketName}", bucketName);
                        break;
                    }

                    var groups = listResponse.S3Objects
                        .Where(obj => obj.Key.Contains("/"))
                        .GroupBy(obj => obj.Key.Split('/')[0]);

                    foreach (var group in groups)
                    {
                        var task = ProcessGroupAsync(group, bucketName, count);
                        runningTasks.Add(task);

                        if (runningTasks.Count >= maxConcurrency)
                        {
                            var completedTask = await Task.WhenAny(runningTasks);
                            runningTasks.Remove(completedTask);
                        }
                    }

                    listRequest.ContinuationToken = listResponse.NextContinuationToken;
                } while (!string.IsNullOrEmpty(listRequest.ContinuationToken));

                await Task.WhenAll(runningTasks);

                logger.LogInformation("Bucket {BucketName} cleanup completed. Deleted {Count} objects.", bucketName, count);
            }
            catch (AmazonS3Exception ex)
            {
                logger.LogError(ex, "AWS S3 error while deleting objects from bucket: {BucketName}. Status Code: {StatusCode}", bucketName, ex.StatusCode);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while deleting objects from bucket: {BucketName}", bucketName);
            }
        }

        private async Task<int> ProcessGroupAsync(IGrouping<string, S3Object> group, string bucketName, int count)
        {
            if (!Guid.TryParse(group.Key, out Guid folderGuid))
            {
                return count;
            }

            if ((await RetrieveFromCache(folderGuid)) == null)
            {
                var deleteRequest = new DeleteObjectsRequest
                {
                    BucketName = bucketName,
                    Objects = group.Select(obj => new KeyVersion { Key = obj.Key }).ToList()
                };
                var deleteResponse = await s3Client.DeleteObjectsAsync(deleteRequest);
                count += group.Count();
                logger.LogInformation("Objects from bucket: {BucketName} for folder: {FolderGuid} NOT found in the system", bucketName, folderGuid);

            }
            /*else
            {
                logger.LogInformation("Objects from bucket: {BucketName} for folder: {FolderGuid} found in the system", bucketName, folderGuid);
            }*/

            return count;
        }


        private async Task DeleteAllObjectsAsync(string bucketName)
        {
            if (string.IsNullOrWhiteSpace(bucketName))
            {
                logger.LogWarning("Bucket name is null or empty. Skipping cleanup.");
                return;
            }

            try
            {
                logger.LogInformation("Starting deletion for bucket: {BucketName}", bucketName);
                var listRequest = new ListObjectsV2Request { BucketName = bucketName };

                do
                {
                    var listResponse = await s3Client.ListObjectsV2Async(listRequest);
                    if (!listResponse.S3Objects.Any())
                    {
                        logger.LogInformation("No objects found in bucket: {BucketName}", bucketName);
                        break;
                    }

                    var deleteRequest = new DeleteObjectsRequest
                    {
                        BucketName = bucketName,
                        Objects = listResponse.S3Objects.Select(obj => new KeyVersion { Key = obj.Key }).ToList()
                    };

                    var deleteResponse = await s3Client.DeleteObjectsAsync(deleteRequest);
                    logger.LogInformation("Deleted {ObjectCount} objects from bucket: {BucketName}", deleteResponse.DeletedObjects.Count, bucketName);

                    listRequest.ContinuationToken = listResponse.NextContinuationToken;

                } while (!string.IsNullOrEmpty(listRequest.ContinuationToken));

                logger.LogInformation("Bucket {BucketName} cleanup completed.", bucketName);
            }
            catch (AmazonS3Exception ex)
            {
                logger.LogError(ex, "AWS S3 error while deleting objects from bucket: {BucketName}. Status Code: {StatusCode}", bucketName, ex.StatusCode);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occured while deleting objects from bucket: {BucketName}", bucketName);
            }
        }

        /// <summary>
        /// Triggers an <see cref="InvalidationMark"/> based on ContentId
        /// </summary>
        private async Task RequestMarkerRecalculate(Guid contentId)
        {
            await markerMessaging.Enqueue(new MarkerMessage()
            {
                Type = MarkerType.Intro,
                ContentId = contentId.ToString(),
                // for SearchReindex, MarkerType isn't required
                Markers = new List<Marker>()
                {
                    new InvalidationMark(MarkerType.Intro)
                    {
                        ContentId = contentId.ToString(),
                        JobId = "-",
                        SearchReindex = true,
                    }
                }
            });

            foreach (var item in Enum.GetValues<MarkerType>())
            {
                await markerMessaging.Enqueue(new MarkerMessage()
                {
                    Type = item,
                    ContentId = contentId.ToString(),
                    Markers = new List<Marker>()
                    {
                        new InvalidationMark(item)
                        {
                            ContentId = contentId.ToString(),
                            Delivery = true,
                        }
                    }
                });
            }
        }

        /// <summary>
        /// Triggers a <see cref="RelationshipRefresh"/> message for the <paramref name="contentId"/> <br/>
        /// with all <see cref="ContentRelation"/> enum values <br/>
        /// this will trigger an S3 cache re-sync as well for every enum value
        /// </summary>
        private async Task RequestRelationsRefresh(Guid contentId)
        {
            foreach (var value in Enum.GetValues(typeof(ContentRelation)).Cast<ContentRelation>())
            {
                await relationshipUpdates.Enqueue(new RelationshipRefresh
                {
                    SourceId = contentId.ToString(),
                    Relation = value,
                });
            }
        }
        /*
        public async Task<List<EntityHistory<Content>>> ContentHistory(Guid contentId, CancellationToken cancel = default)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(ContentHistory))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            if (!await featureManager.IsEnabledAsync("AllowContentQDBL", cancel))
            {
                logger.LogDebug("Content QDBL is turned off by FeatureManager");
                return null;
            }

            var history = await ledgerRepository.History(new EntityFilter()
            {
                UtilityRef = contentId.ToString()
            }, ContentLedger.FromIonValue, cancel);
            return ConvertToContentHistory(history);
        } */


        private async Task UpdateAssetFileSizes(Content entity)
        {
            try
            {
                if (entity.Assets == null) return;

                var tasks = new List<Task<(string Id, long Size)>>();
                foreach (var asset in entity.Assets.Where(x => x.FileSize == 0 && !string.IsNullOrEmpty(x.ObjectUrl)))
                {
                    // if the object url points to S3, then we try to get the file size from S3
                    if (ConvertUrlToS3(asset.ObjectUrl, out string bucketName, out string fileKey))
                    {
                        if (await s3Repository.ExistsAsync(bucketName, fileKey))
                            tasks.Add(s3Repository.GetFileSize(bucketName, fileKey, asset.Id?.ToString()));
                    }
                }

                foreach (var (id, size) in await Task.WhenAll(tasks))
                    entity.Assets.Single(x => x.Id?.ToString() == id).FileSize = size;
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(UpdateAssetFileSizes))
                    .Log(LogLevel.Error, ex, "ContentId: {ContentId}", entity.Id);
            }
        }

        /// <summary>
        /// Sends a content operation data to reporting engine
        /// </summary>
        private async Task SendReport(string eventName)
        {
            try
            {
                await dataProducer.PutRecordAsync(new DataContract
                {
                    EventType = EventType.ContentOperation,
                    EventName = eventName,
                    Count = 1,
                });
            }
            catch (Exception ex)
            {
                // beause we've ignored the result of this method, if any error appears we need some log
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(SendReport))
                    .Log(LogLevel.Error, ex, "Report Exception");
            }
        }

        /// <summary>
        /// Triggers a content operation webhook event
        /// </summary>
        private async Task SendWebhook(Content content, WebhookEventType eventType)
        {
            await webhookQueue.Enqueue(new WebHookEvent
            {
                EventCreated = DateTime.UtcNow,
                EventType = eventType,
                ObjectReferenceId = content.Id.ToString(),
                ObjectReferenceExternalId = content.ExternalId,
            });
        }


        /// <summary>
        /// Fills content audit fields if they are empty
        /// </summary>
        private static void EnsureAuditInfo(Content entity, Guid userId, bool maintenance)
        {
            if (entity.OwnerId == Guid.Empty && !maintenance)
                entity.OwnerId = userId;
            if (entity.LastModifiedBy == Guid.Empty && !maintenance)
                entity.LastModifiedBy = userId;
            if (entity.CreatedDate == default)
                entity.CreatedDate = DateTime.UtcNow;
            if (entity.LastModifiedDate == default)
                entity.LastModifiedDate = DateTime.UtcNow;
        }

        /*
                private static List<EntityHistory<Content>> ConvertToContentHistory(IEnumerable<EntityHistory<ContentLedger>> contentLedger)
                {
                    return contentLedger == null ? new List<EntityHistory<Content>>() : contentLedger.Select(item => new EntityHistory<Content>() { Version = item.Version, Transaction = ContentLedger.ToEntity(item.Transaction) }).ToList();
                }
        */
        private async Task SendContentCreationMetricMessage(Content content, ContentAction action)
        {
            var payload = new List<Property>
            {
                new() { Name = "ContentId", Value = $"{content.Id}" },
                new() { Name = "OwnerId", Value = $"{content.OwnerId}" },
                new() { Name = "ContentType", Value = $"{content.Type}" },
                new() { Name = "Duration", Value = $"{content.Duration}" },
                new() { Name = "Action", Value = $"{action}" },
                new() { Name = "Type", Value = "ContentCreationContract" },
                new() { Name = "Timestamp", Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") }
            };

            if (content.Labels != null && content.Labels.TryGetValue(LabelType.Location, out var locationList))
            {
                const string countryPattern = @"\b[A-Z]{2}\b";
                const string cityPattern = @"\b[A-Z][a-zA-Z-]+(?:\s[A-Z][a-zA-Z-]+)*\b";

                var country = locationList.Find(x => Regex.IsMatch(x, countryPattern));
                var city = locationList.Find(x => Regex.IsMatch(x, cityPattern));

                payload.Add(new Property { Name = "Country", Value = country });
                payload.Add(new Property { Name = "City", Value = city });
            }

            var contentCreationMetricsMessage = new ContentCreationMetricMessage
            {
                Properties = payload,
                RunDate = DateTime.UtcNow,
                TimeStamp = DateTime.UtcNow
            };

            await contentCreationMetricsMessages.Enqueue(contentCreationMetricsMessage);
        }

        private void ResolveIpAddress(Content content)
        {
            try
            {
                var httpContext = httpContextAccessor.HttpContext;
                var ipAddress = httpContext?.Request?.GetClientIpAddress() ?? string.Empty;

                if (string.IsNullOrEmpty(ipAddress)) return;
                var response = geoIpProvider.City(ipAddress);

                content.Labels ??= new Dictionary<LabelType, List<string>>();

                if (!content.Labels.ContainsKey(LabelType.Location)) content.Labels.Add(LabelType.Location, new List<string>());

                content.Labels[LabelType.Location].Add(response.Country.IsoCode ?? "ZZ");
                if (!string.IsNullOrEmpty(response.City.Name))
                {
                    content.Labels[LabelType.Location].Add(response.City.Name ?? "UNKNOWN");
                }
            }
            catch
            {
                // resolve ip address will not block execution
            }
        }

        private static void ExtractGenre(Content content)
        {
            content.Localizations?.ToList().ForEach(s =>
            {
                if (!string.IsNullOrEmpty(s.Value?.Name)) s.Value.Name = s.Value.Name.CleanGenre();
            });
            var genre = content?.OriginalTitle?.Split("#")?.ToList();
            //content.OriginalTitle = genre?.FirstOrDefault();
            genre?.RemoveAt(0);
            content.Labels ??= new Dictionary<LabelType, List<string>>();
            if (!content.Labels.ContainsKey(LabelType.Genre))
            {
                content.Labels[LabelType.Genre] = new List<string>();
            }

            if (genre != null)
                content.Labels[LabelType.Genre].AddRange(genre);
        }

        private static bool IsTriggerRecalculate(
            Content content,
            bool isContentTypeToBeChanged,
            IEnumerable<string> changedFields,
            bool previouslyPublished
            )
        {
            return
                content.Published &&
                (
                    isContentTypeToBeChanged
                    || changedFields.Contains(nameof(Content.Labels)) // key fields changed
                )
                || (previouslyPublished != content.Published)

                ; // content was published or unpublished but now is not
        }

        private async Task ContentHasGenreUpdate(Content currentEntity, Content update)
        {
            if (update.Published && currentEntity.Published)
            {
                // Optimize by pre-computing genre sets to avoid repeated LINQ operations
                var currentGenres = new HashSet<string>(
                    currentEntity.Labels?
                        .Where(x => x.Key == LabelType.Genre)
                        .SelectMany(x => x.Value)
                        .Where(tag => !string.IsNullOrEmpty(tag)) ?? Enumerable.Empty<string>()
                );

                var updateGenres = new HashSet<string>(
                    update.Labels?
                        .Where(x => x.Key == LabelType.Genre)
                        .SelectMany(x => x.Value)
                        .Where(tag => !string.IsNullOrEmpty(tag)) ?? Enumerable.Empty<string>()
                );

                // Process new genres (in update but not in current)
                var newGenres = updateGenres.Except(currentGenres);
                var newGenreTasks = newGenres.Select(tag =>
                    contentTagProcessor.Process(new WorkItemContext<ContentTag>(new ContentTag
                    {
                        PropertyName = "Content:Labels:Genre",
                        PropertyValue = tag
                    }))
                );

                // Process removed genres (in current but not in update)
                var removedGenres = currentGenres.Except(updateGenres);
                var removedGenreTasks = removedGenres.Select(tag =>
                    contentTagProcessor.Process(new WorkItemContext<ContentTag>(new ContentTag
                    {
                        PropertyName = "Content:Labels:Genre",
                        PropertyValue = tag
                    }))
                );

                // Execute all tasks concurrently
                await Task.WhenAll(newGenreTasks.Concat(removedGenreTasks));
            }
            else if (currentEntity.Published != update.Published)
            {
                await ContentGenreUpdate(update);
            }
        }

        public async Task ContentGenreUpdate(Content entity, Content update = null)
        {
            var labels = new List<string>();
            if (update != null && update.Labels!.ContainsKey(LabelType.Genre))
            {
                if (!entity.Labels!.ContainsKey(LabelType.Genre)) entity?.Labels?.Add(LabelType.Genre, new List<string>());
                var removedItems = update?.Labels?[LabelType.Genre].Except(entity?.Labels?[LabelType.Genre]!).ToList();
                var addedItems = entity?.Labels?[LabelType.Genre].Except(update?.Labels?[LabelType.Genre]!).ToList();
                if (!addedItems!.Any() && !removedItems!.Any()) return;

                labels.AddRange(addedItems!);
                labels.AddRange(removedItems!);
            }
            else
            {
                if (!entity.Labels.ContainsKey(LabelType.Genre)) return;
                labels = entity.Labels[LabelType.Genre];
            }


            var tasks = labels
            .Select(x => new ContentTag
            {
                PropertyName = "Content:Labels:Genre",
                PropertyValue = x
            })
            .Select(y => contentTagProcessor.Process(new WorkItemContext<ContentTag>(y)));
            if (tasks != null) await Task.WhenAll(tasks);
        }

        public async Task FixContentDuration(Content entity, Guid userId)
        {
            if (entity.Assets == null || !entity.Assets.Any(s => s.SubType == SubType.HLS && !s.IsDeleted))
                return;
            var duration = entity.Assets.Find(s => s.SubType == SubType.HLS && !s.IsDeleted).Duration;
            entity.Duration = duration;

            await Save(entity, userId, false, true);
        }

        public async Task SKUPackageSetup(Content entity, Guid userId, Guid? authGroupId)
        {
            if (authGroupId == null) return;
            entity.AuthGroupIds ??= new List<Guid>();
            entity.AuthGroupIds.Add(authGroupId.Value);

            await Save(entity, userId, false, true);
        }
        //[Time("PERFORMANCE")]
        //[TimeCheck("PropertyBag-ContentService")]
        public async Task<BlueGuava.Library.Interop.v2.Object> PropertyBag(Guid contentId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(PropertyBag))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            var content = await repository.Retrieve(contentId);
            if (content == null) return null;
            return await converter.ConvertObject(content);
        }

        public async Task<BlueGuava.Library.Interop.v2.Object> RetrieveFromCacheAndConvert(Guid contentId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(RetrieveFromCacheAndConvert))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            return await s3Repository.RetrieveAsync<Library.Interop.v2.Object>(bucketSettings.CurrentValue.Contents ?? "", String.Format("api/v6/content/{0}/entity.json", contentId.ToString()));
        }

        public async Task<Content> RetrieveFromCache(Guid contentId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentService), nameof(RetrieveFromCache))
                    .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

            var content = await s3Repository.RetrieveAsyncAsString(bucketSettings.CurrentValue.Ingest ?? "", String.Format("api/v6/content/{0}/entity.json", contentId.ToString()));
            if (content == null) return null;
            return JsonConvert.DeserializeObject<Content>(content);
        }

        public async Task<int> CopyObject(string sourceBucket, string sourceKey, Guid ownerId, string email, string userData)
        {
            var count = 0;
            if (!string.IsNullOrEmpty(sourceKey)) sourceKey = sourceKey.Replace("\\", "/");
            var isFile = await IsFile(sourceBucket, sourceKey);

            if (isFile)
            {
                await jobManagerQueue.Enqueue(new ChangeContentJobRequest(ownerId, email)
                {
                    SourceBucket = sourceBucket,
                    SourceKey = sourceKey,
                    UserData = userData
                }, 10);
                count = 1;
                logger.LogInformation("CopyObject Job created for Single File {SourceBucker} {SourceKey}", sourceBucket, sourceKey);
            }
            else
            {
                //var results = new List<string>();
                var listRequest = new ListObjectsV2Request
                {
                    BucketName = sourceBucket,
                    Prefix = sourceKey
                };

                logger.LogInformation("CopyObject List Request creation for {@ListRequest}", listRequest);

                try
                {
                    var listResponse = await s3Client.ListObjectsV2Async(listRequest);

                    logger.LogInformation("CopyObject List Response {@ListResponse}", listResponse);

                    foreach (var s3Object in listResponse.S3Objects)
                    {
                        if (s3Object.Key.EndsWith('/') && s3Object.Size == 0)
                        {
                            logger.LogInformation("CopyObject Folder processed. {S3ObjectKey} size is 0", s3Object.Key);
                            continue;
                        }

                        logger.LogInformation("CopyObject ForEach Job creation {S3ObjectKey}", s3Object.Key);

                        try
                        {
                            await jobManagerQueue.Enqueue(new ChangeContentJobRequest(ownerId, email)
                            {
                                SourceBucket = sourceBucket,
                                SourceKey = s3Object.Key,
                                UserData = userData
                            }, 10);
                            count++;
                        }
                        catch (Exception ex)
                        {
                            logger.LogError("CopyObject Error CopyObject {S3ObjectKey} - {StackTrace}", s3Object.Key, ex.Message + " - " + ex.StackTrace);
                        }
                    }
                    logger.LogInformation($"CopyObject Folder processed. {count} jobs created");
                }
                catch (Exception ex)
                {
                    logger.LogError("CopyObject Error - {StackTrace}", ex.Message + " - " + ex.StackTrace);
                }
            }
            return count;
        }

        public async Task CleanDeletedFiles(Content content, Guid userId)
        {
            if (content == null)
            {
                logger.LogInformation("Content is null");
                return;
            }

            var listRequest = content.Assets?.Where(s => s.IsDeleted && !string.IsNullOrWhiteSpace(s.ObjectUrl)).ToList();

            if (listRequest?.Any() == false)
            {
                logger.LogInformation("No deleted assets found for Content: {ContentId}", content.Id);
                return;
            }

            var objects = new List<(string bucket, string path)>();
            foreach (var item in listRequest)
            {
                try
                {
                    item.ObjectUrl.ConvertInputFileLocationToS3(out string bucket, out string path);
                    if (string.IsNullOrWhiteSpace(bucket) || string.IsNullOrWhiteSpace(path))
                    {
                        logger.LogInformation("Empty bucket or key for AssetId: {AssetId}, URL: {Url}", item.Id, item.ObjectUrl);
                        continue;
                    }
                    objects.Add((bucket, path));
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error parsing S3 URL for content: {ContentId}, asset: {AssetId}", content.Id, item.Id);
                }
            }

            foreach (var group in objects.GroupBy(p => p.bucket))
            {
                var bucket = group.Key;
                var paths = group.Select(p => p.path).Distinct().ToList();

                var deleteRequest = new DeleteObjectsRequest
                {
                    BucketName = bucket,
                    Objects = paths.Select(p => new KeyVersion { Key = p }).ToList(),
                    Quiet = false
                };

                try
                {
                    var deleteResponse = await s3Client.DeleteObjectsAsync(deleteRequest);
                    logger.LogInformation("Deleted {DeletedCount} objects from bucket: {BucketName}", deleteResponse.DeletedObjects.Count, bucket);
                    if (deleteResponse.DeleteErrors?.Any() == true)
                    {
                        foreach (var err in deleteResponse.DeleteErrors)
                            logger.LogInformation("Failed to delete {Key} from bucket: {BucketName}. Code={Code}, Message={Message}", err.Key, bucket, err.Code, err.Message);
                    }
                }
                catch (AmazonS3Exception ex)
                {
                    logger.LogError(ex, "AWS S3 error while deleting objects from bucket: {BucketName}. Status Code: {StatusCode}", bucket, ex.StatusCode);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error occured while deleting objects from bucket: {BucketName}", bucket);
                }
            }
            
            content?.Assets?.RemoveAll(s => s.IsDeleted);
            await Save(content, userId, true, true);

            logger.LogInformation("Content {ContentId} cleanup completed.", content.Id);
        }


        private async Task RemixV2PlaylistAndLocalization(Content content, List<string>? organizations)
        {
            if (content == null) return;

            if (organizations != null)// it should be real time not via queue similar as the above relation is working
            {
                foreach (var item in organizations)
                {
                    if (!Guid.TryParse(item, out _)) continue;
                    var response = await collectionServiceClient.CreateRelation(new RelationRequest()
                    {
                        SourceId = content.Id.ToString(),
                        TargetId = item,
                        Type = SourceType.Content,
                        Relation = "Organization"
                    }, default);

                    if (response == null) throw new Exception($"Error while creating Organization relation between {content.Id} and {item}");
                }
            }
            var key = Consts.CONTENT_WORKFLOWID;
            for (int i = 1; i <= 3; i++)
            {
                var newContent = content.ShallowCopy();
                newContent.Id = Guid.NewGuid();
                newContent.OriginalTitle = $"{newContent.OriginalTitle} Playlist Track {i}";
                newContent.Type = ContentType.Collection;

                var defaultWorkflowId = configuration["Console:DefaultWorkflow:Playlist:Id"];
                if (!string.IsNullOrEmpty(defaultWorkflowId))
                {
                    newContent.Properties ??= new Dictionary<string, string>();
                    newContent.Properties[key] = defaultWorkflowId;
                }

                await repository.Create(newContent);
                _ = contentSearch.Save(newContent);

                var resp = await collectionServiceClient.CreateRelation(new RelationRequest()
                {
                    SourceId = content.Id.ToString(),
                    TargetId = newContent.Id.ToString(),
                    Type = SourceType.Content,
                    Relation = "Playlist",
                    Index = i - 1,
                }, default);

                if (organizations != null)// it should be real time not via queue similar as the above relation is working
                {
                    int index = 0;
                    foreach (var item in organizations)
                    {
                        if (!Guid.TryParse(item, out _)) continue;
                        var response = await collectionServiceClient.CreateRelation(new RelationRequest()
                        {
                            SourceId = newContent.Id.ToString(),
                            TargetId = item,
                            Type = SourceType.Content,
                            Relation = "Organization",
                            Index = index++
                        }, default);
                        if (response == null) throw new Exception($"Error while creating Organization relation between {newContent.Id} and {item}");
                    }
                }

                if (resp == null) throw new Exception("Error while creating relation");
            }

            content.Localizations ??= new Dictionary<string, Localization>();

            var defaultAudioWorkflowId = configuration["Console:DefaultWorkflow:Audio:Id"];
            content.Properties ??= new Dictionary<string, string>();
            content.Properties[key] = defaultAudioWorkflowId ?? string.Empty;

            foreach (var item in localizations)
            {
                if (content?.Localizations?.ContainsKey(item) != true) content?.Localizations?.Add(item, new Localization());
            }
        }

        private async Task AddRemixV2PlaylistRelations(Content content, RelationRequest relation)
        {
            if (content == null) return;

            var playlists = await collectionServiceClient.GetSourceRelations(content.Id.ToString(), "Content", "Playlist", default);
            foreach (var item in playlists)
            {
                var resp = await collectionServiceClient.CreateRelation(new RelationRequest()
                {
                    SourceId = item.TargetId,
                    TargetId = relation.TargetId,
                    Type = SourceType.Content,
                    Relation = "Playlist"
                }, default);

                if (resp == null) throw new Exception("Error while creating relation");
            }
        }

        private async Task<bool> IsFile(string bucketName, string key)
        {
            try
            {
                // List objects with the given key as prefix
                var response = await s3Client.ListObjectsV2Async(new ListObjectsV2Request
                {
                    BucketName = bucketName,
                    Prefix = key,
                    MaxKeys = 2 // We only need one result to check
                });

                if (response.S3Objects.Count == 0)
                {
                    // Key does not exist
                    return false;
                }

                // Check if the exact key exists as an object
                var exactMatch = response.S3Objects.FirstOrDefault(o => o.Key == key);
                if (exactMatch != null)
                {
                    // If the size is > 0, it's a file; otherwise, it's likely a folder
                    return exactMatch.Size > 0;
                }

                // If no exact match but objects exist under the prefix, it's a folder
                return false;
            }
            catch (Exception ex)
            {
                // Handle exceptions appropriately
                Console.WriteLine($"Error checking key: {ex.Message}");
                throw;
            }
        }

        #region ===================================== CONTENT POLL

        public async Task<IEnumerable<ContentPoll>> GetPollsByExternalIdAndReferenceId(string externalId, string referenceId)
        {
            if (string.IsNullOrWhiteSpace(externalId) || string.IsNullOrWhiteSpace(referenceId))
                return Array.Empty<ContentPoll>();

            return await repository.RetrievePollsByExternalIdAndReferenceId(externalId, referenceId);
        }

        public async Task<IEnumerable<ContentPoll>> GetPollsByReferenceId(string referenceId)
        {
            if (string.IsNullOrWhiteSpace(referenceId))
                return Array.Empty<ContentPoll>();

            return await repository.RetrievePollsByReferenceId(referenceId);
        }

        public async Task SavePoll(ContentPoll poll)
        {
            if (poll == null)
                throw new ArgumentNullException(nameof(poll));

            await repository.SavePoll(poll);
        }

        public async Task BatchSavePolls(IEnumerable<ContentPoll> polls)
        {
            if (polls == null)
                throw new ArgumentNullException(nameof(polls));

            await repository.BatchSavePolls(polls);
        }

        #endregion ===================================== CONTENT POLL

    }
}

