﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using Microsoft.Extensions.Logging;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Service.V2;

public class ContentMaintenance : IContentMaintenance
{
    private readonly ILogger<ContentMaintenance> logger;
    private readonly IS3Repository s3Repository;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public ContentMaintenance(ILogger<ContentMaintenance> logger, IS3Repository s3Repository,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.s3Repository = s3Repository;
        this.correlationContextAccessor = correlationContextAccessor;
    }


    public async Task<IEnumerable<string>> UpdateContentFields(Content content)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentMaintenance), nameof(UpdateContentFields))
                .Log(LogLevel.Debug, "Content: {Content}", content.ToJson());

        var changedProperties = new List<string>();
        if (await UpdateAssetFileSizes(content))
            changedProperties.Add(nameof(Asset.FileSize));

        if (UpdateAssetLocale(content))
            changedProperties.Add(nameof(Asset.Locale));

        if (UpdateObjectUrls(content))
            changedProperties.Add(nameof(Asset.PublicUrl));

        if (UpdateAssetFileName(content))
            changedProperties.Add(nameof(Asset.FileName));

        if (UpdateContentTags(content))
            changedProperties.Add(nameof(Content.Labels));

        if (UpdateAssetUploaderId(content))
            changedProperties.Add(nameof(Asset.UploaderUserId));

        return changedProperties;
    }


    /// <summary>
    /// Fills empty <see cref="Asset.FileSize"/> fields by querying AWS S3
    /// </summary>
    private async Task<bool> UpdateAssetFileSizes(Content entity)
    {
        var changed = false;
        var updateTask = Task.CompletedTask;
        try
        {
            if (entity.Assets == null) return false;

            var taskList = new List<Task>();
            foreach (var asset in entity.Assets.Where(x => x.FileSize == 0))
            {
                if (string.IsNullOrEmpty(asset.ObjectUrl)) continue;

                // if the object url points to S3, then we try to get the file size from S3
                if (ContentService.ConvertUrlToS3(asset.ObjectUrl, out var bucketName, out var fileKey))
                    if (await s3Repository.ExistsAsync(bucketName, fileKey))
                        taskList.Add(s3Repository.GetFileSize(bucketName, fileKey, asset.Id?.ToString())
                            .ContinueWith(t =>
                            {
                                asset.FileSize = t.Result.Size;
                                changed = true;
                            }));
            }

            if (taskList.Count == 0) return false;
            updateTask = Task.WhenAll(taskList);
            await updateTask;
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentMaintenance), nameof(UpdateAssetFileSizes))
                .Log(LogLevel.Error, updateTask.Exception ?? ex, "ContentId: {ContentId}", entity.Id);
        }

        return changed;
    }

    /// <summary>
    /// Copies <see cref="Content.OriginalLanguage"/> to assets with unspecified <see cref="Asset.Locale"/>
    /// </summary>
    private static bool UpdateAssetLocale(Content entity)
    {
        if (entity.Assets == null) return false;

        var changed = false;
        foreach (var asset in entity.Assets.Where(x => string.IsNullOrEmpty(x.Locale)))
        {
            asset.Locale = entity.OriginalLanguage;
            changed = true;
        }

        return changed;
    }

    /// <summary>
    /// Generates <see cref="Asset.PublicUrl"/> to assets with CDN return
    /// </summary>
    private static bool UpdateObjectUrls(Content entity)
    {
        if (entity.Assets == null) return false;

        var changed = false;
        foreach (var asset in entity.Assets.Where(x => string.IsNullOrEmpty(x.PublicUrl) && x.IsPublic))
        {
            if (asset.SubType == SubType.HLS && entity.Type == ContentType.LiveStream) continue;

            var cdnUrl = GeneratePublicCdnUrl(asset.ObjectUrl);
            if (string.IsNullOrEmpty(cdnUrl)) continue;

            asset.PublicUrl = cdnUrl;
            changed = true;
        }

        return changed;
    }

    /// <summary>
    /// Computes <see cref="Asset.FileName"/> from <see cref="Asset.ObjectUrl"/>
    /// </summary>
    private static bool UpdateAssetFileName(Content entity)
    {
        if (entity.Assets == null) return false;

        var changed = false;
        foreach (var asset in entity.Assets.Where(x => string.IsNullOrEmpty(x.FileName)))
        {
            if (string.IsNullOrEmpty(asset.ObjectUrl)) continue;
            var objectUrl = asset.ObjectUrl.TrimEnd('/');
            asset.FileName = Path.GetFileName(objectUrl);
            changed = true;
        }

        return changed;
    }

    /// <summary>
    /// Attaches and removes tags based on the <see cref="Content.Assets"/> collection
    /// </summary>
    private static bool UpdateContentTags(Content entity)
    {
        var changed = false;
        var validAssets = (entity.Assets ?? new List<Asset>())
            .Where(a => !string.IsNullOrEmpty(a.ObjectUrl))
            .Where(a => !a.IsDeleted).ToList();

        var hasOriginal = validAssets.Any(a => a.SubType == SubType.Original && !a.IsDeleted);
        if (hasOriginal)
        {
            if (AddTag(entity, "Originalassetexist")) changed = true;
        }
        else if (RemoveTag(entity, "Originalassetexist"))
        {
            changed = true;
        }

        var hasSubtitle = validAssets.Any(a => IsSubtitle(a.SubType));
        if (hasSubtitle)
        {
            if (AddTag(entity, "Subtitleexist")) changed = true;
        }
        else if (RemoveTag(entity, "Subtitleexist"))
        {
            changed = true;
        }

        var isTranscoded = validAssets.Any(a => IsStreamingManifest(a.SubType));
        if (isTranscoded)
        {
            if (AddTag(entity, "Transcodeexist")) changed = true;
        }
        else if (RemoveTag(entity, "Transcodeexist"))
        {
            changed = true;
        }

        return changed;
    }


    /// <summary>
    /// Generates a CDN-enabled URL for the native AWS S3 URL, <paramref name="url"/>
    /// </summary>
    /// <returns>The CDN-enabled URL or <see langword="null"/></returns>
    private static string? GeneratePublicCdnUrl(string? url)
    {
        if (!Uri.TryCreate(url, UriKind.Absolute, out var contentUrl)) return null;

        var cdnUrl = Environment.GetEnvironmentVariable("CDNURL");
        if (string.IsNullOrEmpty(cdnUrl)) return null;
        if (url.Contains(cdnUrl)) return null;

        return $"{cdnUrl}{contentUrl.AbsolutePath}";
    }

    /// <summary>
    /// Decides whether <paramref name="subtype"/> identifies a subtitle asset
    /// </summary>
    private static bool IsSubtitle(SubType subtype)
    {
        return subtype == SubType.SubtitleVtt
               || subtype == SubType.SubtitleSrt
               || subtype == SubType.SubtitleDfxp;
    }

    /// <summary>
    /// Decides whether <paramref name="subtype"/> identifies a streaming media manifest
    /// </summary>
    private static bool IsStreamingManifest(SubType subtype)
    {
        return subtype == SubType.HLS
               || subtype == SubType.HLS_4K
               || subtype == SubType.DASH
               || subtype == SubType.DASH_4K
               || subtype == SubType.MSS;
    }

    /// <summary> Adds the specified <paramref name="tag"/> to the <paramref name="content"/> if it does not exist </summary>
    /// <returns> <see langword="true"/> if the tag was added; otherwise, <see langword="false"/> </returns>
    private static bool AddTag(Content content, string tag)
    {
        content.Labels ??= new Dictionary<LabelType, List<string>>();
        if (!content.Labels.ContainsKey(LabelType.Tag)) content.Labels.Add(LabelType.Tag, new List<string>());

        var cmp = StringComparer.OrdinalIgnoreCase;
        if (content.Labels[LabelType.Tag].Contains(tag, cmp)) return false;

        content.Labels[LabelType.Tag].Add(tag);
        return true;
    }

    /// <summary> Removes the specified <paramref name="tag"/> from the <paramref name="content"/> if it does not exist </summary>
    /// <returns> <see langword="true"/> if the tag was removed; otherwise, <see langword="false"/> </returns>
    private static bool RemoveTag(Content content, string tag)
    {
        content.Labels ??= new Dictionary<LabelType, List<string>>();
        if (!content.Labels.ContainsKey(LabelType.Tag)) return false;

        var cmp = StringComparer.OrdinalIgnoreCase;
        return content.Labels[LabelType.Tag].RemoveAll(entry => cmp.Equals(tag, entry)) > 0;
    }

    /// <summary>
    /// Updates the <see cref="Asset.UploaderUserId"/> property to match the <see cref="Content.OwnerId"/> property if it is null or empty
    /// </summary>
    private static bool UpdateAssetUploaderId(Content entity)
    {
        if (entity.Assets == null) return false;

        var changed = false;
        foreach (var asset in entity.Assets.Where(x => string.IsNullOrEmpty(x.UploaderUserId)))
        {
            asset.UploaderUserId = entity.OwnerId.ToString();
            changed = true;
        }

        return changed;
    }
}