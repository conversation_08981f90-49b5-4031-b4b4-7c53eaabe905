using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Services;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Service.V2;

/// <summary>
/// This class is responsible for downloading manifests from the given URLs.
/// </summary>
public class ManifestDownloader : IManifestDownloader
{
    private readonly ILogger<ManifestDownloader> logger;
    private readonly HttpClient httpClient;

    public ManifestDownloader(
        ILogger<ManifestDownloader> logger,
        HttpClient httpClient)
    {
        this.httpClient = httpClient;
        this.logger = logger;
    }

    /// <summary>
    /// Downloads a manifest from the given URL.
    /// </summary>
    /// <param name="manifestUrl"></param>
    /// <returns></returns>
    public async Task<string?> DownloadManifestAsync(string manifestUrl)
    {
        try
        {
            var response = await httpClient.GetAsync(manifestUrl);
            response.EnsureSuccessStatusCode();

            var manifestContent = await response.Content.ReadAsStringAsync();
            return manifestContent;
        }
        catch (Exception ex)
        {
            logger.LogWarning($"Failed to download manifest '{manifestUrl}' because {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Downloads multiple manifests from the given URLs.
    /// </summary>
    /// <param name="manifestUrls"></param>
    /// <returns></returns>
    public async Task<Dictionary<string, string>> DownloadMultipleManifestsAsync(List<string> manifestUrls)
    {
        var manifestContents = new Dictionary<string, string>();

        var counter = 0;
        foreach (var manifestUrl in manifestUrls)
        {
            var manifestContent = await DownloadManifestAsync(manifestUrl);
            if (!string.IsNullOrEmpty(manifestContent))
                manifestContents.Add(++counter + "___" + manifestUrl, manifestContent);
        }

        return manifestContents;
    }
}