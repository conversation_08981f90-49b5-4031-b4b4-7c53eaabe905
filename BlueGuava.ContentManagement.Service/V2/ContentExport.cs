﻿using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.Extensions.AsyncEnumerable;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.NotificationService.Client;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Service.V2;

public class ContentExport : IContentExport
{
    private readonly ILogger<ContentService> logger;
    private readonly IOptionsMonitor<S3Bucket> bucketConfig;
    private readonly IContentRepository repository;
    private readonly IS3Repository s3Repository;
    private readonly INotificationService notification;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public ContentExport(ILogger<ContentService> logger, IOptionsMonitor<S3Bucket> bucketConfig,
        IContentRepository repository, IS3Repository s3Repository, INotificationService notification,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.bucketConfig = bucketConfig;
        this.repository = repository;
        this.s3Repository = s3Repository;
        this.notification = notification;
        this.correlationContextAccessor = correlationContextAccessor;
    }

    public async Task ExportContents(ExportRequest rawExportRequest, ClaimsPrincipal user)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ContentExport), nameof(ExportContents))
                .Log(LogLevel.Debug, "ExportRequest: {ExportRequest}", rawExportRequest.ToJson());

        var jobName = "Content_Export_" + DateTime.UtcNow.Ticks;

        var contents = await rawExportRequest.ItemIds.SplitList(50)
            .Select(args => repository.BatchGet(args)).AsAsyncEnumerable()
            .SelectManyAsync(batch => batch).ToListAsync(rawExportRequest.ItemIds.Count);

        if (contents.Count > 0)
        {
            var fileKey = $"contentExport_{DateTime.UtcNow.Ticks}.{rawExportRequest.OutputFileExtension}";
            var exportData = contents.ToJson();

            if (rawExportRequest.OutputFileExtension.ToLower() == "csv")
            {
                var exporter = new ExportCSV();
                exportData = exporter.Write(contents);
            }

            await s3Repository.PutFileAsync(exportData, bucketConfig.CurrentValue.FileExport, fileKey,
                rawExportRequest.OutputFileExtension);

            var publicUrl = s3Repository.GeneratePreSignedUrl(168, bucketConfig.CurrentValue.FileExport, fileKey);

            await SendExportNotification(JobStatus_SUCCEED, jobName, "The file is created", publicUrl,
                user.GetCustomerId());
        }
        else
        {
            await SendExportNotification(JobStatus_FAILED, jobName, "The given contents are not found in the database",
                null, user.GetCustomerId());
        }
    }

    private const string JobStatus_SUCCEED = "SUCCEED";
    private const string JobStatus_FAILED = "FAILED";

    private async Task SendExportNotification(string status, string jobName, string message, string publicUrl,
        Guid customerId)
    {
        await notification.Trigger(MessageCenter.Common.EventName.ExportFile, customerId, new
        {
            objectType = "File",
            jobType = "export",
            jobName = jobName,
            fileName = publicUrl,
            jobStatus = status.ToString(),
            jobMessage = message,
            scheduled = DateTime.UtcNow,
            scheduledCount = 1
        });
    }
}