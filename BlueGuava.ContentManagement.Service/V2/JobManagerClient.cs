﻿using BlueGuava.HttpRepository;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Common.Entities;
using CorrelationId;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.Extensions.Logging;

namespace BlueGuava.ContentManagement.Service.V2;

public interface IJobManager
{
    Task<SchedulingJob> RetrieveJobEntity(JobType jobType, string jobId, CancellationToken cancel);
}

public class JobManagerClient : IJobManager
{
    private readonly ILogger<JobManagerClient> logger;
    private readonly IHttpRepository jobManagerHttpRepository;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public JobManagerClient(ILogger<JobManagerClient> logger, ICorrelationContextAccessor correlationContextAccessor,
        IHttpRepositoryProvider httpRepositoryProvider)
    {
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
        jobManagerHttpRepository = httpRepositoryProvider.CreateHttpRepository(ServiceNames.JobManager);
    }

    public async Task<SchedulingJob?> RetrieveJobEntity(JobType jobType, string jobId, CancellationToken cancel)
    {
        logger.Standards(correlationContextAccessor, nameof(JobManagerClient), nameof(RetrieveJobEntity))
            .Log(LogLevel.Information, "JobType: {JobType}, JobId: {JobId}", jobId, jobType);
        try
        {
            var type = GetJobType(jobType);
            return await GetJob(jobType, type, jobId, cancel);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(JobManagerClient), nameof(RetrieveJobEntity))
                .Log(LogLevel.Error, ex, "JobType: {JobType}, JobId: {JobId}", jobId, jobType);
            return null;
        }
    }

    private static Type GetJobType(JobType jobType)
    {
        return jobType switch
        {
            JobType.SCHEDULING => typeof(SchedulingJob),

            _ => throw new ArgumentOutOfRangeException(nameof(jobType), jobType.ToString(), "Unknown job type")
        };
    }

    private async Task<SchedulingJob?> GetJob(JobType jobType, Type type, string jobId, CancellationToken cancel)
    {
        return jobType switch
        {
            JobType.SCHEDULING => await jobManagerHttpRepository.RetrieveAsync<SchedulingJob>(
                $"api/v1.0/{type.Name}/{jobId}", null, cancel),
            _ => throw new ArgumentOutOfRangeException(nameof(jobType), jobType.ToString(), "Unknown job type")
        };
    }
}