﻿using System;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.Logging;
using BlueGuava.Extensions.Paging;
using CorrelationId;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Service.V2;

public class PagingContentList : IPagingContentList
{
    private readonly ILogger<PagingContentList> logger;
    private readonly IPagingContentRepository pagingRepository;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public PagingContentList(ILogger<PagingContentList> logger, IPagingContentRepository pagingRepository,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.pagingRepository = pagingRepository;
        this.correlationContextAccessor = correlationContextAccessor;
    }

    public async Task<PagedResult<Content?>> StartPaging(int itemLimit)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(PagingContentList), nameof(StartPaging))
                .Log(LogLevel.Debug, "ItemLimit: {ItemLimit}", itemLimit);

        return await pagingRepository.StartPaging(itemLimit);
    }

    public async Task<PagedResult<Content?>> GetNextPage(string pagingToken)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(PagingContentList), nameof(StartPaging))
                .Log(LogLevel.Debug, "PagingToken: {PagingToken}", pagingToken);

        return await pagingRepository.GetNextPage(pagingToken);
    }

    public async Task<PagedResult<ContentPoll>> StartPagingByReference(string referenceId, int itemLimit)
    {
        if (string.IsNullOrWhiteSpace(referenceId))
            return PagedResult.Create(Array.Empty<ContentPoll>(), null);

        return await pagingRepository.StartPagingByReference(referenceId, itemLimit);
    }

    public async Task<PagedResult<ContentPoll>> GetNextPageByReference(string referenceId, string pagingToken)
    {
        if (string.IsNullOrWhiteSpace(referenceId) || string.IsNullOrWhiteSpace(pagingToken))
            return PagedResult.Create(Array.Empty<ContentPoll>(), null);

        return await pagingRepository.GetNextPageByReference(referenceId, pagingToken);
    }
}