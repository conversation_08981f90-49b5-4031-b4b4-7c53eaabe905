﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AsyncEnumerable;
using BlueGuava.ItemsProcessing;
using BlueGuava.MessageQueuing;

namespace BlueGuava.ContentManagement.Service.V2;

public class ReindexRunner : IItemProcessor<ReindexCommand>
{
    private readonly IContentService service;
    private readonly IMessageQueue<UpdateMessage> contentUpdateMessagingService;

    public ReindexRunner(IContentService service, IMessageQueue<UpdateMessage> contentUpdateMessagingService)
    {
        this.service = service;
        this.contentUpdateMessagingService = contentUpdateMessagingService;
    }

    public string Name { get; } = nameof(ReindexRunner);

    public async Task Process(WorkItemContext<ReindexCommand> workItemContext)
    {
        if (workItemContext.WorkItem.SKUPackageSetup)
        {
            await foreach (var batch in service.ScanThroughWithType((int)ContentType.Video).SliceAsync(30))
                await Task.WhenAll(batch.Select(id =>
                    contentUpdateMessagingService.Enqueue(new ReindexContentMessage(id)
                    {
                        MigrateChapterMarkers = workItemContext.WorkItem.MigrateChapters,
                        CalculateLabels = workItemContext.WorkItem.CalculateLabels,
                        FixDuration = workItemContext.WorkItem.FixDuration,
                        SKUPackageSetup = workItemContext.WorkItem.SKUPackageSetup,
                        AuthGroupId = workItemContext.WorkItem.AuthGroupId,
                        CleanDeletedFile = workItemContext.WorkItem.CleanDeletedFile,
                    })));
        }
        else
        {
            await foreach (var batch in service.ScanThrough(new ContentSearch()).SliceAsync(30))
                await Task.WhenAll(batch.Select(id =>
                    contentUpdateMessagingService.Enqueue(new ReindexContentMessage(id)
                    {
                        MigrateChapterMarkers = workItemContext.WorkItem.MigrateChapters,
                        CalculateLabels = workItemContext.WorkItem.CalculateLabels,
                        FixDuration = workItemContext.WorkItem.FixDuration,
                        SKUPackageSetup = workItemContext.WorkItem.SKUPackageSetup,
                        CleanDeletedFile = workItemContext.WorkItem.CleanDeletedFile,
                    })));
        }
    }
}

public class ReindexCommand
{
    public bool MigrateChapters { get; set; }
    public bool CalculateLabels { get; set; }
    public bool FixDuration { get; set; }
    public bool SKUPackageSetup { get; set; }
    public Guid? AuthGroupId { get; set; }
    public bool CleanDeletedFile { get; set; }

    public ReindexCommand(bool migrateChapters = false, bool calculateLabels = false, bool fixDuration = false, bool sKUPackageSetup = false, Guid? authGroupId = null, bool cleanDeletedFile = false)
    {
        MigrateChapters = migrateChapters;
        CalculateLabels = calculateLabels;
        FixDuration = fixDuration;
        SKUPackageSetup = sKUPackageSetup;
        AuthGroupId = authGroupId;
        CleanDeletedFile = cleanDeletedFile;
    }
}