using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.HttpRepository;
using BlueGuava.MessageQueuing;
using BlueGuava.Tracewind.Common.Models;
using M3U8Parser;
using M3U8Parser.Attributes.BaseAttribute;
using M3U8Parser.ExtXType;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;

namespace BlueGuava.ContentManagement.Service.V2;

public class DynamicManifestGenerator : IDynamicManifestGenerator
{
    private const string RELATION_API_ROUTE_PREFIX = "/api/v2.0/Relation";

    private readonly ILogger<DynamicManifestGenerator> logger;
    private readonly IS3Repository s3Repository;
    private readonly IHttpRepository relationHttpRepository;
    private readonly IContentService contentService;
    private readonly IManifestDownloader manifestDownloader;
    private readonly IMessageQueue<UpdateMessage> contentUpdateMessagingService;
    private readonly IMessageQueue<TraceLogMessage> traceLog;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdates;
    private readonly ModuleInfo moduleInfo;
    private readonly string bucketNamePublic;
    private readonly string cdnUrl;
    private readonly IFeatureManager featureManager;

    public DynamicManifestGenerator(
        ILogger<DynamicManifestGenerator> logger,
        IOptionsMonitor<S3Bucket> bucketSettings,
        IOptions<ModuleInfo> moduleInfo,
        IS3Repository s3Repository,
        IHttpRepositoryProvider httpRepositoryProvider,
        IContentService contentService,
        IManifestDownloader manifestDownloader,
        IMessageQueue<UpdateMessage> contentUpdateMessagingService,
        IMessageQueue<TraceLogMessage> traceLog,
        IOptionsMonitor<CdnSettings> cdnSettings,
        IConfiguration configuration,
        IFeatureManager featureManager,
        IMessageQueue<RelationshipUpdate> relationshipUpdates)
    {
        this.logger = logger;
        this.s3Repository = s3Repository;
        this.contentService = contentService;
        this.manifestDownloader = manifestDownloader;
        this.contentUpdateMessagingService = contentUpdateMessagingService;
        this.traceLog = traceLog;
        this.featureManager = featureManager;
        this.relationshipUpdates = relationshipUpdates;
        this.moduleInfo = moduleInfo.Value;

        relationHttpRepository = httpRepositoryProvider.CreateHttpRepository(ServiceNames.Collections);
        bucketNamePublic = bucketSettings.CurrentValue.Contents ?? "ent-contents-dev-brand";
        cdnUrl = cdnSettings.CurrentValue.DefaultUrl ?? configuration["CDNURL"] ?? "";
    }

    public async Task TriggerManifestGeneration(Content playlist)
    {
        if (!await featureManager.IsEnabledAsync("Allow_DynamicManifestManipulation"))
            return;

        try
        {
            if (playlist is not { Type: ContentType.Playlist })
                return;

            var locale = playlist.OriginalLanguage ?? "en-US";
            // Update the content with the new DASH manifest via SQS
            var message = new AssetUpdateMessage
            {
                ContentId = playlist.Id.ToString(),
                Type = AssetType.Video,
                SubType = SubType.DASH,
                ObjectUrl = "",
                PublicUrl = "",
                FileName = "",
                WorkflowStatus = WorkflowStatus.Triggered,
                Locale = locale
            };

            await contentUpdateMessagingService.Enqueue(message);

            message.Id = Guid.NewGuid().ToString();
            message.SubType = SubType.HLS;
            await contentUpdateMessagingService.Enqueue(message);

            //TODO: Add a delay here to allow the SQS messages to be processed
            await relationshipUpdates.Enqueue(new RelationshipRefresh()
            {
                Relation = ContentRelation.Playlist,
                SourceId = playlist.Id.ToString()
            }, 10);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error triggering manifests for content id: {ContentId}", playlist.Id.ToString());
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = playlist.Id.ToString(),
                CorrelationId = playlist.Id.ToString(),
                ObjectType = (int)ObjectType.Content,
                Description = $"Error triggering manifests for content id: {playlist.Id.ToString()} - {ex.Message}",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });
        }
    }

    /// <summary>
    /// Generates the DASH and HLS manifests for the given content id <br/>
    /// if the feature flag "Allow_DynamicManifestManipulation" is enabled
    /// </summary>
    /// <param name="contentId"></param>
    /// <exception cref="Exception"></exception>
    public async Task GenerateManifests(string contentId)
    {
        if (!Guid.TryParse(contentId, out var cId))
            // Don't generate manifests for invalid content ids
            return;

        if (!await featureManager.IsEnabledAsync("Allow_DynamicManifestManipulation"))
            return;

        try
        {
            logger.LogDebug("Generating manifests for content id: {ContentId}", contentId);

            var playlist = await contentService.Retrieve(cId);
            if (playlist is not { Type: ContentType.Playlist })
                return;

            logger.LogDebug("Retrieving playlist items for content id: {ContentId}", contentId);
            var uri = $"{RELATION_API_ROUTE_PREFIX}/{contentId}/Content/Playlist/List";
            var relations = await relationHttpRepository.RetrieveAsync<Pagination<Relation>>(uri, null);

            var dashManifestUrls = new List<string>();
            var hlsManifestUrls = new List<string>();

            // Retrieve the DASH and HLS manifest URLs from the playlist items
            if (relations?.PageContent != null)
            {
                logger.LogDebug("Retrieved {Count} playlist items for content id: {ContentId}",
                    relations.PageContent.Count, contentId);
                foreach (var relation in relations.PageContent)
                {
                    if (!Guid.TryParse(relation.TargetId, out var playlistItemId)) continue;

                    var playlistItem = await contentService.Retrieve(playlistItemId);
                    if (playlistItem == null || !playlistItem.Published)
                        continue;

                    var dashAsset = playlistItem?.Assets?.FirstOrDefault(x =>
                        x.SubType == SubType.DASH && !string.IsNullOrEmpty(x.PublicUrl));

                    if (dashAsset != null)
                        dashManifestUrls.Add(dashAsset.PublicUrl!);

                    var hlsAsset = playlistItem?.Assets?.FirstOrDefault(x =>
                        x.SubType == SubType.HLS && !string.IsNullOrEmpty(x.PublicUrl));
                    if (hlsAsset != null)
                        hlsManifestUrls.Add(hlsAsset.PublicUrl!);
                }
            }

            if (dashManifestUrls.Any())
            {
                logger.LogDebug("Downloading DASH manifests for content id: {ContentId}", contentId);
                var dashManifest = await manifestDownloader.DownloadMultipleManifestsAsync(dashManifestUrls);
                await SaveDashPlaylist(contentId, playlist.OriginalLanguage ?? "en-US", dashManifest);
            }
            else
            {
                logger.LogDebug("No DASH manifests found for content id: {ContentId}", contentId);
                await traceLog.Enqueue(new TraceLogMessage
                {
                    ObjectId = cId.ToString(),
                    CorrelationId = cId.ToString(),
                    ObjectType = (int)ObjectType.Content,
                    Description = "No DASH manifests found or no public URLs",
                    CreatedDate = DateTime.UtcNow,
                    IpAddress = Environment.MachineName,
                    Source = moduleInfo.Name,
                    SourceVersion = moduleInfo.Version
                });
            }

            if (hlsManifestUrls.Any())
            {
                logger.LogDebug("Downloading HLS manifests for content id: {ContentId}", contentId);
                var hlsManifest = await manifestDownloader.DownloadMultipleManifestsAsync(hlsManifestUrls);
                await SaveHlsPlaylist(contentId, playlist.OriginalLanguage ?? "en-US", hlsManifest);
            }
            else
            {
                logger.LogDebug("No HLS manifests found for content id: {ContentId}", contentId);
                await traceLog.Enqueue(new TraceLogMessage
                {
                    ObjectId = cId.ToString(),
                    CorrelationId = cId.ToString(),
                    ObjectType = (int)ObjectType.Content,
                    Description = "No HLS manifests found or no public URLs",
                    CreatedDate = DateTime.UtcNow,
                    IpAddress = Environment.MachineName,
                    Source = moduleInfo.Name,
                    SourceVersion = moduleInfo.Version
                });
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating manifests for content id: {ContentId}", contentId);
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = cId.ToString(),
                CorrelationId = cId.ToString(),
                ObjectType = (int)ObjectType.Content,
                Description = $"Error generating manifests for content id: {contentId} - {ex.Message}",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });
        }
    }

    /// <summary>
    /// Save the DASH playlist to a S3 file
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="locale"></param>
    /// <param name="dashManifests"></param>
    private async Task SaveDashPlaylist(string contentId, string locale, Dictionary<string, string> dashManifests)
    {
        try
        {
            var doc = new XmlDocument();

            // Create the root element with the correct namespace
            var mpdElement = doc.CreateElement("MPD", "urn:mpeg:dash:schema:mpd:2011");
            doc.AppendChild(mpdElement);

            // Set attributes for root element
            mpdElement.SetAttribute("type", "static");
            mpdElement.SetAttribute("minBufferTime", "PT1S");
            mpdElement.SetAttribute("profiles", "urn:mpeg:dash:profile:isoff-main:2011");
            mpdElement.SetAttribute("xsi:schemaLocation",
                "urn:mpeg:dash:schema:mpd:2011 http://standards.iso.org/ittf/PubliclyAvailableStandards/MPEG-DASH_schema_files/DASH-MPD.xsd");
            mpdElement.SetAttribute("xmlns:cenc", "urn:mpeg:cenc:2013");
            mpdElement.SetAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");

            var periodIndex = 0;
            var periodStart = "PT0S";
            foreach (var dashManifest in dashManifests)
            {
                var dashManifestUrl = dashManifest.Key.Split("___")[1];

                // Load each dash manifest
                var dashDoc = new XmlDocument();
                dashDoc.LoadXml(dashManifest.Value);

                // Get the root element of the dash manifest
                var rootElement = dashDoc.DocumentElement;
                if (rootElement?.ChildNodes == null)
                    throw new ArgumentException($"Invalid DASH manifest {dashManifestUrl}");

                foreach (XmlElement element in rootElement.ChildNodes)
                {
                    var periodElements = dashDoc.GetElementsByTagName("Period");
                    foreach (XmlElement el in periodElements)
                    {
                        var e = dashDoc.CreateElement("BaseURL");
                        var fileName = Path.GetFileName(dashManifestUrl);
                        e.InnerText = dashManifestUrl.Replace(fileName, "");
                        el.AppendChild(e);

                        el.SetAttribute("start", periodStart);
                        el.SetAttribute("id", $"{++periodIndex}");
                        periodStart = TimeSpanToMpdString(MpdTimeStringToTimeSpan(periodStart) +
                                                          MpdTimeStringToTimeSpan(el.GetAttribute("duration")));
                    }

                    // Import the root element into the playlist document
                    var importedElement = doc.ImportNode(element, true);
                    mpdElement.AppendChild(importedElement);
                }
            }

            //save to S3 update content
            var fileKey = $"{contentId}/DASH/{DateTime.UtcNow.Ticks}/{contentId}.mpd";
            await s3Repository.PutFileAsync(mpdElement.OuterXml, bucketNamePublic, fileKey, "dash+xml");

            // Update the content with the new DASH manifest via SQS
            var message = new AssetUpdateMessage
            {
                ContentId = contentId,
                Type = AssetType.Video,
                SubType = SubType.DASH,
                ObjectUrl = $"https://{bucketNamePublic}.s3.{s3Repository.GetCurrentRegion()}.amazonaws.com/" + fileKey,
                PublicUrl = $"{cdnUrl}/{fileKey}",
                WorkflowStatus = WorkflowStatus.Succeeded,
                Locale = locale
            };

            await contentUpdateMessagingService.Enqueue(message);

            var objectId = Guid.TryParse(contentId, out var oId) ? oId : Guid.Empty;
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = objectId.ToString(),
                CorrelationId = objectId.ToString(),
                ObjectType = (int)ObjectType.Content,
                Description = $"DASH manifest generated at: {message.ObjectUrl}",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error generating dynamic DASH manifest");
            throw;
        }
    }

    /// <summary>
    /// Convert a TimeSpan to a MPD string
    /// </summary>
    /// <param name="timeSpan"></param>
    /// <returns></returns>
    private string TimeSpanToMpdString(TimeSpan timeSpan)
    {
        var mpdString = $"PT{timeSpan.TotalMinutes:0}M{timeSpan.Seconds}.{timeSpan.Milliseconds:000}S";
        return mpdString;
    }

    /// <summary>
    /// Convert a MPD string to a TimeSpan
    /// </summary>
    /// <param name="mpdTimeString"></param>
    /// <returns></returns>
    private TimeSpan MpdTimeStringToTimeSpan(string mpdTimeString)
    {
        var timeSpan = XmlConvert.ToTimeSpan(mpdTimeString);

        return timeSpan;
    }


    public async Task SaveHlsPlaylist(string contentId, string locale, Dictionary<string, string> hlsManifests)
    {
        // Create a new master playlist
        var mediaPlaylist = new Dictionary<string, MediaPlaylist>();
        var audioMediaPlaylist = new Dictionary<string, MediaPlaylist>();
        MasterPlaylist? masterPlayList = null;

        var basePath = $"{contentId}/HLS/{DateTime.UtcNow.Ticks}";
        var createHeaders = true;
        //Read the contents of each manifest file
        foreach (var manifestFile in hlsManifests)
            try
            {
                var manifestFileKey = manifestFile.Key.Split("___")[1];

                var fileName = Path.GetFileName(manifestFileKey);
                //actual media file url
                var baseUrl = manifestFileKey.Replace(fileName, "");
                var innerMasterPlaylist = MasterPlaylist.LoadFromText(manifestFile.Value);

                masterPlayList ??= new MasterPlaylist(innerMasterPlaylist.HlsVersion);

                var index = 0;
                foreach (var stream in innerMasterPlaylist.Streams.OrderBy(x => x.Uri))
                {
                    var indexName = $"v{++index}";
                    var fileKey = $"{contentId}_{indexName}.m3u8";
                    var outputPath = $"{cdnUrl}/{basePath}/{fileKey}";
                    var playlistResponse = await manifestDownloader.DownloadManifestAsync($"{baseUrl}{stream.Uri}");

                    // adds the stream to the master playlist this will hold the individual playlist references
                    if (createHeaders)
                        masterPlayList.Streams.Add(new StreamInf
                        {
                            Uri = outputPath,
                            Bandwidth = stream.Bandwidth,
                            AverageBandwidth = stream.AverageBandwidth,
                            Codecs = stream.Codecs,
                            FrameRate = stream.FrameRate,
                            VideoRange = stream.VideoRange,
                            HdcpLevel = stream.HdcpLevel,
                            Audio = stream.Audio,
                            Video = stream.Video,
                            Subtitles = stream.Subtitles,
                            ClosedCaptions = stream.ClosedCaptions,
                            Resolution = stream.Resolution
                        });

                    if (!string.IsNullOrEmpty(playlistResponse))
                    {
                        // merge the individual playlist into a single playlist (this is the playlist that will be downloaded by the player)
                        var playList = MediaPlaylist.LoadFromText(playlistResponse);
                        //adds actual media file url to the playlist
                        playList.MediaSegments.ForEach(x => x.Segments.ForEach(y => y.Uri = $"{baseUrl}{y.Uri}"));
                        if (!mediaPlaylist.ContainsKey(fileKey))
                        {
                            var initUri = $"{baseUrl}{GetBetween(playlistResponse, "#EXT-X-MAP:URI=\"", "\n")}";
                            playList.MediaSegments.FirstOrDefault()?.Segments
                                .Insert(0, new MySegment($"#EXT-X-MAP:URI=\"{initUri}"));
                            mediaPlaylist.Add(fileKey, playList);
                        }
                        else
                        {
                            if (playList.MediaSegments.Count > 0)
                            {
                                playList.MediaSegments.FirstOrDefault()?.Segments
                                    .Insert(0, new MySegment("#EXT-X-DISCONTINUITY"));
                                mediaPlaylist[fileKey].MediaSegments.AddRange(playList.MediaSegments);
                            }
                        }
                    }
                }

                var audioFileKey = $"{contentId}_audio.m3u8";
                var audioMedia =
                    innerMasterPlaylist.Medias?.FirstOrDefault(x =>
                        x.Type != null && x.Type.ToString() == MediaType.Audio.ToString());
                if (audioMedia != null)
                {
                    var audioPlaylistResponse =
                        await manifestDownloader.DownloadManifestAsync($"{baseUrl}{audioMedia.Uri}");
                    if (createHeaders)
                    {
                        audioMedia.Uri = $"{cdnUrl}/{basePath}/{audioFileKey}";
                        masterPlayList.Medias.Add(audioMedia);
                    }

                    if (!string.IsNullOrEmpty(audioPlaylistResponse))
                    {
                        var audioPlayList = MediaPlaylist.LoadFromText(audioPlaylistResponse);

                        audioPlayList.MediaSegments.ForEach(x => x.Segments.ForEach(y => y.Uri = $"{baseUrl}{y.Uri}"));
                        if (!audioMediaPlaylist.ContainsKey(audioFileKey))
                        {
                            var between = GetBetween(audioPlaylistResponse, "#EXT-X-MAP:URI=\"", "\n");
                            var initUri = $"{baseUrl}{between}";
                            audioPlayList.MediaSegments.FirstOrDefault()?.Segments
                                .Insert(0, new MySegment($"#EXT-X-MAP:URI=\"{initUri}"));
                            audioMediaPlaylist.Add(audioFileKey, audioPlayList);
                        }
                        else
                        {
                            if (audioPlayList.MediaSegments.Count > 0)
                            {
                                audioPlayList.MediaSegments.FirstOrDefault()?.Segments
                                    .Insert(0, new MySegment("#EXT-X-DISCONTINUITY"));
                                audioMediaPlaylist[audioFileKey].MediaSegments.AddRange(audioPlayList.MediaSegments);
                            }
                        }
                    }
                }

                createHeaders = false;
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error generating dynamic HLS manifest {FileKey} {File}", manifestFile.Key,
                    manifestFile.Value);
                throw;
            }

        var masterFileKey = $"{basePath}/{contentId}.m3u8";
        await s3Repository.PutFileAsync(masterPlayList.ToString(), bucketNamePublic, masterFileKey, "x-mpegURL");
        foreach (var playlist in mediaPlaylist)
            await s3Repository.PutFileAsync(playlist.Value.ToString(), bucketNamePublic, $"{basePath}/{playlist.Key}",
                "x-mpegURL");

        foreach (var playlist in audioMediaPlaylist)
            await s3Repository.PutFileAsync(playlist.Value.ToString(), bucketNamePublic, $"{basePath}/{playlist.Key}",
                "x-mpegURL");

        // Update the content with the new DASH manifest via SQS
        var message = new AssetUpdateMessage
        {
            ContentId = contentId,
            Type = AssetType.Video,
            SubType = SubType.HLS,
            ObjectUrl = $"https://{bucketNamePublic}.s3.{s3Repository.GetCurrentRegion()}.amazonaws.com/" +
                        masterFileKey,
            PublicUrl = $"{cdnUrl}/{masterFileKey}",
            WorkflowStatus = WorkflowStatus.Succeeded,
            Locale = locale
        };

        await contentUpdateMessagingService.Enqueue(message);

        var objectId = Guid.TryParse(contentId, out var oId) ? oId : Guid.Empty;
        await traceLog.Enqueue(new TraceLogMessage
        {
            ObjectId = objectId.ToString(),
            CorrelationId = objectId.ToString(),
            ObjectType = (int)ObjectType.Content,
            Description = $"HLS manifest generated at: {message.ObjectUrl}",
            CreatedDate = DateTime.UtcNow,
            IpAddress = Environment.MachineName,
            Source = moduleInfo.Name,
            SourceVersion = moduleInfo.Version
        });
    }

    public static string GetBetween(string content, string startString, string endString)
    {
        int Start = 0, End = 0;
        if (content.Contains(startString) && content.Contains(endString))
        {
            Start = content.IndexOf(startString, 0) + startString.Length;
            End = content.IndexOf(endString, Start);
            return content.Substring(Start, End - Start);
        }
        else
        {
            return string.Empty;
        }
    }
}

public class MySegment : Segment
{
    public MySegment(string uri) : base(uri)
    {
        Uri = uri;
    }

    public override string ToString()
    {
        var stringBuilder1 = new StringBuilder();
        stringBuilder1.AppendLine(Uri);
        stringBuilder1.AppendLine();
        return stringBuilder1.ToString();
    }
}