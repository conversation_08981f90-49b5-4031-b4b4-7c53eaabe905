using System;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.Library;
using BlueGuava.Library.Interop.v2;
using BlueGuava.MessageQueuing;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueGuava.ContentManagement.Service;

public class InteropObjectSequencePayloadProcessor : IObjectSequenceProcessor<Library.Interop.v2.Object>
{
    private Subject<Library.Interop.v2.Object>? contentIdSequence;
    private readonly ILogger<InteropObjectSequencePayloadProcessor> logger;
    private readonly IMessageQueue<InteropObjectMessage> contentReleaseMessageQueue;
    private readonly IOptionsMonitor<ItemSequenceOptions> options;

    public InteropObjectSequencePayloadProcessor(
        IMessageQueue<InteropObjectMessage> contentReleaseMessageQueue,
        IOptionsMonitor<ItemSequenceOptions> options,
        ILogger<InteropObjectSequencePayloadProcessor> logger
    )
    {
        this.contentReleaseMessageQueue = contentReleaseMessageQueue;
        this.options = options;
        this.logger = logger;
    }

    public Task InitializeSequence()
    {
        try
        {
            contentIdSequence = new Subject<Library.Interop.v2.Object>();

            if (options.CurrentValue is { BufferTime: not null, BufferSize: not null })
                contentIdSequence
                    .Buffer(options.CurrentValue.BufferTime.Value, options.CurrentValue.BufferSize.Value)
                    .Subscribe(sequence =>
                    {
                        if (sequence.Count != 0)
                            contentReleaseMessageQueue.Enqueue(new InteropObjectMessage(new Library.Interop.v2.Object
                            {
                                Properties = new Properties
                                {
                                    new(Constants.CONTENT_TYPE, "Content"),
                                    new(Constants.CONTENT_ID, Guid.NewGuid().ToString())
                                }
                            })).GetAwaiter().GetResult();
                    });
            else
                throw new ArgumentException("BufferTime and BufferSize must be set");

            return Task.CompletedTask;
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error initializing item sequence");
            throw; //dont swallow exception because we want to stop the service
        }
    }

    public Task AddElementToSequence(Library.Interop.v2.Object element)
    {
        contentIdSequence?.OnNext(element);

        return Task.CompletedTask;
    }

    public Task DisposeSequence()
    {
        contentIdSequence?.Dispose();

        return Task.CompletedTask;
    }
}