﻿using Amazon;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Service.AmazonIVS.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueGuava.ContentManagement.Service.AmazonIVS;

public static class AmazonIvsIntegrationSetupExtensions
{
    /// <summary> Adds <see cref="IAmazonIvsService"/> and dependencies to the <paramref name="services"/> collection </summary>
    public static IServiceCollection AddAmazonIVSIntegration(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddDefaultAWSOptions(configuration.GetAWSOptions());

        services.Configure<Models.IvsOptions>(configuration.GetSection("S3Bucket"));
        services.Configure<Models.IvsOptions>(configuration.GetSection("IVSOptions"));

        // Configure AWS options with USWest2 region for IVS services
        var awsOptions = configuration.GetAWSOptions();
        //awsOptions.Region = RegionEndpoint.EUCentral1;

        services.AddAWSService<Amazon.IVS.IAmazonIVS>(awsOptions);
        services.AddAWSService<Amazon.Ivschat.IAmazonIvschat>(awsOptions);

        services.AddScoped<IAmazonIvsService, AmazonIvsService>();

        return services;
    }
}