﻿using System;
using System.Net;
using System.Net.WebSockets;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Amazon.IVS;
using Amazon.IVS.Model;
using Amazon.Ivschat;
using Amazon.Ivschat.Model;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.IVS;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Service.AmazonIVS.Models;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.HttpRepository;
using BlueGuava.MessageQueuing;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using System.Threading;
using Websocket.Client;
using Amazon;

namespace BlueGuava.ContentManagement.Service.AmazonIVS.Implementation;

public class AmazonIvsService : IAmazonIvsService
{
    private readonly ILogger<AmazonIvsService> logger;
    private readonly IvsOptions ivsOptions;
    private readonly IAmazonIVS ivsClient;
    private readonly IAmazonIvschat ivsChatClient;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IMessageQueue<DiscussionUpdate> discussionUpdateQueue;
    private readonly IMessageQueue<RelationshipUpdate> relationUpdateQueue;
    private readonly IHttpRepository collectionsHttpRepository;
    private readonly IConfiguration configuration;

    public AmazonIvsService(ILogger<AmazonIvsService> logger,
        IOptionsMonitor<IvsOptions> ivsOptions,
        IAmazonIVS ivsClient,
        IAmazonIvschat ivsChatClient,
        ICorrelationContextAccessor correlationContextAccessor,
        IHttpRepositoryProvider httpRepositoryProvider,
        IMessageQueue<DiscussionUpdate> discussionUpdateQueue,
        IMessageQueue<RelationshipUpdate> relationUpdateQueue,
        IConfiguration configuration)
    {
        this.logger = logger;
        this.ivsOptions = ivsOptions.CurrentValue;
        this.ivsClient = ivsClient;
        this.ivsChatClient = ivsChatClient;
        this.correlationContextAccessor = correlationContextAccessor;
        this.discussionUpdateQueue = discussionUpdateQueue;
        this.relationUpdateQueue = relationUpdateQueue;
        this.configuration = configuration;

        this.collectionsHttpRepository = httpRepositoryProvider.CreateHttpRepository(ServiceNames.Collections);

        var regionName = configuration["AWS:Region:IVS"] ?? "";
        if (!string.IsNullOrWhiteSpace(regionName))
        {
            var region = RegionEndpoint.GetBySystemName(regionName);
            this.ivsClient = new AmazonIVSClient(region);
            this.ivsChatClient = new AmazonIvschatClient(region);
        }
        else
        {
            this.ivsClient = ivsClient;
            this.ivsChatClient = ivsChatClient;
        }

    }
    public async Task<LiveStreamInfo> CreateLiveStream(Guid contentId,
        bool record,
        IVSChannelLatencyMode channelLatencyMode,
        IVSChannelType channelType)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(CreateLiveStream))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

        var pipelineArn = record ? await EnsureRecordingConfiguration() : string.Empty;
        var response = await ivsClient.CreateChannelAsync(new CreateChannelRequest
        {
            Name = contentId.ToString(),
            LatencyMode = ToChannelLatencyMode(channelLatencyMode), // aka: Standard
            Type = ToChannelType(channelType), // forward video as received
            RecordingConfigurationArn = pipelineArn
        });

        return Translate(response.Channel, response.StreamKey);
    }

    public async Task<LiveStreamInfo> TryGetLiveStream(Guid contentId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(TryGetLiveStream))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

        try
        {
            var channel = await FindChannel(contentId.ToString());
            if (channel == null) return null;

            var streamKey = await FindStreamKey(channel.Arn);
            return Translate(channel, streamKey);
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> PutMetadata(string channelArn, string metadata)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(PutMetadata))
                .Log(LogLevel.Debug, "ChannelArn: {ChannelArn}, Metadata: {Metadata}", channelArn, metadata);

        try
        {
            await ivsClient.PutMetadataAsync(new PutMetadataRequest
            {
                ChannelArn = channelArn,
                Metadata = metadata
            });
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> StopLiveStream(string channelArn)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(StopLiveStream))
                .Log(LogLevel.Debug, "ChannelArn: {channelArn}", channelArn);

        var stopRequest = new StopStreamRequest { ChannelArn = channelArn };
        try
        {
            _ = await ivsClient.StopStreamAsync(stopRequest);
        }
        catch (ChannelNotBroadcastingException)
        {
            return true;
        }
        catch (StreamUnavailableException)
        {
            return true;
        }
        catch
        {
            return false;
        }

        return false;
    }

    public async Task RemoveLiveStream(string channelArn)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(RemoveLiveStream))
                .Log(LogLevel.Debug, "ChannelArn: {ChannelArn}", channelArn);

        var delRequest = new DeleteChannelRequest { Arn = channelArn };
        _ = await ivsClient.DeleteChannelAsync(delRequest);
    }


    public async Task<StreamKeyInfo> CreateStreamKey(string channelArn)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(CreateStreamKey))
                .Log(LogLevel.Debug, "ChannelArn: {ChannelArn}", channelArn);

        var request = new CreateStreamKeyRequest { ChannelArn = channelArn };
        var response = await ivsClient.CreateStreamKeyAsync(request);

        return new StreamKeyInfo
        {
            StreamKeyArn = response.StreamKey.Arn,
            StreamKey = response.StreamKey.Value
        };
    }

    public async Task RemoveStreamKeys(string channelArn)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(RemoveStreamKeys))
                .Log(LogLevel.Debug, "ChannelArn: {ChannelArn}", channelArn);

        var keyRequest = new ListStreamKeysRequest { ChannelArn = channelArn };
        while (true)
        {
            var keysResponse = await ivsClient.ListStreamKeysAsync(keyRequest);
            foreach (var keyInfo in keysResponse.StreamKeys)
            {
                var delRequest = new DeleteStreamKeyRequest { Arn = keyInfo.Arn };
                _ = await ivsClient.DeleteStreamKeyAsync(delRequest);
            }

            keyRequest.NextToken = keysResponse.NextToken;
            if (string.IsNullOrEmpty(keysResponse.NextToken)) break;
        }
    }


    public async Task<ChatRoomInfo> CreateChatRoom(Guid contentId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(CreateChatRoom))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

        var response = await ivsChatClient.CreateRoomAsync(new CreateRoomRequest
        {
            Name = contentId.ToString(),
            MaximumMessageLength = 500,
            MaximumMessageRatePerSecond = 10,
            MessageReviewHandler = string.IsNullOrEmpty(ivsOptions.MessageReviewHandler)
                ? null
                : new MessageReviewHandler
                { Uri = ivsOptions.MessageReviewHandler, FallbackResult = FallbackResult.ALLOW }
        });

        return new ChatRoomInfo
        {
            RoomArn = response.Arn
        };
    }

    public async Task<RoomKeyInfo> CreateRoomKey(string roomArn, ClaimsPrincipal user, bool moderator)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(CreateRoomKey))
                .Log(LogLevel.Debug, "RoomArn: {RoomArn}, Moderator: {Moderator}", roomArn, moderator);

        var userId = user.GetCustomerId().ToString();
        var capabilities = new List<string> { "SEND_MESSAGE" };
        if (moderator) capabilities.AddRange(new[] { "DISCONNECT_USER", "DELETE_MESSAGE" });
        var response = await ivsChatClient.CreateChatTokenAsync(new CreateChatTokenRequest
        {
            RoomIdentifier = roomArn,
            Capabilities = capabilities,
            SessionDurationInMinutes = 180,
            UserId = userId,
            Attributes = new Dictionary<string, string>()
            {
                { "username", user.FindFirst(ClaimFields.DisplayName)?.Value ?? userId },
                { "userEmail", user.FindFirst(ClaimFields.EmailAddress)?.Value ?? string.Empty },
                { "avatar", user.FindFirst(ClaimFields.AvatarImageUrl)?.Value ?? string.Empty }
            }
        });

        return new RoomKeyInfo
        {
            Token = response.Token,
            SessionExpiration = response.SessionExpirationTime,
            TokenExpiration = response.TokenExpirationTime
        };
    }

    public async Task RemoveChatRoom(string roomArn)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(AmazonIvsService), nameof(RemoveChatRoom))
                .Log(LogLevel.Debug, "RoomArn: {RoomArn}", roomArn);

        _ = await ivsChatClient.DeleteRoomAsync(new DeleteRoomRequest
        {
            Identifier = roomArn
        });
    }


    private async Task<string> EnsureRecordingConfiguration()
    {
        const string configName = "default-recording";
        if (string.IsNullOrEmpty(ivsOptions.IVSRecording))
            return string.Empty; // no bucket name defined

        var listRequest = new ListRecordingConfigurationsRequest();
        while (true) // enumerate recording cnofigurations & select matching name
        {
            var listResponse = await ivsClient.ListRecordingConfigurationsAsync(listRequest);

            foreach (var item in listResponse.RecordingConfigurations)
                if (item.Name == configName)
                    return item.Arn;

            listRequest.NextToken = listResponse.NextToken;
            if (string.IsNullOrEmpty(listResponse.NextToken)) break;
        }

        var thumbnailInterval = (int)ivsOptions.ThumbnailInterval.TotalSeconds;
        var response = await ivsClient.CreateRecordingConfigurationAsync(new CreateRecordingConfigurationRequest
        {
            Name = configName,
            DestinationConfiguration = new Amazon.IVS.Model.DestinationConfiguration()
            {
                S3 = new Amazon.IVS.Model.S3DestinationConfiguration
                {
                    BucketName = ivsOptions.IVSRecording,
                }
            },
            ThumbnailConfiguration = new ThumbnailConfiguration
            {
                RecordingMode = thumbnailInterval >= 10 ? RecordingMode.INTERVAL : RecordingMode.DISABLED,
                TargetIntervalSeconds = thumbnailInterval >= 10 ? thumbnailInterval : 60
            },
            RenditionConfiguration = new RenditionConfiguration
            {
                Renditions = new List<string>() { "FULL_HD" },
                RenditionSelection = RenditionConfigurationRenditionSelection.CUSTOM
            }
        });

        return response.RecordingConfiguration.Arn;
    }

    private async Task<Channel> FindChannel(string contentId)
    {
        Channel channel = null;
        var listRequest = new ListChannelsRequest { FilterByName = contentId.ToString() };
        while (channel == null)
        {
            var listResponse = await ivsClient.ListChannelsAsync(listRequest);
            var summary = listResponse.Channels.FirstOrDefault();

            listRequest.NextToken = listResponse.NextToken;
            if (summary == null && string.IsNullOrEmpty(listResponse.NextToken)) break;
            if (summary == null) continue; // maybe it's on the next

            var request = new GetChannelRequest { Arn = summary.Arn };
            var response = await ivsClient.GetChannelAsync(request);
            channel = response.Channel;
        }

        return channel;
    }

    private async Task<StreamKey> FindStreamKey(string channelArn)
    {
        StreamKey streamKey = null;
        var listRequest = new ListStreamKeysRequest { ChannelArn = channelArn };
        while (streamKey == null)
        {
            var listResponse = await ivsClient.ListStreamKeysAsync(listRequest);
            var summary = listResponse.StreamKeys.FirstOrDefault();

            listRequest.NextToken = listResponse.NextToken;
            if (summary == null && string.IsNullOrEmpty(listResponse.NextToken)) break;
            if (summary == null) continue; // maybe it's on the next

            var request = new GetStreamKeyRequest { Arn = summary.Arn };
            var response = await ivsClient.GetStreamKeyAsync(request);
            streamKey = response.StreamKey;
        }

        return streamKey;
    }


    private static LiveStreamInfo Translate(Channel channel, StreamKey streamKey)
    {
        return new LiveStreamInfo()
        {
            ContentId = Guid.Parse(channel.Name),
            ChannelArn = channel.Arn,
            PlaybackUrl = channel.PlaybackUrl,
            IngestEndpoint = channel.IngestEndpoint,
            StreamKeyArn = streamKey?.Arn,
            StreamKey = streamKey?.Value
        };
    }

    private static ChannelLatencyMode ToChannelLatencyMode(IVSChannelLatencyMode mode)
    {
        return mode switch
        {
            IVSChannelLatencyMode.NORMAL => ChannelLatencyMode.NORMAL,
            IVSChannelLatencyMode.LOW => ChannelLatencyMode.LOW,
            _ => ChannelLatencyMode.NORMAL
        };
    }

    private static ChannelType ToChannelType(IVSChannelType mode)
    {
        return mode switch
        {
            IVSChannelType.BASIC => ChannelType.BASIC,
            IVSChannelType.STANDARD => ChannelType.STANDARD,
            IVSChannelType.ADVANCED_SD => ChannelType.ADVANCED_SD,
            IVSChannelType.ADVANCED_HD => ChannelType.ADVANCED_HD,
            _ => ChannelType.BASIC
        };
    }

    public async Task SendMessage(string roomArn, ClaimsPrincipal user, string? message = null)
    {
        var moderator = user.GetCustomerId();
        var roomKeyInfo = await CreateRoomKey(roomArn, user, true);
        var socket = RetrieveSocketUri();

        var payload = new Models.WebsocketCommand
        {
            Action = IvsActions.SEND_MESSAGE.ToString(),
            Attributes = new Attributes()
            {
                user_id = moderator.ToString(),
                display_name = user.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Name)?.Value,
            },
            Content = message ?? string.Empty,
            RequestId = Guid.NewGuid().ToString(),
        };

        try
        {
            var exitEvent = new ManualResetEvent(false);
            var url = new Uri(socket);

            var factory = new Func<ClientWebSocket>(() =>
            {
                var client = new ClientWebSocket
                {
                    Options =
                    {
                            KeepAliveInterval = TimeSpan.FromSeconds(5),
                        // Proxy = ...
                        // ClientCertificates = ...
                    }
                };
                client.Options.AddSubProtocol(roomKeyInfo.Token);
                return client;
            });

            using (var client = new WebsocketClient(url, factory))
            {
                client.DisconnectionHappened.Subscribe(info =>
                {
                    logger.LogInformation("Disconnection happened, type: {@Info}" + info);
                    exitEvent.Set();
                });

                client.Start();
                client.Send(payload.ToJson());

                //because we need to wait for the client to sent the message
                await Task.Delay(500);
                client.Stop(WebSocketCloseStatus.NormalClosure, "Stop called");
                exitEvent.WaitOne();
            }
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ERROR: " + ex.Message);
        }
    }

    public string RetrieveSocketUri()
    {
        return $"wss://edge.ivschat.{ivsClient.Config.RegionEndpoint.SystemName}.amazonaws.com";
    }

    public async Task RemoveMessage(string roomArn, string messageId, string contentId, string ipAddress, string? reason = null)
    {
        //TODO: get discussion record
        var url = $"/Discussion/List/Content/{contentId}?threadId=Discussion:{messageId}";
        var discussionResult = await collectionsHttpRepository.RetrieveAsync<CollectionPagedResult<DiscussionEntryResponse>>(url);
        var discussion = discussionResult?.PageContent.FirstOrDefault();

        logger.LogInformation("RemoveMessage: Discussion API called: {Url}", url);

        if (discussion == null)
        {
            logger.LogWarning("RemoveMessage: Failed to find discussion record for message {MessageId}", messageId);
            //tODO: throw 404?
            return;
        }
        logger.LogInformation("RemoveMessage: Discussion found: {@Discussion}", discussion);

        discussion!.ModerationTags ??= new List<string>();
        discussion!.ModerationTags.Add(reason ?? "Deleted by moderator");

        var message = new DeleteMessageRequest()
        {
            RoomIdentifier = roomArn,
            Reason = reason,
            Id = messageId,
        };

        logger.LogInformation("RemoveMessage: Delete IVS Chat: {@Message}", message);
        var response = await ivsChatClient.DeleteMessageAsync(message);


        //error handling https://docs.aws.amazon.com/ivs/latest/ChatAPIReference/API_DeleteMessage.html
        if (response.HttpStatusCode != HttpStatusCode.OK)
            throw new ArgumentException("RemoveMessage: Failed to delete message {messageId} in room {roomArn} with reason {reason} with status code {response.HttpStatusCode}");

        var discussionDeleteMessage = new DiscussionUpdate
        {
            CustomerId = discussion.CustomerId,
            ContentId = discussion.ContentId,
            Comment = discussion.Payload,
            CreatedDate = discussion.CreatedDate,
            MessageId = discussion.MessageId,
            Allowed = false,
            ModerationTags = discussion.ModerationTags,
            Id = discussion.Id,
            ParentId = discussion.ParentId,
            Type = "text",
            CustomerIp = ipAddress,
            Sender = configuration["ModuleInfo:Name"] ?? "Unknown"
        };
        _ = discussionUpdateQueue.Enqueue(discussionDeleteMessage);

        logger.LogInformation("RemoveMessage: Delete message completed! Message sent to collection Service: {@DiscussionDeleteMessage}", discussionDeleteMessage);
    }

    public async Task BanUser(string roomArn, string userId, string? reason = null)
    {
        var response = await ivsChatClient.DisconnectUserAsync(new DisconnectUserRequest()
        {
            RoomIdentifier = roomArn,
            UserId = userId,
            Reason = reason,
        });

        //error handling https://docs.aws.amazon.com/ivs/latest/ChatAPIReference/API_DeleteMessage.html
        if (response.HttpStatusCode != HttpStatusCode.OK)
            throw new ArgumentException($"BanUser: Failed to ban user {userId} in room {roomArn} with reason {reason} with status code {response.HttpStatusCode}");

        var room = await ivsChatClient.GetRoomAsync(new GetRoomRequest()
        {
            Identifier = roomArn,
        });

        _ = relationUpdateQueue.Enqueue(new SingleRelationshipUpdate
        {
            Action = ActionKind.Add,
            Relation = CustomerRelation.BanDiscussion,
            SourceId = userId,
            TargetId = room.Name,//this is the contentId
        });

        _ = relationUpdateQueue.Enqueue(new RelationshipRefresh()
        {
            Relation = CustomerRelation.BanDiscussion,
            SourceId = userId,
        });
    }


    public static class ChatMessage
    {
        public static List<string> Values { get; set; } = new List<string>();
    }
}