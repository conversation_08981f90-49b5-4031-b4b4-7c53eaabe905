using System.ComponentModel.DataAnnotations;
namespace BlueGuava.ContentManagement.Service.AmazonIVS.Models;

public class IvsBanRequest : IvsRequest
{
    /// <summary>
    /// The id of the user to ban
    /// </summary>
    [Required(ErrorMessage = Errors.ErrorChatBanUseridBadRequest)]
    public string? CustomerId { get; set; }

    /// <summary>
    /// The reason for the ban
    /// </summary>
    public string? Reason { get; set; }
}
