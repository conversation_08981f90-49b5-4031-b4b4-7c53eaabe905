using System.ComponentModel.DataAnnotations;

namespace BlueGuava.ContentManagement.Service.AmazonIVS.Models;

public class IvsDeleteMessageRequest : IvsRequest
{
    /// <summary>
    /// The id of the message to delete
    /// </summary>
    [Required(ErrorMessage = Errors.ErrorChatDeleteMessageMessageIdBadRequest)]
    public string? MessageId { get; set; }

    /*
      /// <summary>
      /// The id of the content
      /// </summary>
      [Required(ErrorMessage = Errors.ErrorChatDeleteMessageContentIdBadRequest)]
      public string? ContentId { get; set; }
     */
    /// <summary>
    /// The reason for the ban
    /// </summary>
    public string? Reason { get; set; }
}