namespace BlueGuava.ContentManagement.Service.AmazonIVS.Models;

public static class Errors
{
    public const string ErrorChatArnBadRequest = "ERROR_CHAT_ARN_BADREQUEST";
    public const string ErrorChatSendMessageBadRequest = "ERROR_CHAT_SENDMESSAGE_BADREQUEST";
    public const string ErrorChatBanUseridBadRequest = "ERROR_CHAT_BAN_USERID_BADREQUEST";
    public const string ErrorChatDeleteMessageMessageIdBadRequest = "BADREQUEST";
    public const string ErrorChatBanBadRequest = "BADREQUEST";
    public const string? ErrorChatDeleteMessageContentIdBadRequest = "ERROR_CHAT_DELETEMESSAGE_CONTENTID_BADREQUEST";
    public const string? ErrorChatDeleteMessageUserIdBadRequest = "ERROR_CHAT_DELETEMESSAGE_USERID_BADREQUEST";
}