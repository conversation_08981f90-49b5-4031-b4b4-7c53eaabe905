using System;
using System.Collections.Generic;
namespace BlueGuava.ContentManagement.Service.AmazonIVS.Models;

public class DiscussionEntryResponse
{
    public string Id { get; set; }

    public string CustomerId { get; set; }
    public string ProviderId { get; set; }
    public string ContentId { get; set; }
    public DateTime CreatedDate { get; set; }

    public string Payload { get; set; }
    public string ParentId { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public string Deleted { get; set; }

    public List<DiscussionEntryResponse> Children { get; set; }

    /// <summary>
    /// IVS message id for virtual relations (LIKE, etc)
    /// </summary>
    public string? MessageId { get; set; }

    public int Position { get; set; }

    public bool? Allowed { get; set; }
    public List<string> ModerationTags { get; set; } = new List<string>();
}