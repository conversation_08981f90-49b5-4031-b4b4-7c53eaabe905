namespace BlueGuava.ContentManagement.Service.AmazonIVS.Models;

public enum IvsActions
{
    DELETE_MESSAGE = 0,
    DISCONNECT_USER = 1,
    SEND_MESSAGE = 2,
}

public class WebsocketCommand
{
    public string? Action { get; set; }
    public Attributes? Attributes { get; set; }
    public string? Content { get; set; }
    public string? RequestId { get; set; }
}

public class Attributes
{

    public string? user_id { get; set; }
    public string? display_name { get; set; }
}