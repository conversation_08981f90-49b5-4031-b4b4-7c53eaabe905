﻿using System;
using System.Collections.Generic;
using BlueGuava.MessageQueuing;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.Service.AmazonIVS.EventBridge;

public class IvsCallbackEvent : IQueueItem
{
    [JsonProperty("id")] public string Id { get; set; }
    [JsonProperty("time")] public DateTime EventTime { get; set; }

    [JsonProperty("resources")] public List<string> Resources { get; set; }

    [JsonProperty("detail-type")] public string DetailType { get; set; }
    [JsonProperty("detail")] public IvsEventDetail Detail { get; set; }

    string IQueueItem.Receipt { get; set; }

    string IQueueItem.GroupId
    {
        get => Detail?.ChannelName;
        set => _ = value;
    }

    string IQueueItem.DeduplicationId
    {
        get => Id;
        set => _ = value;
    }

    public string? Sender { get; set; }
}