﻿using System.Collections.Generic;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.Service.AmazonIVS.EventBridge;

public class IvsEventDetail
{
    [JsonProperty("event_name")] public string EventName { get; set; }

    [JsonProperty("channel_name")] public string ChannelName { get; set; }
    [JsonProperty("stream_id")] public string StreamId { get; set; }

    [JsonProperty("recording_status")]
    public string Status
    {
        get => EventName;
        set => EventName = value;
    }

    [JsonProperty("recording_status_reason")]
    public string StatusReason { get; set; }

    [JsonProperty("recording_s3_bucket_name")]
    public string S3BucketName { get; set; }

    [JsonProperty("recording_s3_key_prefix")]
    public string S3KeyPrefix { get; set; }

    [JsonProperty("recording_duration_ms")]
    public string DurationMillis { get; set; }

    [JsonProperty("recording_session_id")] public string SessionId { get; set; }

    [JsonProperty("recording_session_stream_ids")]
    public List<string> SessionStreamIds { get; set; }
}