﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.MediaLive;
using Amazon.MediaLive.Model;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Service.MediaLive.Implementation;

public class MediaLiveIntegration : IMediaLiveIntegration
{
    private readonly ILogger<MediaLiveIntegration> logger;
    private readonly MediaLiveOptions options;
    private readonly IAmazonMediaLive mediaLiveClient;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public MediaLiveIntegration(
        ILogger<MediaLiveIntegration> logger,
        IOptionsMonitor<MediaLiveOptions> options,
        IAmazonMediaLive mediaLiveClient,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.logger = logger;
        this.options = options.CurrentValue;
        this.mediaLiveClient = mediaLiveClient;
        this.correlationContextAccessor = correlationContextAccessor;
        this.options.Destination = this.options.Destination.TrimEnd('/');
        this.options.DistributionUrl = this.options.DistributionUrl.TrimEnd('/');
    }


    public async Task<string> CreateChannel(Guid contentId, string objectUrl)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(MediaLiveIntegration), nameof(CreateChannel))
                .Log(LogLevel.Debug, "ContentId: {ContentId}, ObjectUrl: {ObjectUrl}", contentId, objectUrl);

        var preroll = await CreateInput(options.PrerollFile, $"pre_{contentId}");
        var input = await CreateInput(objectUrl, contentId.ToString());
        var channel = await CreateChannel(contentId.ToString(), preroll, input);

        return channel.Id;
    }

    public async Task<string> StartStreaming(Guid contentId, DateTime startDate)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(MediaLiveIntegration), nameof(StartStreaming))
                .Log(LogLevel.Debug, "ContentId: {ContentId}, StartDate: {StartDate}", contentId, startDate);

        var channel = await FindChannel(contentId.ToString());
        if (channel == null) throw new InvalidOperationException("Channel not found");

        if (channel.State == ChannelState.CREATING) return null;
        VerifyChannelState(channel.State, ChannelState.IDLE);
        await AddSchedule(channel.Id, startDate, contentId.ToString());

        var request = new StartChannelRequest { ChannelId = channel.Id };
        var response = await mediaLiveClient.StartChannelAsync(request);

        return $"{options.DistributionUrl}/{contentId}/{contentId}.m3u8";
    }

    public async Task<string> StopStreaming(Guid contentId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(MediaLiveIntegration), nameof(StopStreaming))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

        var channel = await FindChannel(contentId.ToString());
        if (channel == null) return ChannelState.DELETED;
        VerifyChannelState(channel.State, ChannelState.RUNNING, ChannelState.IDLE,
            ChannelState.STOPPING, ChannelState.DELETING, ChannelState.DELETED);

        if (channel.State != ChannelState.RUNNING) return channel.State;
        var request = new StopChannelRequest { ChannelId = channel.Id };
        var response = await mediaLiveClient.StopChannelAsync(request);

        return response.State;
    }

    public async Task<bool> DeleteChannel(Guid contentId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(MediaLiveIntegration), nameof(DeleteChannel))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

        var channel = await FindChannel(contentId.ToString());
        if (channel != null && channel.State != ChannelState.DELETED)
        {
            if (channel.State == ChannelState.DELETING) return false;
            if (channel.State == ChannelState.STOPPING) return false;
            VerifyChannelState(channel.State, ChannelState.IDLE);

            var request = new DeleteChannelRequest { ChannelId = channel.Id };
            var response = await mediaLiveClient.DeleteChannelAsync(request);
            if (response.State != ChannelState.DELETED) return false;
        }

        var retry = false;
        var inputList = EnumerateInputs(contentId.ToString());
        await foreach (var input in inputList)
        {
            var request = new DeleteInputRequest { InputId = input.Id };
            try
            {
                await mediaLiveClient.DeleteInputAsync(request);
            }
            catch
            {
                retry = true;
            }
        }

        return !retry;
    }


    private async Task<Input> CreateInput(string url, string name)
    {
        return (await mediaLiveClient.CreateInputAsync(new CreateInputRequest
        {
            Name = name,
            Type = InputType.MP4_FILE,
            Sources =
            {
                new InputSourceRequest
                {
                    Url = url
                }
            }
        })).Input;
    }

    private Task<Channel> CreateChannel(string contentId, Input preroll, Input input)
    {
        return CreateChannel(contentId, preroll, input, new[]
        {
            new VideoDescription
            {
                Width = 1920,
                Height = 1080,
                Name = "1080p"
            },
            new VideoDescription
            {
                Width = 1280,
                Height = 720,
                Name = "720p"
            },
            new VideoDescription
            {
                Width = 854,
                Height = 480,
                Name = "480p"
            }
        });
    }
    //TO-DO
    //End of support notice: On November 13, 2025, AWS will discontinue AWS Elemental MediaStore. After November 13, 2025, you will no longer be able to access the MediaStore console or MediaStore containers. For more information, visit
    private async Task<Channel> CreateChannel(string contentId, Input preroll, Input input,
        VideoDescription[] resolutions)
    {
        if (resolutions == null || resolutions.Length == 0)
            throw new ArgumentException("No video resolutions specified", nameof(resolutions));

        var request = new CreateChannelRequest
        {
            RoleArn = options.RoleArn,
            ChannelClass = ChannelClass.SINGLE_PIPELINE,
            Name = contentId,
            InputAttachments =
            {
                new InputAttachment
                {
                    InputAttachmentName = preroll.Name,
                    InputId = preroll.Id,
                    InputSettings = new InputSettings
                    {
                        SourceEndBehavior = InputSourceEndBehavior.LOOP
                    }
                },
                new InputAttachment
                {
                    InputAttachmentName = input.Name,
                    InputId = input.Id,
                    InputSettings = new InputSettings
                    {
                        SourceEndBehavior = InputSourceEndBehavior.CONTINUE
                    }
                }
            },
            Destinations =
            {
                new OutputDestination
                {
                    Id = "dest",
                    Settings =
                    {
                        new OutputDestinationSettings
                        {
                            Url = options.Destination + $"/{contentId}/{contentId}"
                        }
                    }
                }
            },
            EncoderSettings = new EncoderSettings
            {
                OutputGroups =
                {
                    new OutputGroup
                    {
                        Name = "out",
                        OutputGroupSettings = new OutputGroupSettings
                        {
                            HlsGroupSettings = new HlsGroupSettings
                            {
                                HlsCdnSettings = new HlsCdnSettings
                                {
                                    HlsMediaStoreSettings = new HlsMediaStoreSettings
                                    {
                                        MediaStoreStorageClass = HlsMediaStoreStorageClass.TEMPORAL
                                    }
                                },
                                Destination = new OutputLocationRef
                                {
                                    DestinationRefId = "dest"
                                }
                            }
                        }
                    }
                },
                TimecodeConfig = new TimecodeConfig
                {
                    Source = TimecodeConfigSource.EMBEDDED
                },
                AudioDescriptions =
                {
                    new AudioDescription
                    {
                        Name = "audio",
                        AudioTypeControl = AudioDescriptionAudioTypeControl.FOLLOW_INPUT,
                        LanguageCodeControl = AudioDescriptionLanguageCodeControl.FOLLOW_INPUT
                    }
                },
                VideoDescriptions = resolutions.ToList()
            }
        };

        var outputGroup = request.EncoderSettings.OutputGroups[0];
        foreach (var video in request.EncoderSettings.VideoDescriptions)
            outputGroup.Outputs.Add(new Output
            {
                AudioDescriptionNames = { "audio" },
                VideoDescriptionName = video.Name,
                OutputName = video.Name,
                OutputSettings = new OutputSettings
                {
                    HlsOutputSettings = new HlsOutputSettings
                    {
                        NameModifier = video.Name.PadLeft(6, '_'),
                        HlsSettings = new HlsSettings
                        {
                            StandardHlsSettings = new StandardHlsSettings
                            {
                                AudioRenditionSets = "audio",
                                M3u8Settings = new M3u8Settings { }
                            }
                        }
                    }
                }
            });

        var response = await mediaLiveClient.CreateChannelAsync(request);
        return response.Channel;
    }

    private async Task AddSchedule(string channelId, DateTime startDate, string inputName)
    {
        await mediaLiveClient.BatchUpdateScheduleAsync(new BatchUpdateScheduleRequest
        {
            ChannelId = channelId,
            Creates = new BatchScheduleActionCreateRequest
            {
                ScheduleActions =
                {
                    new ScheduleAction
                    {
                        ActionName = "go_live",
                        ScheduleActionStartSettings = new ScheduleActionStartSettings
                        {
                            FixedModeScheduleActionStartSettings = new FixedModeScheduleActionStartSettings
                            {
                                Time = startDate.ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ss'.'fff'Z'")
                            }
                        },
                        ScheduleActionSettings = new ScheduleActionSettings
                        {
                            InputSwitchSettings = new InputSwitchScheduleActionSettings
                            {
                                InputAttachmentNameReference = inputName
                            }
                        }
                    }
                }
            }
        });
    }

    private async Task<ChannelSummary> FindChannel(string contentId)
    {
        var request = new ListChannelsRequest { };
        while (request != null)
        {
            var response = await mediaLiveClient.ListChannelsAsync(request);
            var channel = response.Channels.SingleOrDefault(c => c.Name == contentId);
            if (channel != null) return channel;

            request.NextToken = response.NextToken;
            if (string.IsNullOrEmpty(response.NextToken))
                return null; // enumeration finished
        }

        // these lines should never execute
        throw new InvalidOperationException("Inconclusive");
    }

    private async IAsyncEnumerable<Input> EnumerateInputs(string suffix)
    {
        var request = new ListInputsRequest { MaxResults = 100 };
        while (request != null)
        {
            var response = await mediaLiveClient.ListInputsAsync(request);
            foreach (var input in response.Inputs.Where(i => i.Name.EndsWith(suffix)))
                yield return input;

            request.NextToken = response.NextToken;
            if (string.IsNullOrEmpty(response.NextToken))
                request = null; // break;
        }
    }

    private static void VerifyChannelState(ChannelState actual, params ChannelState[] expected)
    {
        if (expected.Contains(actual)) return;
        throw new InvalidOperationException($"The channel is in the {actual} state");
    }
}