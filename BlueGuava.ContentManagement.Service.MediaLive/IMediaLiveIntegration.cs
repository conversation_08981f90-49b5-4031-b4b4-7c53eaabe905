﻿using System;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Service.MediaLive;

public interface IMediaLiveIntegration
{
    /// <summary>
    /// Creates an AWS Elemental MediaLive channel for the specified <paramref name="contentId"/>
    /// with a configured preroll loop, and <paramref name="objectUrl"/> as the scheduled input
    /// </summary>
    /// <returns> The identifier of the AWS Elemental MediaLive channel </returns>
    Task<string> CreateChannel(Guid contentId, string objectUrl);

    /// <summary>
    /// Starts streaming the AWS Elemental MediaLive channel for <paramref name="contentId"/>
    /// looping prereoll until <paramref name="startDate"/>, when the sceduled input starts
    /// </summary>
    /// <returns> The url of the stream or <see langword="null"/> if the channel is not ready yet </returns>
    Task<string> StartStreaming(Guid contentId, DateTime startDate);

    /// <summary> Stops the stream on the AWS Elemental MediaLive channel for <paramref name="contentId"/> </summary>
    /// <returns> The textual representation of the status of the channel </returns>
    Task<string> StopStreaming(Guid contentId);

    /// <summary> Deletes the AWS Elemental MediaLive channel for <paramref name="contentId"/> </summary>
    /// <returns> <see langword="true"/> if the channel and inputs were deleted; otherwise <see langword="false"/> </returns>
    /// <exception cref="InvalidOperationException"> if the channel is running or is in a failed state </exception>
    Task<bool> DeleteChannel(Guid contentId);
}