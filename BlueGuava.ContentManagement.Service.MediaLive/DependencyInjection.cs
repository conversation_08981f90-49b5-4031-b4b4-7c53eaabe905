﻿using Amazon.MediaLive;
using BlueGuava.ContentManagement.Service.MediaLive.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace BlueGuava.ContentManagement.Service.MediaLive;

public static class DependencyInjection
{
    public static IServiceCollection AddMediaLiveIntegration(this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddAWSService<IAmazonMediaLive>();
        services.AddScoped<IMediaLiveIntegration, MediaLiveIntegration>();
        services.Configure<MediaLiveOptions>(configuration.GetSection("MediaLive"));
        return services;
    }
}