namespace BlueGuava.ContentManagement.IntegrationTests.Models;

public class PagedResult<TData>
{
    public PagedResult(IReadOnlyCollection<TData> data, string token)
    {
        PageContent = data;
        PagingToken = token;
    }

    public IEnumerable<TData> PageContent { get; set; }

    public string PagingToken { get; set; }

    public bool Finished => string.IsNullOrEmpty(PagingToken);
}