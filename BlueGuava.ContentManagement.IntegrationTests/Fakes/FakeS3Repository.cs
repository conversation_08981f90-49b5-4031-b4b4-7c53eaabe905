using System.Net;
using Amazon.S3;
using Amazon.S3.Model;
using BlueGuava.Extensions.AWS.Repositories.S3.Entities;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.IntegrationTests.Fakes;

public class FakeS3Repository : IS3Repository
{
    private readonly ILogger<FakeS3Repository> logger;

    public FakeS3Repository(ILogger<FakeS3Repository> logger)
    {
        this.logger = logger;
    }

    public Task PutFileAsync<T>(T payload, string bucketName, string fileKey, string typeTag, S3CannedACL? acl = null,
        Dictionary<string, string>? tags = null, Dictionary<string, object>? headers = null) where T : class
    {
        throw new NotImplementedException();
    }

    public Task PutFileAsync(string payload, string bucketName, string fileKey, string typeTag, S3CannedACL? acl = null,
        Dictionary<string, string>? tags = null, Dictionary<string, object>? headers = null)
    {
        return Task.CompletedTask;
    }

    public Task<T?> RetrieveAsync<T>(string bucketName, string key, CancellationToken token = new()) where T : class
    {
        throw new NotImplementedException();
    }

    public Task<string?> RetrieveAsyncAsString(string bucketName, string key, CancellationToken token = new())
    {
        throw new NotImplementedException();
    }

    public Task<Stream?> RetrieveAsync(string bucketName, string key, CancellationToken token = new())
    {
        throw new NotImplementedException();
    }

    public Task<T> RetrieveAsyncFromXml<T>(string bucketName, string key, CancellationToken token = new())
        where T : class
    {
        throw new NotImplementedException();
    }

    public string GeneratePreSignedUrl(double duration, string bucketName, string fileKey)
    {
        throw new NotImplementedException();
    }

    public string GeneratePreSignedUrl(double duration, string bucketName, string fileKey, ResponseHeaderOverrides responseHeaderOverrides)
    {
        throw new NotImplementedException();
    }
    public Task<HttpStatusCode> PutTagAsync(string bucketName, string fileKey, Dictionary<string, string> tags)
    {
        throw new NotImplementedException();
    }

    public Task<HttpStatusCode> RemoveTagAsync(string bucketName, string fileKey, Dictionary<string, string> tags)
    {
        throw new NotImplementedException();
    }

    public Task<(string Id, long Size)> GetFileSize(string bucketName, string fileKey, string id)
    {
        return Task.FromResult((id, 1000L));
    }

    public Task CopyFileAsync(string sourceBucketName, string sourceFileKey, string destBucketName, string destFileKey,
        string contentType)
    {
        throw new NotImplementedException();
    }

    public Task<bool> ExistsAsync(string bucketName, string fileKey)
    {
        throw new NotImplementedException();
    }

    public Task<string?> SearchAsync(string bucketName, string bucketPrefix, string partialFileKey)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<string>?> SearchAsync(string bucketName, string bucketPrefix)
    {
        throw new NotImplementedException();
    }

    public string GetCurrentRegion()
    {
        throw new NotImplementedException();
    }

    public Task RemoveFileAsync(string bucketName, string fileKey)
    {
        return Task.CompletedTask;
    }

    public Task<S3RestoreObjectResponse> RestoreObjectAsync(int duration, GlacierJobTier tier, string? bucketName,
        string? fileKey)
    {
        throw new NotImplementedException();
    }
    public Task PutFileAsync(byte[] payload, string bucketName, string fileKey, string typeTag, S3CannedACL? acl = null, Dictionary<string, string>? tags = null, Dictionary<string, object>? headers = null)
    {
        throw new NotImplementedException();
    }
}