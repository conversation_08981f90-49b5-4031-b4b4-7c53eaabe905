using System.Net;
using BlueGuava.ContentManagement.Api;
using BlueGuava.ContentManagement.IntegrationTests.Infrastructure;
using BlueGuava.Extensions.AspNetCore.VersionController;
using Microsoft.AspNetCore.Mvc.Testing;
using Newtonsoft.Json;
using Xunit;

namespace BlueGuava.ContentManagement.IntegrationTests.Features;

[Collection("Contents Database collection")]
public class BasicTests
    : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly HttpClient client;

    public BasicTests(CustomWebApplicationFactory<Program> factory)
    {
        // Arrange
        client = factory.CreateClient(new WebApplicationFactoryClientOptions
        {
            AllowAutoRedirect = false
        });
    }

    [Theory]
    [InlineData("/swagger/v1/swagger.json", HttpStatusCode.NotFound)]
    [InlineData("/swagger/v2.0/swagger.json", HttpStatusCode.OK)]
    [InlineData("/swagger/v2.1/swagger.json", HttpStatusCode.OK)]
    [InlineData("/swagger/v3.0/swagger.json", HttpStatusCode.OK)]
    [InlineData("/swagger/v3.1/swagger.json", HttpStatusCode.OK)]
    [InlineData("/swagger/v3.2/swagger.json", HttpStatusCode.OK)]
    public async Task Get_Swagger(string url, HttpStatusCode expectedStatusCode)
    {
        // Act
        var response = await client.GetAsync(url);
        Assert.Equal(expectedStatusCode, response.StatusCode);
    }

    [Theory]
    [InlineData("/version")]
    public async Task Get_Version(string url)
    {
        // Act
        var response = await client.GetAsync(url);
        response.EnsureSuccessStatusCode(); // Status Code 200-299

        var resp = await response.Content.ReadAsStringAsync();
        var versionInfo = JsonConvert.DeserializeObject<VersionInformation>(resp);
        Assert.NotNull(versionInfo);
        Assert.NotNull(versionInfo.ServiceName);
        Assert.NotNull(versionInfo.VersionNumber);
    }

    [Theory]
    [InlineData("/metrics")]
    public async Task Get_Metrics(string url)
    {
        // Act
        var response = await client.GetAsync(url);
        response.EnsureSuccessStatusCode(); // Status Code 200-299

        var resp = await response.Content.ReadAsStringAsync();
        Assert.NotNull(resp);
    }
}