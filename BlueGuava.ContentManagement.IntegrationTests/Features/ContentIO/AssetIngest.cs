using System.Net;
using System.Security.Claims;
using System.Text;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using BlueGuava.MessageQueuing;
using BlueGuava.MessageQueuing.FakeQueue;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Xunit;

namespace BlueGuava.ContentManagement.IntegrationTests.Features.ContentIO;

[Trait("ContentIO", "AssetIngest")]
public partial class TestBase
{
    /// <summary>
    /// THINAIR-2702: UploaderUserId should be set to the user id of the user who uploaded the asset
    /// </summary>
    /// <param name="version"></param>
    /// <param name="expectedStatusCode"></param>
    [Theory]
    [InlineData("3.4", HttpStatusCode.Created)]
    [InlineData("3.3", HttpStatusCode.Created)]
    [InlineData("3.2", HttpStatusCode.Created)]
    [InlineData("3.1", HttpStatusCode.Created)]
    [InlineData("3.0", HttpStatusCode.Created)]
    [InlineData("2.1", HttpStatusCode.Created)]
    [InlineData("2.0", HttpStatusCode.Created)]
    public async Task THINAIR_2702_UploaderUserId(string version, HttpStatusCode expectedStatusCode)
    {
        // Arrange

        var itemService = factory.Services.GetService<IContentService>()!;
        var contentId = Guid.NewGuid();
        var someOtherUserId = Guid.NewGuid();
        var locale = "en-US";

        // Create a content item in the database
        await itemService.Create(new Content
        {
            Id = contentId,
            Type = ContentType.Video,
            OwnerId = someOtherUserId, // this will be used by the asset fixer method
            Assets = new List<Asset>
                {
                    // create a new asset without UploaderUserId
                    new()
                    {
                        Id = Guid.NewGuid(),
                        Type = AssetType.Video,
                        SubType = SubType.Original,
                        WorkflowStatus = WorkflowStatus.Succeeded,
                        PublicUrl = "https://cdn.INTEGRATION.ent360.blue/GUID/GUID.mp4",
                        ObjectUrl = "https://ent-contents-INTEGRATION-brand.s3.amazonaws.com/GUID/GUID.mp4",
                        Locale = locale,
                        Duration = 0,
                        IsPublic = true,
                        FileName = "GUID.mp4",
                        UploaderUserId = null
                    }
                }
        },
            new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "Some other User"),
                new Claim(ClaimTypes.NameIdentifier, $"{someOtherUserId}"),
                new Claim(ClaimTypes.Email, "<EMAIL>"),
                new Claim(ClaimTypes.Role, "TechnicalUser")
            })));

        // create fake jwt token with claims
        var claims = new Dictionary<string, object>
        {
            { ClaimTypes.Name, "Some other User" },
            { ClaimTypes.NameIdentifier, $"{Guid.NewGuid()}" },
            { ClaimTypes.Email, "<EMAIL>" },
            { ClaimTypes.Role, "TechnicalUser" }
        };
        client.SetFakeBearerToken(claims);

        // Act

        //fill the mandatory fields
        var request = new IngestAssetRequestV1
        {
            ContentId = contentId,
            OriginalFileName = "GUID.png",
            ObjectUrl = "https://ent-contents-INTEGRATION-brand.s3.amazonaws.com/GUID/GUID.png",
            IsPublic = true,
            PublicUrl = "https://cdn.INTEGRATION.ent360.blue/GUID/GUID.png",
            Type = ContentType.Image,
            Locale = locale
        };

        var httpContent = new StringContent(request.ToJson()!, Encoding.UTF8, "application/json");
        var response = await client.PostAsync($"api/v{version}/Content/IngestAsset", httpContent);

        // Assert

        Assert.Equal(expectedStatusCode, response.StatusCode);

        var contentResponse =
            JsonConvert.DeserializeObject<ContentResponse>(await response.Content.ReadAsStringAsync());

        //assert HTTP response
        Assert.NotNull(contentResponse);
        //test the fixer method so no asset is missing UploaderUserId
        Assert.False(contentResponse.Assets?.Any(x => string.IsNullOrEmpty(x.UploaderUserId)));
        // test the UploaderUserId is set to the owner of one of the asset item
        Assert.True(contentResponse.Assets?.Any(x => x.UploaderUserId == someOtherUserId.ToString()));
        // test the UploaderUserId is not set to the owner of one of the asset item
        Assert.True(contentResponse.Assets?.Any(x => x.UploaderUserId != someOtherUserId.ToString()));

        var item = await itemService.Retrieve(contentId);

        Assert.NotNull(item);
        Assert.Equal(item.Assets?.Count, 2);
        //test the fixer method so no asset is missing UploaderUserId
        Assert.False(item.Assets?.Any(x => string.IsNullOrEmpty(x.UploaderUserId)));
        // test the UploaderUserId is set to the owner of one of the asset item
        Assert.True(item.Assets?.Any(x => x.UploaderUserId == someOtherUserId.ToString()));
        // test the UploaderUserId is not set to the owner of one of the asset item
        Assert.True(item.Assets?.Any(x => x.UploaderUserId != someOtherUserId.ToString()));


        ClearQueues();
    }

    [Fact]
    public void ClearQueues()
    {
        var type = typeof(IQueueItem);
        var types = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(s => s.GetTypes())
            .Where(p => type.IsAssignableFrom(p));

        var method = typeof(TestBase).GetMethod("ClearQueue");
        foreach (var t in types)
        {
            var genericMethod = method.MakeGenericMethod(t);
            genericMethod.Invoke(this, null); // No target, no arguments
        }
    }

    [Fact]
    public void ClearQueue<T>() where T : class, IQueueItem
    {
        var fakeQueue = factory.Services.GetService<IMessageQueue<T>>()! as FakeMessageQueue<T>;
        fakeQueue?.Reset();
    }
}