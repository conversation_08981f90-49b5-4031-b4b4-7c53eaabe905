using BlueGuava.ContentManagement.Api;
using BlueGuava.ContentManagement.Api.Models;
using BlueGuava.ContentManagement.IntegrationTests.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Xunit;

namespace BlueGuava.ContentManagement.IntegrationTests.Features.ContentIO;

[Collection("Contents Database collection")]
public partial class TestBase
    : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly CustomWebApplicationFactory<Program> factory;
    protected readonly HttpClient client;

    private readonly IConfiguration configuration;
    private readonly IOptionsMonitor<AuthenticationSettings> authOptions;

    public TestBase(CustomWebApplicationFactory<Program> factory)
    {
        this.factory = factory;
        // Arrange
        client = CreateHttpClient();

        configuration = factory.Services.GetService<IConfiguration>()!;

        authOptions = factory.Services.GetService<IOptionsMonitor<AuthenticationSettings>>()!;
    }

    private HttpClient CreateHttpClient()
    {
        var authOptions = factory.Services.GetService<IOptionsMonitor<AuthenticationSettings>>();

        var httpClient = factory.CreateClient();

        // if (!string.IsNullOrWhiteSpace(authOptions?.CurrentValue.TechnicalUserToken))
        //     httpClient.DefaultRequestHeaders.Authorization =
        //         new AuthenticationHeaderValue("bearer", authOptions.CurrentValue.TechnicalUserToken);

        return httpClient;
    }
}