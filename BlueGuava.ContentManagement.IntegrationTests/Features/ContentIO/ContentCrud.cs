using System.Net;
using System.Security.Claims;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Packages.Entities.DrmEntities;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace BlueGuava.ContentManagement.IntegrationTests.Features.ContentIO;

[Trait("ContentIO", "ContentCrud")]
public partial class TestBase
{
    /// <summary>
    /// NEX-1931: Only the creator or a sysadmin can delete a content
    /// </summary>
    /// <param name="version"></param>
    /// <param name="userRole"></param>
    /// <param name="expectedStatusCode"></param>
    [Theory]
    [InlineData("3.4", "SysAdmin", HttpStatusCode.OK)]
    [InlineData("3.4", "TechnicalUser", HttpStatusCode.OK)]
    public async Task NEX_1931_DeleteContentWithSysAdminCredential(string version, string userRole,
        HttpStatusCode expectedStatusCode)
    {
        // Arrange
        var contentId = await CreateTestContent();

        // create fake jwt token with claims
        var claims = new Dictionary<string, object>
        {
            { ClaimTypes.Name, $"{userRole} User" },
            { ClaimTypes.NameIdentifier, $"{Guid.NewGuid()}" },
            { ClaimTypes.Email, "<EMAIL>" },
            { ClaimTypes.Role, userRole }
        };
        client.SetFakeBearerToken(claims);

        // Act
        var response = await client.DeleteAsync($"api/v{version}/Content/{contentId}", default);

        // Assert
        Assert.Equal(expectedStatusCode, response.StatusCode);
        Assert.Equal("Deleted", await response.Content.ReadAsStringAsync());

        var itemService = factory.Services.GetService<IContentService>()!;
        var item = await itemService.Retrieve(contentId);

        Assert.Null(item);
    }

    /// <summary>
    /// NEX-1931: Only the creator or a sysadmin can delete a content
    /// </summary>
    /// <param name="version"></param>
    /// <param name="userRole"></param>
    /// <param name="expectedStatusCode"></param>
    [Theory]
    [InlineData("3.4", "Creator", HttpStatusCode.OK)]
    public async Task NEX_1931_DeleteContentWithOwnerCredential(string version, string userRole,
        HttpStatusCode expectedStatusCode)
    {
        // Arrange
        var ownerId = Guid.NewGuid();
        var contentId = await CreateTestContent(ownerId);

        var itemService = factory.Services.GetService<IContentService>()!;
        var item2 = await itemService.Retrieve(contentId);

        // create fake jwt token with claims
        var claims = new Dictionary<string, object>
        {
            { ClaimTypes.Name, $"{userRole} User" },
            { ClaimTypes.NameIdentifier, $"{ownerId}" },
            { ClaimTypes.Email, "<EMAIL>" },
            { ClaimTypes.Role, userRole }
        };
        client.SetFakeBearerToken(claims);

        // Act
        var response = await client.DeleteAsync($"api/v{version}/Content/{contentId}", default);

        // Assert
        Assert.Equal(expectedStatusCode, response.StatusCode);
        Assert.Equal("Deleted", await response.Content.ReadAsStringAsync());

        //var itemService = factory.Services.GetService<IContentService>()!;
        var item = await itemService.Retrieve(contentId);

        Assert.Null(item);
    }

    /// <summary>
    /// NEX-1931: Only the creator or a sysadmin can delete a content
    /// </summary>
    /// <param name="version"></param>
    /// <param name="userRole"></param>
    /// <param name="expectedStatusCode"></param>
    [Theory]
    [InlineData("3.4", "Creator", HttpStatusCode.Forbidden)]
    public async Task NEX_1931_DeleteContentWithWrongCredential(string version, string userRole,
        HttpStatusCode expectedStatusCode)
    {
        // Arrange
        var contentId = await CreateTestContent();

        var itemService = factory.Services.GetService<IContentService>()!;
        var item2 = await itemService.Retrieve(contentId);

        // create fake jwt token with claims
        var claims = new Dictionary<string, object>
        {
            { ClaimTypes.Name, $"{userRole} User" },
            { ClaimTypes.NameIdentifier, $"{Guid.NewGuid()}" },
            { ClaimTypes.Email, "<EMAIL>" },
            { ClaimTypes.Role, userRole }
        };
        client.SetFakeBearerToken(claims);

        // Act
        var response = await client.DeleteAsync($"api/v{version}/Content/{contentId}", default);

        // Assert
        Assert.Equal(expectedStatusCode, response.StatusCode);
    }

    private void AssertQueues()
    {
        Assert.Single(factory.InteropObjectMessaging.Data);

        Assert.Equal(2, factory.WebHookEventMessaging.Data.Count);

        Assert.Empty(factory.ContentPublishMessaging.Data);

        Assert.Single(factory.JobRequestMessaging.Data);

        Assert.Empty(factory.JobMessaging.Data);

        Assert.Empty(factory.MarkerMessaging.Data);

        Assert.Empty(factory.UpdateMessaging.Data);

        Assert.Empty(factory.CommercialMetricMessaging.Data);

        Assert.Empty(factory.ContentIngestMessaging.Data);
        Assert.Empty(factory.ObjectReleaseMessaging.Data);
        Assert.Equal(2, factory.RelationshipUpdateMessaging.Data.Count);

        Assert.Single(factory.TraceLogMessaging.Data);
        Assert.Equal(2, factory.ContentCreationMetricMessaging.Data.Count);
        Assert.Empty(factory.ContentWriteBackMessaging.Data);
        Assert.Empty(factory.ResourceCheckTriggerMessaging.Data);

        Assert.Empty(factory.AdvertisementTriggerMessaging.Data);
        Assert.Empty(factory.UseCaseRequestMessaging.Data);
        Assert.Empty(factory.MediaLiveControlTaskMessaging.Data);

        Assert.Empty(factory.MediaLiveStreamJobMessaging.Data);

        Assert.Empty(factory.IVSCallbackEventMessaging.Data);


        ClearQueues();
    }

    private async Task<Guid> CreateTestContent(Guid? owner = null)
    {
        var itemService = factory.Services.GetService<IContentService>()!;
        var contentId = Guid.NewGuid();
        var someOtherUserId = owner ?? Guid.NewGuid();
        var locale = "en-US";

        // Create a content item in the database
        await itemService.Create(new Content
        {
            Id = contentId,
            Type = ContentType.Video,
            OwnerId = someOtherUserId, // this will be used by the asset fixer method
            Assets = new List<Asset>
                {
                    {
                        // create a new asset without UploaderUserId
                        new()
                        {
                            Id = Guid.NewGuid(),
                            Type = AssetType.Video,
                            SubType = SubType.Original,
                            WorkflowStatus = WorkflowStatus.Succeeded,
                            PublicUrl = "https://cdn.INTEGRATION.ent360.blue/GUID/GUID.mp4",
                            ObjectUrl = "https://ent-contents-INTEGRATION-brand.s3.amazonaws.com/GUID/GUID.mp4",
                            Locale = locale,
                            Duration = 0,
                            IsPublic = true,
                            FileName = "GUID.mp4",
                            UploaderUserId = null
                        }
                    }
                }
        },
            new ClaimsPrincipal(new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, "Creator User"),
                new Claim(ClaimTypes.NameIdentifier, $"{someOtherUserId}"),
                new Claim(ClaimTypes.Email, "<EMAIL>"),
                new Claim(ClaimTypes.Role, "Creator")
            })));
        return contentId;
    }
}