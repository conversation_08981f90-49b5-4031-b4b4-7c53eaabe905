using System.Net.Http.Headers;
using Amazon.DynamoDBv2;
using BlueGuava.Audit.Common.Entities;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Api.Models;
using BlueGuava.ContentManagement.Api.Queuing;
using BlueGuava.ContentManagement.Api.Queuing.Model;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.IntegrationTests.Fakes;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Service.AmazonIVS.EventBridge;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.Library.Delivery.Messaging.Models;
using BlueGuava.MarkerManagement.Models;
using BlueGuava.MessageQueuing;
using BlueGuava.MessageQueuing.FakeQueue;
using BlueGuava.Reporting.Messages.Entities;
using BlueGuava.Reporting.Messages.Entities.WriteBacks;
using BlueGuava.Tracewind.Common.Models;
using BlueGuava.Webhook.Messaging.Entities;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WebMotions.Fake.Authentication.JwtBearer;
using Xunit.Abstractions;

namespace BlueGuava.ContentManagement.IntegrationTests.Infrastructure;

public class CustomWebApplicationFactory<TProgram>
    : WebApplicationFactory<TProgram> where TProgram : class
{
    private IConfiguration? configuration;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("IntegrationTest");
        builder.ConfigureLogging((context, logging) =>
        {
            // clear all previously registered providers
            logging.ClearProviders();
        });

        builder.ConfigureAppConfiguration((context, conf) =>
        {
            var env = context.HostingEnvironment;
            if (env.EnvironmentName is "Development" or "IntegrationTest")
            {
                conf.AddJsonFile($"appsettings.{env.EnvironmentName}.json", false, true);
                conf.AddEnvironmentVariables();
            }

            configuration = conf.Build();
        });

        builder.ConfigureServices(services =>
        {
            services.AddAuthentication(FakeJwtBearerDefaults.AuthenticationScheme).AddFakeJwtBearer();


            services.RemoveAllGeneric(typeof(IMessageQueue<>))
                .AddSingleton(typeof(IMessageQueue<MarkerMessage>), typeof(FakeMessageQueue<MarkerMessage>))
                .AddSingleton(typeof(IMessageQueue<Job>), typeof(FakeMessageQueue<Job>))
                .AddSingleton(typeof(IMessageQueue<JobUpdate>), typeof(FakeMessageQueue<JobUpdate>))
                .AddSingleton(typeof(IMessageQueue<JobRequest>), typeof(FakeMessageQueue<JobRequest>))
                .AddSingleton(typeof(IMessageQueue<WebHookEvent>), typeof(FakeMessageQueue<WebHookEvent>))
                .AddSingleton(typeof(IMessageQueue<TraceLogMessage>), typeof(FakeMessageQueue<TraceLogMessage>))
                .AddSingleton(typeof(IMessageQueue<UserLogEntry>), typeof(FakeMessageQueue<UserLogEntry>))
                .AddSingleton(typeof(IMessageQueue<UseCaseRequest>), typeof(FakeMessageQueue<UseCaseRequest>))
                .AddSingleton(typeof(IMessageQueue<CommercialMetricMessage>),
                    typeof(FakeMessageQueue<CommercialMetricMessage>))
                .AddSingleton(typeof(IMessageQueue<ContentCreationMetricMessage>),
                    typeof(FakeMessageQueue<ContentCreationMetricMessage>))
                .AddSingleton(typeof(IMessageQueue<ContentWriteBackMessage>),
                    typeof(FakeMessageQueue<ContentWriteBackMessage>))
                .AddSingleton(typeof(IMessageQueue<RelationshipUpdate>), typeof(FakeMessageQueue<RelationshipUpdate>))
                .AddSingleton(typeof(IMessageQueue<UpdateMessage>), typeof(FakeMessageQueue<UpdateMessage>))
                .AddSingleton(typeof(IMessageQueue<IvsCallbackEvent>), typeof(FakeMessageQueue<IvsCallbackEvent>))
                .AddSingleton(typeof(IMessageQueue<MediaLiveControlTask>),
                    typeof(FakeMessageQueue<MediaLiveControlTask>))
                .AddSingleton(typeof(IMessageQueue<MediaLiveStreamJob>), typeof(FakeMessageQueue<MediaLiveStreamJob>))
                .AddSingleton(typeof(IMessageQueue<ObjectReleaseMessage>),
                    typeof(FakeMessageQueue<ObjectReleaseMessage>))
                .AddSingleton(typeof(IMessageQueue<ContentIngestMessage>),
                    typeof(FakeMessageQueue<ContentIngestMessage>))
                .AddSingleton(typeof(IMessageQueue<InteropObjectMessage>),
                    typeof(FakeMessageQueue<InteropObjectMessage>))
                .AddSingleton(typeof(IMessageQueue<PlatformMetricMessage>),
                    typeof(FakeMessageQueue<PlatformMetricMessage>))
                .AddSingleton(typeof(IMessageQueue<ContentPublishMessage>),
                    typeof(FakeMessageQueue<ContentPublishMessage>))
                .AddSingleton(typeof(IMessageQueue<ResourceCheckTrigger>),
                    typeof(FakeMessageQueue<ResourceCheckTrigger>))
                .AddSingleton(typeof(IMessageQueue<AdvertismentTrigger>), typeof(FakeMessageQueue<AdvertismentTrigger>))
                .AddSingleton(typeof(IMessageQueue<JoinProjectMessage>), typeof(FakeMessageQueue<JoinProjectMessage>))
                ;

            services.RemoveAllGeneric(typeof(IS3Repository))
                .AddSingleton<IS3Repository, FakeS3Repository>();

            services.RemoveAll(typeof(IAmazonDynamoDB));

            var dynamoDbConfig = configuration?.GetSection("DynamoDb");

            services.AddAWSService<AmazonDynamoDBClient>();  // Registers IAmazonDynamoDB

            /*
            services.AddSingleton<IAmazonDynamoDB>(sp =>
            {
                var clientConfig = new AmazonDynamoDBConfig
                {
                    ServiceURL = dynamoDbConfig?.GetValue<string>("LocalServiceUrl")
                };

                return new AmazonDynamoDBClient(clientConfig);
            });
*/

            services.RemoveAllGeneric(typeof(IOpenSearchService))
                .AddScoped<IOpenSearchService, FakeOpenSearch>();

            services.RemoveAllGeneric(typeof(IOpenSearchService))
                .AddSingleton<ISearchAdapter, FakeSearchAdapter>();
        });
    }

    public FakeMessageQueue<JobRequest> JobRequestMessaging =>
        Services.GetService<IMessageQueue<JobRequest>>()! as FakeMessageQueue<JobRequest>;

    public FakeMessageQueue<MarkerMessage> MarkerMessaging =>
        Services.GetService<IMessageQueue<MarkerMessage>>()! as FakeMessageQueue<MarkerMessage>;

    public FakeMessageQueue<Job> JobMessaging => Services.GetService<IMessageQueue<Job>>()! as FakeMessageQueue<Job>;

    public FakeMessageQueue<JobUpdate> JobUpdateMessaging =>
        Services.GetService<IMessageQueue<JobUpdate>>()! as FakeMessageQueue<JobUpdate>;

    public FakeMessageQueue<UpdateMessage> UpdateMessaging =>
        Services.GetService<IMessageQueue<UpdateMessage>>()! as FakeMessageQueue<UpdateMessage>;

    public FakeMessageQueue<TraceLogMessage> TraceLogMessaging =>
        Services.GetService<IMessageQueue<TraceLogMessage>>()! as FakeMessageQueue<TraceLogMessage>;

    public FakeMessageQueue<WebHookEvent> WebHookEventMessaging =>
        Services.GetService<IMessageQueue<WebHookEvent>>()! as FakeMessageQueue<WebHookEvent>;

    public FakeMessageQueue<UseCaseRequest> UseCaseRequestMessaging =>
        Services.GetService<IMessageQueue<UseCaseRequest>>()! as FakeMessageQueue<UseCaseRequest>;

    public FakeMessageQueue<CommercialMetricMessage> CommercialMetricMessaging =>
        Services.GetService<IMessageQueue<CommercialMetricMessage>>()! as FakeMessageQueue<CommercialMetricMessage>;

    public FakeMessageQueue<ContentCreationMetricMessage> ContentCreationMetricMessaging =>
        Services.GetService<IMessageQueue<ContentCreationMetricMessage>>()! as
            FakeMessageQueue<ContentCreationMetricMessage>;

    public FakeMessageQueue<ContentWriteBackMessage> ContentWriteBackMessaging =>
        Services.GetService<IMessageQueue<ContentWriteBackMessage>>()! as FakeMessageQueue<ContentWriteBackMessage>;

    public FakeMessageQueue<RelationshipUpdate> RelationshipUpdateMessaging =>
        Services.GetService<IMessageQueue<RelationshipUpdate>>()! as FakeMessageQueue<RelationshipUpdate>;

    public FakeMessageQueue<IvsCallbackEvent> IVSCallbackEventMessaging =>
        Services.GetService<IMessageQueue<IvsCallbackEvent>>()! as FakeMessageQueue<IvsCallbackEvent>;

    public FakeMessageQueue<MediaLiveControlTask> MediaLiveControlTaskMessaging =>
        Services.GetService<IMessageQueue<MediaLiveControlTask>>()! as FakeMessageQueue<MediaLiveControlTask>;

    public FakeMessageQueue<MediaLiveStreamJob> MediaLiveStreamJobMessaging =>
        Services.GetService<IMessageQueue<MediaLiveStreamJob>>()! as FakeMessageQueue<MediaLiveStreamJob>;

    public FakeMessageQueue<ObjectReleaseMessage> ObjectReleaseMessaging =>
        Services.GetService<IMessageQueue<ObjectReleaseMessage>>()! as FakeMessageQueue<ObjectReleaseMessage>;

    public FakeMessageQueue<ContentIngestMessage> ContentIngestMessaging =>
        Services.GetService<IMessageQueue<ContentIngestMessage>>()! as FakeMessageQueue<ContentIngestMessage>;

    public FakeMessageQueue<InteropObjectMessage> InteropObjectMessaging =>
        Services.GetService<IMessageQueue<InteropObjectMessage>>()! as FakeMessageQueue<InteropObjectMessage>;

    public FakeMessageQueue<ContentPublishMessage> ContentPublishMessaging =>
        Services.GetService<IMessageQueue<ContentPublishMessage>>()! as FakeMessageQueue<ContentPublishMessage>;

    public FakeMessageQueue<ResourceCheckTrigger> ResourceCheckTriggerMessaging =>
        Services.GetService<IMessageQueue<ResourceCheckTrigger>>()! as FakeMessageQueue<ResourceCheckTrigger>;

    public FakeMessageQueue<AdvertismentTrigger> AdvertisementTriggerMessaging =>
        Services.GetService<IMessageQueue<AdvertismentTrigger>>()! as FakeMessageQueue<AdvertismentTrigger>;

    private ITestOutputHelper TestOutputHelper => Services.GetService<ITestOutputHelper>()!;

    public HttpClient CreateHttpClient()
    {
        var authOptions = Services.GetService<IOptionsMonitor<AuthenticationSettings>>();

        var httpClient = CreateClient();

        if (!string.IsNullOrWhiteSpace(authOptions?.CurrentValue.TechnicalUserToken))
            httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("bearer", authOptions.CurrentValue.TechnicalUserToken);

        return httpClient;
    }
}