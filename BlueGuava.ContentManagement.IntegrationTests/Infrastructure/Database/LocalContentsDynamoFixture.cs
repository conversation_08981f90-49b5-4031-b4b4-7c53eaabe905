using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.Model;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;
using Polly;
using Polly.Retry;
using Xunit;
using Xunit.Abstractions;

namespace BlueGuava.ContentManagement.IntegrationTests.Infrastructure.Database;

public class LocalContentsDynamoFixture : IAsyncLifetime
{
    private readonly IMessageSink sink;
    private readonly AsyncRetryPolicy retryPolicy;
    private readonly IAmazonDynamoDB client;
    private readonly IDynamoDBContext dynamoContext;
    private readonly string tableName = "Contents_V2";

    public LocalContentsDynamoFixture(IMessageSink sink)
    {
        this.sink = sink;
        var clientConfig = new AmazonDynamoDBConfig
        {
            ServiceURL = "http://localhost:8000"
        };
        client = new AmazonDynamoDBClient(clientConfig);
        dynamoContext = new DynamoDBContext(client);

        retryPolicy = Policy // retries indefinitely, until a 'stable' status is reached
            .Handle<ResourceNotFoundException>()
            .WaitAndRetryForeverAsync(i => TimeSpan.FromSeconds(5),
                (ex, span) => Console.WriteLine("\n*** Retry attempt after {0} because {1}", span, ex.Message));
    }

    public async Task InitializeAsync()
    {
        await InitializeTables();
    }

    public async Task DisposeAsync()
    {
        await CleanupTable();

        client.Dispose();
        dynamoContext.Dispose();
    }

    public async Task InitializeTables()
    {
        var tableDescription = await GetTableInformation();
        if (tableDescription == null)
            await CreateTable();

        await CleanupTable();
        Console.WriteLine("\n*** Item count: {0}", await GetItemCount());
    }

    private async Task CreateTable()
    {
        var request = new CreateTableRequest
        {
            AttributeDefinitions = new List<AttributeDefinition>()
            {
                new()
                {
                    AttributeName = "Id",
                    AttributeType = "S"
                },
                new()
                {
                    AttributeName = "ExternalId",
                    AttributeType = "S"
                },
                new()
                {
                    AttributeName = "OriginalFileName",
                    AttributeType = "S"
                },
                new()
                {
                    AttributeName = "Published",
                    AttributeType = "N"
                },
                new()
                {
                    AttributeName = "LastModifiedDate",
                    AttributeType = "S"
                }
            },
            KeySchema = new List<KeySchemaElement>
            {
                new()
                {
                    AttributeName = "Id",
                    KeyType = "HASH" //Partition key
                }
            },
            ProvisionedThroughput = new ProvisionedThroughput
            {
                ReadCapacityUnits = 100,
                WriteCapacityUnits = 100
            },
            TableName = tableName,
            GlobalSecondaryIndexes = new List<GlobalSecondaryIndex>()
            {
                new()
                {
                    IndexName = "ExternalId-index",
                    KeySchema = new List<KeySchemaElement>
                    {
                        new()
                        {
                            AttributeName = "ExternalId",
                            KeyType = "HASH" //Partition key
                        }
                    },
                    Projection = new Projection
                    {
                        ProjectionType = "ALL"
                    },
                    ProvisionedThroughput = new ProvisionedThroughput
                    {
                        ReadCapacityUnits = 100,
                        WriteCapacityUnits = 100
                    }
                },
                new()
                {
                    IndexName = "OriginalFileName-index",
                    KeySchema = new List<KeySchemaElement>
                    {
                        new()
                        {
                            AttributeName = "OriginalFileName",
                            KeyType = "HASH" //Partition key
                        }
                    },
                    Projection = new Projection
                    {
                        ProjectionType = "ALL"
                    },
                    ProvisionedThroughput = new ProvisionedThroughput
                    {
                        ReadCapacityUnits = 100,
                        WriteCapacityUnits = 100
                    }
                },
                new()
                {
                    IndexName = "Published-LastModifiedDate-index",
                    KeySchema = new List<KeySchemaElement>
                    {
                        new()
                        {
                            AttributeName = "Published",
                            KeyType = "HASH" //Partition key
                        },
                        new()
                        {
                            AttributeName = "LastModifiedDate",
                            KeyType = "RANGE" //Sort key
                        }
                    },
                    Projection = new Projection
                    {
                        ProjectionType = "ALL"
                    },
                    ProvisionedThroughput = new ProvisionedThroughput
                    {
                        ReadCapacityUnits = 100,
                        WriteCapacityUnits = 100
                    }
                }
            }
        };

        _ = await client.CreateTableAsync(request);

        await WaitUntilTableReady();
    }

    public async Task<long?> GetItemCount()
    {
        var request = new DescribeTableRequest
        {
            TableName = tableName
        };
        var tableInformation = await client.DescribeTableAsync(request);
        return tableInformation?.Table.ItemCount;
    }

    private async Task WaitUntilTableReady()
    {
        try
        {
            // Let us wait until table is created. Call DescribeTable.
            await retryPolicy.ExecuteAsync(async () =>
            {
                var res = await client.DescribeTableAsync(new DescribeTableRequest
                {
                    TableName = tableName
                });

                Console.WriteLine("\n*** Table name: {0}, status: {1}",
                    res.Table.TableName,
                    res.Table.TableStatus);

                var status = res.Table.TableStatus;

                if (status != "ACTIVE")
                    throw new ResourceNotFoundException($"\n*** Table {tableName} is not ready yet. Status: {status}");
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n*** Exception caught: {ex.Message}");
        }
    }

    public async Task<DescribeTableResponse?> GetTableInformation()
    {
        try
        {
            var request = new DescribeTableRequest
            {
                TableName = tableName
            };

            return await client.DescribeTableAsync(request);
        }
        catch (ResourceNotFoundException)
        {
            return null;
        }
    }

    private async Task CleanupTable()
    {
        Console.WriteLine("\n*** Table cleanup started");

        var search = dynamoContext.FromScanAsync<ContentRefDto>(new ScanOperationConfig());

        var result = await search.GetRemainingAsync();
        foreach (var item in result) await dynamoContext.DeleteAsync(item);

        Console.WriteLine("\n*** Table cleanup completed items removed: {0}", result.Count);
    }
}