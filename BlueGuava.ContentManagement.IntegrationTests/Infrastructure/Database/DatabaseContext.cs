using Xunit;

namespace BlueGuava.ContentManagement.IntegrationTests.Infrastructure.Database;

[CollectionDefinition("Contents Database collection")]
public class ContentsDynamoDbContextFixtureCollection : ICollectionFixture<LocalContentsDynamoFixture>
{
    // This class has no code, and is never created. Its purpose is simply
    // to be the place to apply [CollectionDefinition] and all the
    // ICollectionFixture<> interfaces.
}