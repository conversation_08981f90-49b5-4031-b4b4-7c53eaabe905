using Microsoft.Extensions.DependencyInjection;

namespace BlueGuava.ContentManagement.IntegrationTests.Infrastructure;

public static class ServiceExtensions
{
    /// <summary> Remove all services with the given <paramref name="genericType"/> from the <paramref name="services"/> pool </summary>
    /// <remarks> The type for <paramref name="genericType"/> must be an unbounded generic, eg: <c>typeof(IRepository&lt;&gt;)</c> </remarks>
    public static IServiceCollection RemoveAllGeneric(this IServiceCollection services, Type genericType)
    {
        // NOTE: use ToList() to create a copy, so that we can safely remove
        // items from the original without compromising the enumeration
        foreach (var reg in services.ToList())
        {
            if (!reg.ServiceType.IsGenericType) continue;
            if (reg.ServiceType.GetGenericTypeDefinition() != genericType) continue;
            services.Remove(reg);
        }

        return services;
    }


    /// <summary> Remove all services with the given <paramref name="serviceType"/> from the <paramref name="services"/> pool </summary>
    public static IServiceCollection RemoveAll(this IServiceCollection services, Type serviceType)
    {
        // NOTE: use ToList() to create a copy, so that we can safely remove
        // items from the original without compromising the enumeration
        foreach (var reg in services.ToList())
        {
            if (reg.ServiceType.IsGenericType) continue;
            if (reg.ServiceType != serviceType) continue;
            services.Remove(reg);
        }

        return services;
    }
}