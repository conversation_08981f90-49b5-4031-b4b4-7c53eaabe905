<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AngleSharp" Version="1.3.0" />
        <PackageReference Include="BlueGuava.MessageQueuing.FakeQueue" Version="8.1.1" />
        <PackageReference Include="FluentAssertions" Version="8.1.1" />
        <PackageReference Include="LinqKit" Version="1.3.8" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.13" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="WebMotions.Fake.Authentication.JwtBearer" Version="8.0.2" />
        <PackageReference Include="xunit" Version="2.9.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Api\BlueGuava.ContentManagement.Api.csproj" />
    </ItemGroup>

</Project>
