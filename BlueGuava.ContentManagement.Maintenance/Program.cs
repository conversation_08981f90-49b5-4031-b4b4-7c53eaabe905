﻿using System;
using System.Diagnostics;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.DependencyInjection;

namespace BlueGuava.ContentManagement.Maintenance;

internal class Program
{
    private static async Task<int> Main(string[] args)
    {
        try
        {
            var janitor = new Janitor("ContentManagement",
                services => { services.AddScoped<Common.Repositories.V2.IContentRepository, ContentRepository>(); });
            return await janitor.Run(args);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Demystify());
            return ExitCodes.EXIT_GENERAL_ERROR;
        }
    }
}