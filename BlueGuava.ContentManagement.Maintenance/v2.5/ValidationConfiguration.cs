﻿using System;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Maintenance.v2._5;

internal class ValidationConfiguration : IMaintenanceTool
{
    private readonly ILogger<ValidationConfiguration> logger;
    private readonly IConfiguration configuration;
    private readonly IParameterStore parameterStore;

    public ValidationConfiguration(ILogger<ValidationConfiguration> logger, IConfiguration configuration,
        IParameterStore parameterStore)
    {
        this.logger = logger;
        this.configuration = configuration;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 5);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        if (configuration.GetSection("ContentValidation").Exists())
            return ExitCodes.EXIT_SUCCESS;

        // for all content types verify that localization (title, and info or detail) for all brand languages exist
        await parameterStore.AddParameter("ContentValidation:CheckLocalization:Enabled", "true");

        // for all content types verify that all three image assets exist for the default language (`--`)
        await parameterStore.AddParameter("ContentValidation:CheckImages:Enabled", "true");
        await parameterStore.AddParameter("ContentValidation:CheckImages:ImageTypes:0:0", nameof(SubType.Landscape));
        await parameterStore.AddParameter("ContentValidation:CheckImages:ImageTypes:1:0", nameof(SubType.Portrait));
        await parameterStore.AddParameter("ContentValidation:CheckImages:ImageTypes:2:0", nameof(SubType.Square));

        // for audio, video, remix and livestream verify that both hls and dash asset exists
        await parameterStore.AddParameter("ContentValidation:CheckStreams:Enabled", "true");
        await parameterStore.AddParameter("ContentValidation:CheckStreams:ForTypes:0", nameof(ContentType.Audio));
        await parameterStore.AddParameter("ContentValidation:CheckStreams:ForTypes:1", nameof(ContentType.Video));
        await parameterStore.AddParameter("ContentValidation:CheckStreams:ForTypes:2", nameof(ContentType.Remix));
        await parameterStore.AddParameter("ContentValidation:CheckStreams:ForTypes:3", nameof(ContentType.LiveStream));
        await parameterStore.AddParameter("ContentValidation:CheckStreams:StreamTypes:0:0", nameof(SubType.HLS));
        await parameterStore.AddParameter("ContentValidation:CheckStreams:StreamTypes:1:0", nameof(SubType.DASH));

        // for the listed content types verify that duration is a positive number (`> 0`)
        await parameterStore.AddParameter("ContentValidation:CheckDuration:Enabled", "true");
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForJobs", "true");
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:0", nameof(ContentType.Audio));
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:1", nameof(ContentType.Video));
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:2", nameof(ContentType.Trailer));
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:3", nameof(ContentType.Podcast));
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:4", nameof(ContentType.Remix));
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:5",
            nameof(ContentType.CameraCapture));
        await parameterStore.AddParameter("ContentValidation:CheckDuration:ForTypes:6",
            nameof(ContentType.ScreenCapture));

        return ExitCodes.EXIT_SUCCESS;
    }
}