﻿using System;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace BlueGuava.ContentManagement.Maintenance.v2._2;

internal class IvsChatlogConfig : IMaintenanceTool
{
    private readonly ILogger<IvsChatlogConfig> logger;
    private readonly IResourceNameProvider resourceNameProvider;
    private readonly IParameterStore parameterStore;

    public IvsChatlogConfig(
        ILogger<IvsChatlogConfig> logger,
        IResourceNameProvider resourceNameProvider,
        IParameterStore parameterStore)
    {
        this.logger = logger;
        this.resourceNameProvider = resourceNameProvider;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 2);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        var lambdaArn = await resourceNameProvider.Get("lambda-ivschatlog-arn");
        await parameterStore.SetParameter("IVSOptions:MessageReviewHandler", lambdaArn);
        return ExitCodes.EXIT_SUCCESS;
    }
}