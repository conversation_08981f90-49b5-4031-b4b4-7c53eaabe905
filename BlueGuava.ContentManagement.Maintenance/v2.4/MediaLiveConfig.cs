﻿using System;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Maintenance.v2._4;

internal class MediaLiveConfig : IMaintenanceTool
{
    private readonly ILogger<MediaLiveConfig> logger;
    private readonly IConfiguration configuration;
    private readonly IResourceNameProvider resourceNameProvider;
    private readonly IParameterStore parameterStore;

    public MediaLiveConfig(ILogger<MediaLiveConfig> logger, IConfiguration configuration,
        IResourceNameProvider resourceNameProvider, IParameterStore parameterStore)
    {
        this.logger = logger;
        this.configuration = configuration;
        this.resourceNameProvider = resourceNameProvider;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 4);

    public Steps WhenSteps { get; } = Steps.Configure;

    //TO-DO
    //https://aws.amazon.com/blogs/media/support-for-aws-elemental-mediastore-ending-soon/
    //Amazon S3 is now a lower cost option for live video origination workflows when compared to MediaStore. In addition, AWS Elemental MediaPackage provides sophisticated packaging features such as DRM, HLS/DASH/CMAF re-packaging, cross-region redundancy, and more.
    public async Task<int> Execute(bool debugMode)
    {
        await parameterStore.SetParameter("MediaLive:RoleArn",
            await resourceNameProvider.Get("medialive_role_arn"));

        var mediastore = resourceNameProvider.Get("mediastore_domain_name");
        await parameterStore.SetParameter("MediaLive:Destination",
            $"mediastoressl://{await mediastore}/");

        await parameterStore.SetParameter("MediaLive:DistributionUrl",
            $"https://live.{configuration["Settings:Domain"]}/");

        return ExitCodes.EXIT_SUCCESS;
    }
}