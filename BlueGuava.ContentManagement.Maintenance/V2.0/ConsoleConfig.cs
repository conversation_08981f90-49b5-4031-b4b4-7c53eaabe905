﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Maintenance.V2._0;

public class ConsoleConfig : IMaintenanceTool
{
    private readonly ILogger<ConsoleConfig> logger;
    private readonly IConfiguration configuration;
    private readonly IParameterStore parameterStore;

    public ConsoleConfig(ILogger<ConsoleConfig> logger, IConfiguration configuration, IParameterStore parameterStore)
    {
        this.logger = logger;
        this.configuration = configuration;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 0);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        logger.LogDebug($"Updating console endpoints to v{SchemaVersion}{(debugMode ? " in debug mode" : "")}...");

        try
        {
            var tasks = new List<Task>();
            var baseUrl = configuration["Services:ContentManagement"];

            if (string.IsNullOrEmpty(baseUrl)) return 9;

            if (!debugMode)
            {
                tasks.Add(parameterStore.SetParameter("Console:Endpoints:Content", $"{baseUrl}api/v2.0/Content"));
                tasks.Add(parameterStore.SetParameter("Console:Endpoints:ContentEnums", $"{baseUrl}api/v2.0/Enums"));
            }

            await Task.WhenAll(tasks);

            logger.LogDebug($"Updated {tasks.Count} endpoints to v{SchemaVersion}.");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ExitCodes.EXIT_GENERAL_ERROR;
        }

        return ExitCodes.EXIT_SUCCESS;
    }
}