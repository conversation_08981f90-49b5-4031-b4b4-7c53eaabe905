﻿using System;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Maintenance.V2._0;

public class ConvertContentV2 : IMaintenanceTool
{
    private readonly ILogger<ConvertContentV2> logger;

    public ConvertContentV2(ILogger<ConvertContentV2> logger)
    {
        this.logger = logger;
    }

    public Version SchemaVersion { get; } = new(2, 0);

    public Steps WhenSteps { get; } = Steps.Migration;

    public Task<int> Execute(bool debugMode)
    {
        logger.LogDebug("Content v1 -> v2 conversion is no longer supported");
        return Task.FromResult(ExitCodes.EXIT_SUCCESS);
    }
}