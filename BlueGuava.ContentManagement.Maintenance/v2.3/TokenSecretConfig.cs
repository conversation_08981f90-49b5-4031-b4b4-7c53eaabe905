﻿using System;
using System.Security.Cryptography;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Maintenance.v2._3;

internal class TokenSecretConfig : IMaintenanceTool
{
    private readonly ILogger<TokenSecretConfig> logger;
    private readonly IConfiguration configuration;
    private readonly IParameterStore parameterStore;

    public TokenSecretConfig(ILogger<TokenSecretConfig> logger, IConfiguration configuration,
        IParameterStore parameterStore)
    {
        this.logger = logger;
        this.configuration = configuration;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 3);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        var secret = GenerateTokenSecret(24);
        await parameterStore.SetParameter("ContentToken:Secret", secret);
        return ExitCodes.EXIT_SUCCESS;
    }

    public string GenerateTokenSecret(int length)
    {
        var buffer = new byte[2 * length];
        RandomNumberGenerator.Fill(buffer);
        var base64 = Convert.ToBase64String(buffer);
        return base64.Replace("+", "").Replace("/", "")
            .Substring(0, length);
    }
}