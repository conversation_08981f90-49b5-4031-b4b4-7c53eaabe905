﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <Version>1.0.0</Version>
        <LangVersion>12</LangVersion>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Amazon.Extensions.Configuration.SystemsManager" Version="6.2.2" />
        <PackageReference Include="BlueGuava.Maintenance.Framework" Version="8.1.1" />
        <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Repository.DynamoDb\BlueGuava.ContentManagement.Repository.DynamoDb.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Common\BlueGuava.ContentManagement.Common.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service\BlueGuava.ContentManagement.Service.csproj" />
    </ItemGroup>

</Project>