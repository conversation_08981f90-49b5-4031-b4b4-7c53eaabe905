﻿using System;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Maintenance.v2._1;

internal class ConsoleConfig : IMaintenanceTool
{
    private readonly ILogger<ConsoleConfig> logger;
    private readonly IConfiguration configuration;
    private readonly IParameterStore parameterStore;

    public ConsoleConfig(ILogger<ConsoleConfig> logger, IConfiguration configuration, IParameterStore parameterStore)
    {
        this.logger = logger;
        this.configuration = configuration;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 1);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        var baseUrl = new Uri(configuration["Services:ContentManagement"]);
        var recordingUrl = new Uri(baseUrl, "api/v2.0/Recording").ToString();
        await parameterStore.SetParameter("Console:Endpoints:Recording", recordingUrl);
        return ExitCodes.EXIT_SUCCESS;
    }
}