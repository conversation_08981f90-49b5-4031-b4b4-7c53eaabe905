﻿using System;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;
using Microsoft.Extensions.Configuration;

namespace BlueGuava.ContentManagement.Maintenance.v2._6;

internal class ApiVersionConfiguration : IMaintenanceTool
{
    private readonly IConfiguration configuration;
    private readonly IParameterStore parameterStore;

    public ApiVersionConfiguration(IConfiguration configuration, IParameterStore parameterStore)
    {
        this.configuration = configuration;
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 6);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        if (configuration.GetSection("ApiVersion").Exists())
            return ExitCodes.EXIT_SUCCESS;

        // for indicating which version of ContentManagement API should be used by other services
        await parameterStore.AddParameter("ApiVersion:ContentManagement", "4.0");

        return ExitCodes.EXIT_SUCCESS;
    }
}