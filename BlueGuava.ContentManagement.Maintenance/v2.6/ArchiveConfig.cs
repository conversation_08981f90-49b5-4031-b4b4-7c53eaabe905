﻿using System;
using System.Threading.Tasks;
using BlueGuava.Maintenance.Abstractions;
using BlueGuava.Maintenance.Framework;

namespace BlueGuava.ContentManagement.Maintenance.v2._6;

internal class ArchiveConfig : IMaintenanceTool
{
    private readonly IParameterStore parameterStore;

    public ArchiveConfig(IParameterStore parameterStore)
    {
        this.parameterStore = parameterStore;
    }

    public Version SchemaVersion { get; } = new(2, 6);

    public Steps WhenSteps { get; } = Steps.Configure;

    public async Task<int> Execute(bool debugMode)
    {
        var paramName = "ArchiveConfiguration:FileRestorationPeriod";
        var paramValue = "30";
        await parameterStore.SetParameter(paramName, paramValue);
        return ExitCodes.EXIT_SUCCESS;
    }
}