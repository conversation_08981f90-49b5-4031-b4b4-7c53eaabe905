﻿using System;

namespace BlueGuava.ContentManagement.Service.Chime.Exceptions;

public class ResourceNotFoundException : Exception
{
    public ResourceNotFoundException(string resourceName, string resourceId)
        : base($"No {resourceName} found by {resourceId}")
    {
        ResourceName = resourceName;
        ResourceId = resourceId;
    }

    public string ResourceName { get; set; }
    public string ResourceId { get; set; }
}