﻿using System;
using Amazon;

namespace BlueGuava.ContentManagement.Service.Chime.Implementation;

public class ChimeOptions
{
    private string? mediaRegion;

    /// <summary> AWS region for meeting media </summary>
    public string? MediaRegion
    {
        get => mediaRegion;
        set => mediaRegion = string.IsNullOrEmpty(value)
            ? null
            : RegionEndpoint.GetBySystemName(value).SystemName
              ?? throw new ArgumentOutOfRangeException(nameof(value), value, "Invalid AWS region name");
    }

    /// <summary> AWS account identifier </summary>
    public string? AccountId { get; set; }

    /// <summary> AWS S3 bucket name for meeting media </summary>
    public string? ChimeMedia { get; set; }
}