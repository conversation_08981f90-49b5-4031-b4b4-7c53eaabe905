﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.ChimeSDKMediaPipelines;
using Amazon.ChimeSDKMediaPipelines.Model;
using Amazon.ChimeSDKMeetings;
using Amazon.ChimeSDKMeetings.Model;
using BlueGuava.ContentManagement.Common.Entities.Chime;
using BlueGuava.ContentManagement.Common.Entities.IVS;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using CaptureNotFoundException = Amazon.ChimeSDKMediaPipelines.Model.NotFoundException;
using MeetingNotFoundException = Amazon.ChimeSDKMeetings.Model.NotFoundException;
using PlacementInfo = BlueGuava.ContentManagement.Common.Entities.Chime.MediaPlacement;

namespace BlueGuava.ContentManagement.Service.Chime.Implementation;

public class ChimeIntegrationService : IChimeIntegrationService
{
    private readonly ILogger<ChimeIntegrationService> logger;
    private readonly ChimeOptions chimeOptions;
    private readonly IAmazonChimeSDKMeetings chimeMeetings;
    private readonly IAmazonChimeSDKMediaPipelines chimeMedia;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IFeatureManager featureManager;

    public ChimeIntegrationService(
        ILogger<ChimeIntegrationService> logger,
        IOptionsMonitor<ChimeOptions> chimeOptions,
        IAmazonChimeSDKMeetings chimeMeetings,
        IAmazonChimeSDKMediaPipelines chimeMedia,
        ICorrelationContextAccessor correlationContextAccessor,
        IFeatureManager featureManager
    )
    {
        this.logger = logger;
        this.chimeOptions = chimeOptions.CurrentValue;
        this.chimeMeetings = chimeMeetings;
        this.chimeMedia = chimeMedia;
        this.correlationContextAccessor = correlationContextAccessor;
        this.featureManager = featureManager;
    }


    public async Task<MeetingInfo> CreateMeeting(Guid contentId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(CreateMeeting))
                .Log(LogLevel.Debug, "ContentId: {ContentId}", contentId);

        var chimeRegion = chimeMeetings.Config.RegionEndpoint;
        var response = await chimeMeetings.CreateMeetingAsync(new CreateMeetingRequest
        {
            ExternalMeetingId = contentId.ToString(),
            ClientRequestToken = contentId.ToString(),
            MediaRegion = chimeRegion.SystemName,
            MeetingFeatures = new MeetingFeaturesConfiguration()
            {
                Audio = new AudioFeatures()
                {
                    EchoReduction = MeetingFeatureStatus.AVAILABLE
                }
            }
        });

        if (await featureManager.IsEnabledAsync("AllowAutoLiveTranscribe"))
            await StartTranscription(response.Meeting.MeetingId!);

        return Translate(response.Meeting);
    }

    public async Task<MeetingInfo> GetMeeting(string meetingId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(GetMeeting))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}", meetingId);

        return Translate(await LoadMeeting(meetingId));
    }

    public async Task DeleteMeeting(string meetingId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(DeleteMeeting))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}", meetingId);

        var request = new DeleteMeetingRequest { MeetingId = meetingId };
        await chimeMeetings.DeleteMeetingAsync(request);
        // Delete throws NotFoundException if resource does not exist
    }


    public async Task<AttendeeInfo> ConnectAttendee(string meetingId, Guid userId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(ConnectAttendee))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}, UserId: {UserId}", meetingId, userId);

        var meeting = await LoadMeeting(meetingId); // verify if meeting exists
        var match = await FindAttendee(meetingId, userId.ToString());
        if (match != null) return Translate(match, meeting.MeetingId, Guid.Parse(meeting.ExternalMeetingId));

        var request = new CreateAttendeeRequest
        {
            MeetingId = meeting.MeetingId,
            ExternalUserId = userId.ToString()
        };
        var response = await chimeMeetings.CreateAttendeeAsync(request);
        return Translate(response.Attendee, meeting.MeetingId, Guid.Parse(meeting.ExternalMeetingId));
    }

    public async Task<AttendeeInfo> DisconnectAttendee(string meetingId, Guid userId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(DisconnectAttendee))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}, UserId: {UserId}", meetingId, userId);

        var userCode = userId.ToString();
        var meeting = await LoadMeeting(meetingId); // verify if meeting exists
        var match = await FindAttendee(meetingId, userCode); // and the attendee
        if (match != null) throw new Exceptions.ResourceNotFoundException("attendee", userCode);

        var request = new DeleteAttendeeRequest
        {
            MeetingId = meeting.MeetingId,
            AttendeeId = match?.AttendeeId
        };
        await chimeMeetings.DeleteAttendeeAsync(request);

        return Translate(match, meeting.MeetingId, Guid.Parse(meeting.ExternalMeetingId));
    }


    public async Task StartTranscription(string meetingId, string? locale = null)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(StartTranscription))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}, Locale: {Locale}", meetingId, locale);

        //_ = await LoadMeeting(meetingId); // throws if meeting does not exist
        await chimeMeetings.StartMeetingTranscriptionAsync(new StartMeetingTranscriptionRequest
        {
            MeetingId = meetingId,
            TranscriptionConfiguration = new TranscriptionConfiguration
            {
                EngineTranscribeSettings = new EngineTranscribeSettings
                {
                    IdentifyLanguage = true,
                    EnablePartialResultsStabilization = true,
                    PartialResultsStability = TranscribePartialResultsStability.High,
                    LanguageOptions = "en-US,es-US,zh-CN,fr-FR,hi-IN",
                    PreferredLanguage = "en-US"
                }
            }
        });
    }

    public async Task StopTranscription(string meetingId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(StopTranscription))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}", meetingId);

        var request = new StopMeetingTranscriptionRequest { MeetingId = meetingId };
        await chimeMeetings.StopMeetingTranscriptionAsync(request);
    }


    public async Task<CaptureInfo> StartRecording(string meetingId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(StartRecording))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}", meetingId);
        var meeting = await LoadMeeting(meetingId); // verify if meeting exists
        var fullS3Path = $"{chimeOptions.ChimeMedia}/{meeting.ExternalMeetingId}/{Guid.NewGuid()}/";
        var sinkArn = $"arn:aws:s3:::{fullS3Path}";
        var captureResponse = await chimeMedia.CreateMediaCapturePipelineAsync(new CreateMediaCapturePipelineRequest
        {
            SourceArn = GetMediaSourceArn(meeting),
            SourceType = MediaPipelineSourceType.ChimeSdkMeeting,
            SinkArn = sinkArn,
            SinkType = MediaPipelineSinkType.S3Bucket,
            ClientRequestToken = meeting.ExternalMeetingId,
            ChimeSdkMeetingConfiguration = new ChimeSdkMeetingConfiguration
            {
                ArtifactsConfiguration = new ArtifactsConfiguration
                {
                    Audio = new AudioArtifactsConfiguration
                    {
                        MuxType = AudioMuxType.AudioOnly
                    },
                    Video = new VideoArtifactsConfiguration
                    {
                        State = ArtifactsState.Enabled,
                        MuxType = VideoMuxType.VideoOnly
                    },
                    Content = new ContentArtifactsConfiguration
                    {
                        State = ArtifactsState.Enabled,
                        MuxType = ContentMuxType.ContentOnly
                    }
                }
            }
        });

        return Translate(captureResponse.MediaCapturePipeline, meeting);
    }

    public async Task<CaptureInfo?> TryGetRecording(string? captureId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(TryGetRecording))
                .Log(LogLevel.Debug, "CaptureId: {CaptureId}", captureId);

        try
        {
            var capture = await GetCapture(captureId);
            var meeting = await LoadMeeting(GetMeetingId(capture.SourceArn));
            return Translate(capture, meeting);
        }
        catch
        {
            return null;
        }
    }

    public async Task<CaptureInfo> StopRecording(string captureId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(StopRecording))
                .Log(LogLevel.Debug, "CaptureId: {CaptureId}", captureId);

        var capture = await GetCapture(captureId);
        var meeting = await LoadMeeting(GetMeetingId(capture.SourceArn));

        var request = new DeleteMediaCapturePipelineRequest { MediaPipelineId = captureId };
        await chimeMedia.DeleteMediaCapturePipelineAsync(request);

        return Translate(capture, meeting);
    }


    public async Task<LiveSinkInfo> StartStreaming(string meetingId, LiveStreamInfo endpoint,
        ViewConfigurations viewConfigurations)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(StartStreaming))
                .Log(LogLevel.Debug, "MeetingId: {MeetingId}, Endpoint: {Endpoint}", meetingId, endpoint.ToJson());

        var meeting = await LoadMeeting(meetingId); // verify if meeting exists, and fetch details
        var connectorResponse = await chimeMedia.CreateMediaLiveConnectorPipelineAsync(
            new CreateMediaLiveConnectorPipelineRequest
            {
                ClientRequestToken = Guid.NewGuid().ToString() + DateTime.UtcNow.Ticks,
                Sources =
                {
                    new LiveConnectorSourceConfiguration
                    {
                        SourceType = LiveConnectorSourceType.ChimeSdkMeeting,
                        ChimeSdkMeetingLiveConnectorConfiguration = new ChimeSdkMeetingLiveConnectorConfiguration
                        {
                            Arn = meeting.MeetingArn.Replace(":meeting/", ":meeting:"),
                            MuxType = LiveConnectorMuxType.AudioWithCompositedVideo,
                            CompositedVideo = new CompositedVideoArtifactsConfiguration
                            {
                                GridViewConfiguration = GetViewConfiguration(viewConfigurations),
                                Layout = new LayoutOption(LayoutOption.GridView),
                                Resolution = ResolutionOption.HD
                            }
                        }
                    }
                },
                Sinks =
                {
                    new LiveConnectorSinkConfiguration
                    {
                        SinkType = LiveConnectorSinkType.RTMP,
                        RTMPConfiguration = new LiveConnectorRTMPConfiguration
                        {
                            Url = $"rtmps://{endpoint.IngestEndpoint}/app/{endpoint.StreamKey}",
                            AudioChannels = AudioChannelsOption.Stereo,
                            AudioSampleRate = "48000" // default
                        }
                    }
                }
            });

        return Translate(connectorResponse.MediaLiveConnectorPipeline, meeting);
    }

    public async Task<LiveSinkInfo?> TryGetStreaming(string liveSinkId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(TryGetStreaming))
                .Log(LogLevel.Debug, "LiveSinkId: {LiveSinkId}", liveSinkId);

        try
        {
            var liveSink = await GetLiveSink(liveSinkId);
            var source = liveSink.Sources[0].ChimeSdkMeetingLiveConnectorConfiguration;
            var meeting = await LoadMeeting(GetMeetingId(source.Arn));
            return Translate(liveSink, meeting);
        }
        catch
        {
            return null;
        }
    }

    public async Task<LiveSinkInfo> StopStreaming(string liveSinkId)
    {
        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(ChimeIntegrationService), nameof(StopStreaming))
                .Log(LogLevel.Debug, "LiveSinkId: {LiveSinkId}", liveSinkId);

        var liveSink = await GetLiveSink(liveSinkId);
        var source = liveSink.Sources[0].ChimeSdkMeetingLiveConnectorConfiguration;
        var meeting = await LoadMeeting(GetMeetingId(source.Arn));

        var request = new DeleteMediaPipelineRequest { MediaPipelineId = liveSinkId };
        await chimeMedia.DeleteMediaPipelineAsync(request);

        return Translate(liveSink, meeting);
    }


    private async Task<Meeting> LoadMeeting(string meetingId)
    {
        try
        {
            var request = new GetMeetingRequest { MeetingId = meetingId };
            var response = await chimeMeetings.GetMeetingAsync(request);
            return response.Meeting;
        }
        catch (MeetingNotFoundException)
        {
            throw new Exceptions.ResourceNotFoundException("meeting", meetingId);
        }
    }

    private async Task<Attendee?> FindAttendee(string meetingId, string userId)
    {
        var request = new ListAttendeesRequest { MeetingId = meetingId };
        while (true)
        {
            var page = await chimeMeetings.ListAttendeesAsync(request);
            foreach (var item in page.Attendees)
                if (item.ExternalUserId == userId)
                    return item; // item found

            request.NextToken = page.NextToken;
            if (string.IsNullOrEmpty(page.NextToken)) break;
        }

        return null;
    }

    public async Task<IEnumerable<Attendee?>> ListAttendees(string meetingId)
    {
        var result = new List<Attendee>();
        var request = new ListAttendeesRequest { MeetingId = meetingId };
        while (true)
        {
            var page = await chimeMeetings.ListAttendeesAsync(request);

            if (page.Attendees?.Any() ?? false)
                result.AddRange(page.Attendees);

            request.NextToken = page.NextToken;
            if (string.IsNullOrEmpty(page.NextToken)) break;
        }

        return result;
    }

    private async Task<MediaPipeline> GetMediaPipeline(string? pipelineId)
    {
        try
        {
            var request = new GetMediaPipelineRequest { MediaPipelineId = pipelineId };
            var response = await chimeMedia.GetMediaPipelineAsync(request);
            return response.MediaPipeline;
        }
        catch (CaptureNotFoundException)
        {
            throw new Exceptions.ResourceNotFoundException("media pipeline", pipelineId ?? string.Empty);
        }
    }

    private async Task<MediaCapturePipeline> GetCapture(string? captureId)
    {
        return (await GetMediaPipeline(captureId)).MediaCapturePipeline
               ?? throw new Exceptions.ResourceNotFoundException("media capture", captureId ?? string.Empty);
    }

    private async Task<MediaLiveConnectorPipeline> GetLiveSink(string liveSinkId)
    {
        return (await GetMediaPipeline(liveSinkId)).MediaLiveConnectorPipeline
               ?? throw new Exceptions.ResourceNotFoundException("live sink", liveSinkId);
    }

    private string GetMediaSourceArn(Meeting meeting)
    {
        return $"arn:aws:chime:{meeting.MediaRegion}:{chimeOptions.AccountId}:{meeting.MeetingId}";
    }

    private string GetMeetingId(string mediaSourceArn)
    {
        return mediaSourceArn.TrimEnd(':').Substring(mediaSourceArn.LastIndexOf(':') + 1);
    }


    private static MeetingInfo Translate(Meeting meeting)
    {
        return new MeetingInfo()
        {
            MeetingId = meeting.MeetingId,
            ContentId = Guid.Parse(meeting.ExternalMeetingId),
            ExternalMeetingId = meeting.ExternalMeetingId,
            MediaRegion = meeting.MediaRegion,
            MeetingHostId = meeting.MeetingHostId,
            PrimaryMeetingId = meeting.PrimaryMeetingId,
            MediaPlacement = meeting.MediaPlacement == null
                ? null
                : new PlacementInfo
                {
                    AudioFallbackUrl = meeting.MediaPlacement.AudioFallbackUrl,
                    AudioHostUrl = meeting.MediaPlacement.AudioHostUrl,
                    EventIngestionUrl = meeting.MediaPlacement.EventIngestionUrl,
                    ScreenDataUrl = meeting.MediaPlacement.ScreenDataUrl,
                    ScreenSharingUrl = meeting.MediaPlacement.ScreenSharingUrl,
                    ScreenViewingUrl = meeting.MediaPlacement.ScreenViewingUrl,
                    SignalingUrl = meeting.MediaPlacement.SignalingUrl,
                    TurnControlUrl = meeting.MediaPlacement.TurnControlUrl
                },
            MeetingFeatures = meeting.MeetingFeatures == null
                ? null
                : new MeetingFeatures
                {
                    Audio = meeting.MeetingFeatures.Audio == null
                        ? null
                        : new MeetingAudio
                        {
                            EchoReduction = meeting.MeetingFeatures.Audio.EchoReduction
                        }
                }
                // NOTE: docs describes this field, but there is no property in the assembly
                // TenantIds = meeting.TenantIds,
        };
    }

    private static AttendeeInfo Translate(Attendee? attendee, string meetingId, Guid contentId)
    {
        return new AttendeeInfo()
        {
            MeetingId = meetingId,
            ContentId = contentId,
            AttendeeId = attendee?.AttendeeId,
            ExternalUserId = attendee?.ExternalUserId,
            JoinToken = attendee?.JoinToken,
            Capabilities = attendee?.Capabilities == null
                ? null
                : new Capabilities
                {
                    Audio = attendee.Capabilities.Audio,
                    Video = attendee.Capabilities.Video,
                    Content = attendee.Capabilities.Content
                }
        };
    }

    private static CaptureInfo Translate(MediaCapturePipeline capture, Meeting meeting)
    {
        return new CaptureInfo()
        {
            MeetingId = meeting.MeetingId,
            CaptureId = capture.MediaPipelineId,
            ContentId = Guid.Parse(meeting.ExternalMeetingId),
            ArchiveUrl = "s3://" + capture.SinkArn.Substring(13)
        };
    }

    private static LiveSinkInfo Translate(MediaLiveConnectorPipeline liveSink, Meeting meeting)
    {
        return new LiveSinkInfo()
        {
            MeetingId = meeting.MeetingId,
            ContentId = Guid.Parse(meeting.ExternalMeetingId),
            LiveSinkId = liveSink.MediaPipelineId,
            LiveSinkStatus = liveSink.Status
        };
    }

    private static GridViewConfiguration GetViewConfiguration(ViewConfigurations viewConfiguration)
    {
        switch (viewConfiguration)
        {
            case ViewConfigurations.Default:
                return new GridViewConfiguration
                { ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.Horizontal) };
            case ViewConfigurations.PresentOnlyTopLeft:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.PresenterOnly),
                    PresenterOnlyConfiguration = new PresenterOnlyConfiguration()
                    {
                        PresenterPosition = PresenterPosition.TopLeft
                    }
                };
            case ViewConfigurations.PresentOnlyTopRight:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.PresenterOnly),
                    PresenterOnlyConfiguration = new PresenterOnlyConfiguration()
                    {
                        PresenterPosition = PresenterPosition.TopRight
                    }
                };
            case ViewConfigurations.PresentOnlyBottomLeft:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.PresenterOnly),
                    PresenterOnlyConfiguration = new PresenterOnlyConfiguration()
                    {
                        PresenterPosition = PresenterPosition.BottomLeft
                    }
                };
            case ViewConfigurations.PresentOnlyBottomRight:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.PresenterOnly),
                    PresenterOnlyConfiguration = new PresenterOnlyConfiguration()
                    {
                        PresenterPosition = PresenterPosition.BottomRight
                    }
                };

            case ViewConfigurations.ActiveSpeakerTopLeft:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.ActiveSpeakerOnly),
                    ActiveSpeakerOnlyConfiguration = new ActiveSpeakerOnlyConfiguration()
                    { ActiveSpeakerPosition = ActiveSpeakerPosition.TopLeft }
                };
            case ViewConfigurations.ActiveSpeakerTopRight:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.ActiveSpeakerOnly),
                    ActiveSpeakerOnlyConfiguration = new ActiveSpeakerOnlyConfiguration()
                    { ActiveSpeakerPosition = ActiveSpeakerPosition.TopRight }
                };
            case ViewConfigurations.ActiveSpeakerBottomLeft:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.ActiveSpeakerOnly),
                    ActiveSpeakerOnlyConfiguration = new ActiveSpeakerOnlyConfiguration()
                    { ActiveSpeakerPosition = ActiveSpeakerPosition.BottomLeft }
                };
            case ViewConfigurations.ActiveSpeakerBottomRight:
                return new GridViewConfiguration
                {
                    ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.ActiveSpeakerOnly),
                    ActiveSpeakerOnlyConfiguration = new ActiveSpeakerOnlyConfiguration()
                    { ActiveSpeakerPosition = ActiveSpeakerPosition.BottomRight }
                };
            case ViewConfigurations.Horizontal:
                return new GridViewConfiguration
                { ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.Horizontal) };
            case ViewConfigurations.Vertical:
                return new GridViewConfiguration
                { ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.Vertical) };
            default:
                return new GridViewConfiguration
                { ContentShareLayout = new ContentShareLayoutOption(ContentShareLayoutOption.Horizontal) };
        }
    }
}