﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net8.0</TargetFrameworks>
        <LangVersion>12</LangVersion>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AWSSDK.Chime" Version="3.7.401.4" />
        <PackageReference Include="AWSSDK.ChimeSDKIdentity" Version="3.7.400.106" />
        <PackageReference Include="AWSSDK.ChimeSDKMediaPipelines" Version="3.7.401.59" />
        <PackageReference Include="AWSSDK.ChimeSDKMessaging" Version="3.7.400.106" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Common\BlueGuava.ContentManagement.Common.csproj" />
    </ItemGroup>

</Project>
