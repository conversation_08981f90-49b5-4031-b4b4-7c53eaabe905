﻿using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Service.Chime.Implementation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using static Amazon.RegionEndpoint;

namespace BlueGuava.ContentManagement.Service.Chime;

public static class ChimeIntergrationSetupExtensions
{
    /// <summary> Adds <see cref="IChimeIntegrationService"/> and dependencies to the <paramref name="services"/> collection </summary>
    public static IServiceCollection AddChimeIntegration(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDefaultAWSOptions(configuration.GetAWSOptions());
        services.Configure<ChimeOptions>(configuration.GetSection("Chime")); // MediaRegion
        services.Configure<ChimeOptions>(configuration.GetSection("AWS")); // AccountId
        services.Configure<ChimeOptions>(configuration.GetSection("S3Bucket")); // ChimeMedia

        var awsOptions = configuration.GetAWSOptions();
        var region = configuration["Chime:MediaRegion"];
        if (!string.IsNullOrEmpty(region))
            awsOptions.Region = GetBySystemName(region);

        services.AddAWSService<Amazon.Chime.IAmazonChime>(awsOptions);
        services.AddAWSService<Amazon.ChimeSDKIdentity.IAmazonChimeSDKIdentity>(awsOptions);
        services.AddAWSService<Amazon.ChimeSDKMediaPipelines.IAmazonChimeSDKMediaPipelines>(awsOptions);
        services.AddAWSService<Amazon.ChimeSDKMeetings.IAmazonChimeSDKMeetings>(awsOptions);
        services.AddAWSService<Amazon.ChimeSDKMessaging.IAmazonChimeSDKMessaging>(awsOptions);

        services.AddScoped<IChimeIntegrationService, ChimeIntegrationService>();

        return services;
    }
}