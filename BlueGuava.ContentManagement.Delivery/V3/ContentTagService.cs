﻿using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Services.V3;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Library;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;

namespace BlueGuava.ContentManagement.Delivery.V3;

public class ContentTagService : IContentTagService
{
    private const string CONTENT_REPOSITORY_PATH = "api/v6/content/labels/{0}/children/content/entity.json";
    private const string OBJECT_REPOSITORY_PATH = "{0}/v6/{1}/{2}/entity.json";

    private readonly IS3Repository s3Repository;
    private readonly ModuleInfo moduleInfo;
    private readonly IFeatureManager featureManager;


    private readonly string bucketNamePublic;
    private readonly string contentCdnExpiration;
    private readonly string cdnUrl;


    public ContentTagService(
        IOptionsMonitor<S3Bucket> bucketSettings,
        IS3Repository s3Repository,
        IConfiguration configuration,
        IOptionsMonitor<CdnSettings> cdnSettings,
        IOptions<ModuleInfo> moduleInfo,
        IFeatureManager featureManager)
    {
        this.s3Repository = s3Repository;

        bucketNamePublic = bucketSettings.CurrentValue.Contents ?? "";
        contentCdnExpiration = configuration["CDN:Expiration:Content"] ?? "600";
        cdnUrl = cdnSettings.CurrentValue.DefaultUrl ?? configuration["Settings:CdnBaseUrl"] ?? "";
        this.moduleInfo = moduleInfo.Value;
        this.featureManager = featureManager;
    }

    public async Task ReleaseContentTag(List<Content>? entity, string tag)
    {
        if (entity == null || !entity.Any())
        {
            await s3Repository.RemoveFileAsync(bucketNamePublic,
                string.Format(CONTENT_REPOSITORY_PATH,
                    Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(tag.ToLower()))));
            return;
        }

        var result = new Library.Interop.v2.Object();
        entity!.ToList().ForEach(cnt =>
        {
            var entityType = "Content";
            var child = new Library.Interop.v2.Object();
            child.Actions.SetAction(new Library.Interop.v2.Action()
            {
                Url = string.Format(OBJECT_REPOSITORY_PATH,
                    $"{cdnUrl}/api",
                    $"{entityType.ToLower()}",
                    cnt.Id),
                HttpMethod = "GET",
                ActionName = "Navigation"
            });
            child.SetProperty(Constants.CONTENT_ID, cnt.Id);
            child.SetProperty(Constants.CONTENT_TYPE, entityType);
            child.SetProperty(Constants.CONTENT_INDEX, "0");
            result.AddChild(child);
        });

        result.Version = moduleInfo.Version;
        result.TimeStamp = DateTime.UtcNow.ToString("o");
        result.Properties.Add(new Library.Interop.v2.Property(Constants.CONTENT_LABEL_GENRE, tag.ToLower()));

        var headers = new Dictionary<string, object>();
        headers.Add(nameof(Amazon.S3.Model.HeadersCollection.CacheControl),
            $"max-age={contentCdnExpiration}, must-revalidate");

        var flag = await featureManager.IsEnabledAsync("PublicCacheOff", false);
        if (!flag)
        {
            await s3Repository.PutFileAsync(result.ToJson()!, bucketNamePublic,
            string.Format(CONTENT_REPOSITORY_PATH,
                Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(tag.ToLower()))), "json", null, null,
            headers);
        }
    }
}