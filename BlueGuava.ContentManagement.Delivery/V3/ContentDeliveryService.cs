using System.Diagnostics;
using Amazon.S3.Model;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V3;
using BlueGuava.ContentManagement.Delivery.Conversion;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.Logging;
//using BlueGuava.Integration.Nexius.Messages;
using BlueGuava.Library;
using BlueGuava.MessageQueuing;
using BlueGuava.Reporting.Messages.Entities;
using BlueGuava.Tracewind.Common.Models;
using CorrelationId;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Polly;
using Polly.Wrap;
using S3Bucket = BlueGuava.ContentManagement.Common.S3Bucket;

namespace BlueGuava.ContentManagement.Delivery.V3;

public class ContentDeliveryService : IDeliveryService
{
    private const string CONTENT_REPOSITORY_PATH = "api/v6/content/{0}/entity.json";

    private readonly ILogger<ContentDeliveryService> logger;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IConverter<Content> contentConverter;
    private readonly IMessageQueue<TraceLogMessage> traceLog;
    private readonly IS3Repository s3Repository;
    //private readonly IMessageQueue<PersistContentMessage> persistContentMessageQueue;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging;
    private readonly IMessageQueue<PlatformMetricMessage> platformMetricMessages;
    private readonly IObjectSequenceProcessor<Library.Interop.v2.Object> sequenceProcessor;
    private readonly IFeatureManager featureManager;
    private readonly ModuleInfo moduleInfo;
    private readonly AsyncPolicyWrap wrappedPolicy;

    private readonly string bucketNamePublic;
    private readonly string bucketNamePrivate;
    private readonly string contentCdnExpiration;

    private static readonly SemaphoreSlim s3Semaphore = new SemaphoreSlim(5, 5);

    public ContentDeliveryService(
        ILogger<ContentDeliveryService> logger,
        IOptionsMonitor<S3Bucket> bucketSettings,
        ICorrelationContextAccessor correlationContextAccessor,
        IConverter<Content> contentConverter,
        IMessageQueue<TraceLogMessage> traceLog,
        IS3Repository s3Repository,
        IConfiguration configuration,
        IOptions<ModuleInfo> moduleInfo,
        IOptionsMonitor<CommunicationSettings> options,
        IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging,
        IMessageQueue<PlatformMetricMessage> platformMetricMessages,
        IObjectSequenceProcessor<Library.Interop.v2.Object> sequenceProcessor,
        IFeatureManager featureManager
        //IMessageQueue<PersistContentMessage> persistContentMessageQueue
        )
    {
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
        this.contentConverter = contentConverter;
        this.traceLog = traceLog;
        this.s3Repository = s3Repository;
        this.relationshipUpdateMessaging = relationshipUpdateMessaging;
        this.moduleInfo = moduleInfo.Value;
        this.platformMetricMessages = platformMetricMessages;
        this.sequenceProcessor = sequenceProcessor;
        this.featureManager = featureManager;
        //this.persistContentMessageQueue = persistContentMessageQueue;


        bucketNamePublic = bucketSettings.CurrentValue.Contents ?? "";
        bucketNamePrivate = bucketSettings.CurrentValue.Ingest ?? "";
        contentCdnExpiration = configuration["CDN:Expiration:Content"] ?? "30";

        var bulkheadPolicy = Policy.BulkheadAsync(10, int.MaxValue);
        wrappedPolicy = bulkheadPolicy.WrapAsync(Policy.Handle<Exception>()
            .WaitAndRetryAsync(options.CurrentValue.CircuitBreakerThreshold,
                i => options.CurrentValue.CircuitBreakerDuration,
                (ex, span) => logger.LogError("Circuit breaker open for {Span} because {Message}", span, ex.Message)
            ));
    }

    public async Task ReleaseContent(Content? entity, bool recalculateCatalogs,
        CancellationToken cancellation = default)
    {
        //TODO: implement rules: when will we want to release a content (if published, if changed....)
        //var dataFeeds = await datagroupRepository.ListCollection<Feed>();

        //try { metricsCollector.IncrementSuccessCounter(ApiMethod., content.Type); } catch { }
        if (entity == null) return;

        var stopWatch = new Stopwatch();
        stopWatch.Start();

        try
        {
            var payload = await contentConverter.ConvertObject(entity);
            payload.SetProperty(Constants.CONTENT_KIND, Constants.CONTENT_KIND_FULL);

            var flag = await featureManager.IsEnabledAsync("PublicCacheOff", false);
            if (!flag)
            {
                if (entity.Published)
                {
                    var headers = new Dictionary<string, object>();
                    headers.Add(nameof(HeadersCollection.CacheControl), $"max-age={contentCdnExpiration}, must-revalidate");

                    await s3Semaphore.WaitAsync(cancellation);
                    try
                    {
                        await wrappedPolicy.ExecuteAsync(async (cancel) =>
                        {
                            await s3Repository.PutFileAsync(payload.ToJson(),
                                bucketNamePublic, //bucketSettings.CurrentValue.Contents, 
                                string.Format(CONTENT_REPOSITORY_PATH, entity.Id), "json", null, null, headers);
                        }, cancellation);
                    }
                    finally
                    {
                        s3Semaphore.Release();
                    }
                }
                else
                {
                    await s3Semaphore.WaitAsync(cancellation);
                    try
                    {
                        await wrappedPolicy.ExecuteAsync(
                            async (cancel) =>
                            {
                                await s3Repository.RemoveFileAsync(bucketNamePublic,
                                    string.Format(CONTENT_REPOSITORY_PATH, entity.Id));
                            }, cancellation);
                    }
                    finally
                    {
                        s3Semaphore.Release();
                    }
                }
            }

            await s3Semaphore.WaitAsync(cancellation);
            try
            {
                await wrappedPolicy.ExecuteAsync(async (cancel) =>
                {
                    await s3Repository.PutFileAsync(entity.ToJson() ?? string.Empty,
                        bucketNamePrivate, //bucketSettings.CurrentValue.Content,
                        string.Format(CONTENT_REPOSITORY_PATH, entity.Id), "json");
                }, cancellation);
            }
            finally
            {
                s3Semaphore.Release();
            }

            if (recalculateCatalogs)
            {
                await sequenceProcessor.AddElementToSequence(payload);

                await traceLog.Enqueue(new TraceLogMessage
                {
                    ObjectId = entity.Id.ToString(),
                    CorrelationId = entity.Id.ToString(),
                    ObjectType = (int)ObjectType.Content,
                    Description = entity.Published
                        ? $"Recalculate triggered and S3 upload success to: s3://{bucketNamePublic}/{string.Format(CONTENT_REPOSITORY_PATH, entity.Id)}"
                        : $"Recalculate triggered and S3 file is removed from: s3://{bucketNamePublic}/{string.Format(CONTENT_REPOSITORY_PATH, entity.Id)}",
                    CreatedDate = DateTime.UtcNow,
                    IpAddress = Environment.MachineName,
                    Source = moduleInfo.Name,
                    SourceVersion = moduleInfo.Version
                });
            }

            /*
                        if (entity.Type == ContentType.RemixV2 &&
                            await featureManager.IsEnabledAsync("AllowContentCacheNexius", cancellation))
                            await persistContentMessageQueue.Enqueue(new PersistContentMessage
                            {
                                ContentId = entity.Id.ToString(),
                                IsRelease = true
                            });
            */
            await SendPlatformMetricMessage(stopWatch.Elapsed);
        }
        finally
        {
            stopWatch.Stop();
        }
    }

    public async Task UnreleaseContent(Content? entity, CancellationToken cancellation = default)
    {
        if (entity == null) return;
        //try { metricsCollector.IncrementSuccessCounter(ApiMethod., content.Type); } catch { }
        var payload = await contentConverter.ConvertObject(entity);

        var flag = await featureManager.IsEnabledAsync("PublicCacheOff", false);
        if (!flag)
        {
            await s3Semaphore.WaitAsync(cancellation);
            try
            {
                await wrappedPolicy.ExecuteAsync(
                async (cancel) =>
                {
                    await s3Repository.RemoveFileAsync(bucketNamePublic, string.Format(CONTENT_REPOSITORY_PATH, entity.Id));
                }, cancellation);
            }
            finally
            {
                s3Semaphore.Release();
            }
        }


        await s3Semaphore.WaitAsync(cancellation);
        try
        {
            await wrappedPolicy.ExecuteAsync(async (cancel) =>
            {
                await s3Repository.RemoveFileAsync(bucketNamePrivate,
                    string.Format(CONTENT_REPOSITORY_PATH, entity.Id));
            }, cancellation);
        }
        finally
        {
            s3Semaphore.Release();
        }

        payload.SetProperty(Constants.CONTENT_KIND, Constants.CONTENT_KIND_FULL);

        await sequenceProcessor.AddElementToSequence(payload);

        var relationship = new DeleteRelations(entity.Id.ToString());
        await relationshipUpdateMessaging.Enqueue(relationship);

        // re-activate this block if you want to remove the cache file from nexius
        // if (entity.Type == ContentType.RemixV2 && await featureManager.IsEnabledAsync("AllowContentCacheNexius", cancellation))
        // {
        //     await persistContentMessageQueue.Enqueue(new PersistContentMessage
        //     {
        //         ContentId = entity.Id.ToString(),
        //         IsRelease = false,
        //     });
        // }

        await traceLog.Enqueue(new TraceLogMessage
        {
            ObjectId = entity.Id.ToString(),
            ObjectType = (int)ObjectType.Content,
            CorrelationId = entity.Id.ToString(),
            Description = $"Recalculate triggered and content S3 files are deleted and collection override sent",
            CreatedDate = DateTime.UtcNow,
            IpAddress = Environment.MachineName,
            Source = moduleInfo.Name,
            SourceVersion = moduleInfo.Version
        });
    }

    private async Task SendPlatformMetricMessage(TimeSpan executionDuration)
    {
        try
        {
            var payload = new List<Property>
            {
                new() { Name = "SerializationType", Value = "Content" },
                new() { Name = "ExecutionDuration", Value = Convert.ToInt64(executionDuration.TotalSeconds) },
                new() { Name = "Type", Value = "SerializationContract" },
                new() { Name = "Timestamp", Value = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff") }
            };

            var platformMetricsMessage = new PlatformMetricMessage()
            {
                Properties = payload,
                RunDate = DateTime.UtcNow,
                TimeStamp = DateTime.UtcNow
            };

            await platformMetricMessages.Enqueue(platformMetricsMessage);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentDeliveryService),
                    nameof(SendPlatformMetricMessage))
                .Log(LogLevel.Error, ex, "Sending {messageType} failed", nameof(PlatformMetricMessage));
        }
    }
}
