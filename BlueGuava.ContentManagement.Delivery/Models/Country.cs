﻿using System.Text.Json.Serialization;

namespace BlueGuava.ContentManagement.Delivery.Models;

public class Country : IdHashEntity
{
    [JsonConstructor]
    public Country(string code) : base(code)
    {
        Code = code;
    }

    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;

    public List<string> Regions { get; set; } = new();
    public List<RatingCategory> Categories { get; set; } = new();
    public List<AdvisoryInfo> Advisories { get; set; } = new();

    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
}