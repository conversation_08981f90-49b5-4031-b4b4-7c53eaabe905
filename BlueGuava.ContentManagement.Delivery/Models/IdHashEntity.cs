﻿using System.Text;
using BlueGuava.ContentManagement.Packages.Entities;
using <PERSON>rmur;

namespace BlueGuava.ContentManagement.Delivery.Models;

public abstract class IdHashEntity : IEntity
{
    protected IdHashEntity(string identity)
    {
        Id = CreateGuid(identity);
    }

    public Guid Id { get; }

    public static Guid CreateGuid(string text)
    {
        if (string.IsNullOrEmpty(text)) return default;
        var buffer = Encoding.UTF8.GetBytes(text);
        if (buffer.Length < 16)
        {
            var bytes = new byte[16];
            Array.Copy(buffer, 0, bytes, 0, buffer.Length);
            buffer = bytes;
        }
        else if (buffer.Length > 16)
        {
            using var hashAlg = MurmurHash.Create128();
            buffer = hashAlg.ComputeHash(buffer);
        }

        return new Guid(buffer);
    }
}