using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Delivery.Models.Items;
using BlueGuava.Extensions.Logging;
using BlueGuava.ItemsProcessing;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.MessageQueuing;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.Delivery.ItemProcessors;

public class ManualCatalogItemProcessor : IItemProcessor<ManualCatalogItem>
{
    private readonly ILogger<ManualCatalogItemProcessor> logger;

    private readonly IOpenSearchService openSearchService;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging;

    public ManualCatalogItemProcessor(
        ILogger<ManualCatalogItemProcessor> logger,
        IOpenSearchService openSearchService,
        IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging)
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.relationshipUpdateMessaging = relationshipUpdateMessaging;
    }

    public string Name { get; } = nameof(ManualCatalogItemProcessor);

    public async Task Process(WorkItemContext<ManualCatalogItem> workItemContext)
    {
        var objectId = Guid.Empty;
        try
        {
            var item = workItemContext.WorkItem;

            logger.LogWarning("Processing item: {@Item}", item);
            objectId = Guid.TryParse(item.Payload.GetId(), out objectId)
                ? objectId
                : throw new InvalidOperationException(
                    $"id is not guid: '{item.Payload.GetId()}'");

            var manualCatalogItems = item.Payload.Properties["FilteringRules"];
            var decisionRules = !string.IsNullOrEmpty(manualCatalogItems)
                ? JsonConvert.DeserializeObject<DecisionRules>(manualCatalogItems)
                : null;

            var orderRules = item.Payload.Properties["OrderingRules"];
            var orderingRules = !string.IsNullOrEmpty(orderRules)
                ? JsonConvert.DeserializeObject<OrderRules>(orderRules)
                : null;

            var pageSize = int.TryParse(item.Payload.Properties[Constants.PAGING_PAGE_SIZE], out var ps) ? ps : 100;

            logger.LogWarning("Search started");
            var manualContents = await openSearchService.Search(null, decisionRules, orderingRules, pageSize, 0);


            logger.LogWarning("{@ManualContents}", manualContents);
            if (manualContents.Hits.Data.Any())
            {
                var targets = new List<TargetDetails>();
                targets.AddRange(manualContents.Hits.Data.Select(d => new TargetDetails()
                {
                    TargetId = d.Id.ToString()
                    //TODO: after relationship approval development finished
                    //index =  ordering.ValueOrder.IndexOf(d.Id)
                }));

                logger.LogWarning("Relations: {Targets}", targets.ToJson());

                var relationship = new MultiRelationshipUpdate()
                {
                    Relation = CatalogRelation.Content,
                    UpdateStrategy = UpdateStrategy.Override,
                    SourceId = Guid.TryParse(item.Payload.GetId(), out var id)
                        ? id.ToString()
                        : throw new InvalidOperationException(
                            $"id is not guid: '{item.Payload.GetId()}'"),
                    Targets = targets
                };

                await relationshipUpdateMessaging.Enqueue(relationship);
            }
            else
            {
                logger.LogWarning(@"Can't find children of catalog '{Catalog}'",
                    item.Payload.Properties[Constants.CONTENT_ID]);
            }
        }
        catch (Exception ex)
        {
            // metricsCollector.IncrementErrorCounter(ApiMethod.Publish,  nameof(ManualCatalogItemProcessor));
            logger.LogError(ex, $"Error in  {nameof(ManualCatalogItemProcessor)} method {nameof(Process)}");
        }
    }
}