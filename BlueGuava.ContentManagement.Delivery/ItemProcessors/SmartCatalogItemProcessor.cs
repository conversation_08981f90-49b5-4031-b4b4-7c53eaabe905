using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Delivery.Models.Items;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.Logging;
using BlueGuava.ItemsProcessing;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.MessageQueuing;
using BlueGuava.OrderedSearchResult;
using BlueGuava.Tracewind.Common.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace BlueGuava.ContentManagement.Delivery.ItemProcessors;

public class SmartCatalogItemProcessor : IItemProcessor<SmartCatalogItem>
{
    private readonly ILogger<SmartCatalogItemProcessor> logger;
    private readonly IMessageQueue<TraceLogMessage> traceLog;
    private readonly IOpenSearchService openSearchService;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging;
    private readonly IMessageQueue<RelationshipUpdateAll> relationshipUpdateAllMessaging;
    private readonly ModuleInfo moduleInfo;

    public SmartCatalogItemProcessor(
        ILogger<SmartCatalogItemProcessor> logger,
        IOpenSearchService openSearchService,
        IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging,
        IMessageQueue<RelationshipUpdateAll> relationshipUpdateAllMessaging,
        IMessageQueue<TraceLogMessage> traceLog,
        IOptions<ModuleInfo> moduleInfo)
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.relationshipUpdateMessaging = relationshipUpdateMessaging;
        this.relationshipUpdateAllMessaging = relationshipUpdateAllMessaging;
        this.traceLog = traceLog;

        this.moduleInfo = moduleInfo.Value;
    }

    public string Name { get; } = nameof(SmartCatalogItemProcessor);

    public async Task Process(WorkItemContext<SmartCatalogItem> workItemContext)
    {
        var item = workItemContext.WorkItem;

        var objectId = Guid.Empty;

        try
        {
            objectId = Guid.TryParse(item.Payload.GetId(), out objectId)
                ? objectId
                : throw new InvalidOperationException(
                    $"id is not guid: '{item.Payload.GetId()}'");

            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = objectId.ToString(),
                ObjectType = (int)ObjectType.Catalog,
                CorrelationId = objectId.ToString(),
                Description = $"Processing Catalog: {item.ToJson()}",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });

            var manualCatalogItems = item.Payload.Properties["FilteringRules"];
            var decisionRules = !string.IsNullOrEmpty(manualCatalogItems)
                ? JsonConvert.DeserializeObject<DecisionRules>(manualCatalogItems)
                : null;

            var orderRules = item.Payload.Properties["OrderingRules"];
            var orderingRules = !string.IsNullOrEmpty(orderRules)
                ? JsonConvert.DeserializeObject<OrderRules>(orderRules)
                : null;

            var orderGroups = item.Payload.Properties["OrderingGroups"];
            var orderingGroups = !string.IsNullOrEmpty(orderGroups)
                ? JsonConvert.DeserializeObject<OrderingGroups>(orderGroups)
                : null;

            var pageSize = int.TryParse(item.Payload.Properties[Constants.PAGING_PAGE_SIZE], out var ps) ? ps : 100;
            var index = 0;

            if (orderingGroups != null)
                await ProcessOrderingGroups(item, objectId, decisionRules, orderingGroups, pageSize, index);

            // generate default children result without ordering
            await ProcessDecisionRules(item, objectId, decisionRules, pageSize, index);
        }
        catch (Exception ex)
        {
            await LogException(objectId, ex);
        }
    }

    private async Task ProcessDecisionRules(SmartCatalogItem item, Guid objectId, DecisionRules? decisionRules,
        int pageSize, int index)
    {
        var searchResult = await openSearchService.Search(null, decisionRules, null, pageSize, index);
        await RegularMultiRelationship(searchResult.Hits, objectId, item, UpdateStrategy.Override,
            CatalogRelation.Content, index);
        await relationshipUpdateMessaging.Enqueue(new RelationshipRefresh()
        {
            Relation = CatalogRelation.Content,
            SourceId = objectId.ToString()
        }, 20);
        await traceLog.Enqueue(new TraceLogMessage
        {
            ObjectId = objectId.ToString(),
            ObjectType = (int)ObjectType.Catalog,
            CorrelationId = objectId.ToString(),
            Description =
                $"Registered Relations ({searchResult.Hits?.TotalCount ?? 0}) sent to Collection service - as a default result without any ordering",
            CreatedDate = DateTime.UtcNow,
            IpAddress = Environment.MachineName,
            Source = moduleInfo.Name,
            SourceVersion = moduleInfo.Version
        });

        //get all pages again
        var pageIndex = 0;
        //override current collections, save first page
        var hits = 0;
        while (searchResult.Hits?.TotalCount > hits)
        {
            searchResult = await openSearchService.Search(null, decisionRules, null, pageSize, pageIndex++);
            hits += searchResult.Hits.Data.Count();

            //append other pages
            index = await RegularMultiRelationshipAll(searchResult.Hits, objectId, item, UpdateStrategy.Append,
                CatalogRelation.ContentAll, index);
        }

        await traceLog.Enqueue(new TraceLogMessage
        {
            ObjectId = objectId.ToString(),
            ObjectType = (int)ObjectType.Catalog,
            CorrelationId = objectId.ToString(),
            Description =
                $"Registered Relations ({searchResult.Hits?.TotalCount ?? 0}) sent to Collection service - as a ALL result without any ordering",
            CreatedDate = DateTime.UtcNow,
            IpAddress = Environment.MachineName,
            Source = moduleInfo.Name,
            SourceVersion = moduleInfo.Version
        });

        await relationshipUpdateAllMessaging.Enqueue(new RelationshipRefreshAll()
        {
            Relation = CatalogRelation.ContentAll,
            SourceId = objectId.ToString()
        }, 20);
    }

    private async Task ProcessOrderingGroups(SmartCatalogItem item, Guid objectId, DecisionRules? decisionRules,
        OrderingGroups orderingGroups, int pageSize, int index)
    {
        var orderingGroupTrigger = item.Payload.Properties["OrderingGroupTrigger"];
        var doNotConsiderTimeBased = item.Payload.Properties["DoNotConsiderTimeBased"];

        if (!string.IsNullOrEmpty(doNotConsiderTimeBased) && bool.TryParse(doNotConsiderTimeBased, out var onlyEmpty) &&
            onlyEmpty)
            orderingGroups = new OrderingGroups(orderingGroups.Where(x => x.RefreshPeriod == 0));

        if (!string.IsNullOrEmpty(orderingGroupTrigger) &&
            int.TryParse(orderingGroupTrigger, out var orderingGroupTriggerIndex) && orderingGroupTriggerIndex > 0)
            orderingGroups = new OrderingGroups(orderingGroups.Where(x => x.Index == orderingGroupTriggerIndex));

        foreach (var orderingGroup in orderingGroups)
            try
            {
                var pageSizeOg = orderingGroup.LimitItem > 0 ? orderingGroup.LimitItem : pageSize;
                var orderRulesOg = new OrderRules(orderingGroup.OrderRules);
                var searchResultOg =
                    await openSearchService.Search(null, decisionRules, orderRulesOg, pageSizeOg, index);
                var sourceId = $"{objectId}_{orderingGroup.Name.Replace(" ", "").ToLower()}";
                await RegularMultiRelationship(searchResultOg.Hits, objectId, sourceId, item, UpdateStrategy.Override,
                    CatalogRelation.Content, index);
                await relationshipUpdateMessaging.Enqueue(new RelationshipRefresh()
                {
                    Relation = CatalogRelation.Content,
                    SourceId = sourceId
                }, 20);
                await traceLog.Enqueue(new TraceLogMessage
                {
                    ObjectId = objectId.ToString(),
                    ObjectType = (int)ObjectType.Catalog,
                    CorrelationId = objectId.ToString(),
                    Description =
                        $"Registered Relations ({searchResultOg.Hits?.TotalCount ?? 0}) sent to Collection service - Ordering Group: {orderingGroup.Name}",
                    CreatedDate = DateTime.UtcNow,
                    IpAddress = Environment.MachineName,
                    Source = moduleInfo.Name,
                    SourceVersion = moduleInfo.Version
                });
            }

            catch (Exception ex)
            {
                await LogException(objectId, ex);
            }
    }

    private async Task LogException(Guid objectId, Exception ex)
    {
        if (objectId != Guid.Empty)
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = objectId.ToString(),
                ObjectType = (int)ObjectType.Catalog,
                CorrelationId = objectId.ToString(),
                Description = ex.Message,
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });
        logger.LogError(ex, $"Error in  {nameof(SmartCatalogItemProcessor)} method {nameof(Process)}");
    }

    private async Task<int> RegularMultiRelationship(SearchResult<Content> searchResult, Guid objectId, string sourceId,
        SmartCatalogItem item, UpdateStrategy updateStrategy, CatalogRelation relation, int index)
    {
        var relationship = new MultiRelationshipUpdate();
        if (searchResult.Data.Any())
        {
            var targets = new List<TargetDetails>();
            targets.AddRange(searchResult.Data.Select(d => new TargetDetails()
            {
                TargetId = d.Id.ToString(),
                Index = index++
            }));

            relationship = new MultiRelationshipUpdate()
            {
                Relation = relation,
                UpdateStrategy = updateStrategy,
                SourceId = sourceId,
                Targets = targets
            };
        }
        else
        {
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = objectId.ToString(),
                ObjectType = (int)ObjectType.Catalog,
                CorrelationId = objectId.ToString(),
                Description = $"Can't find children of catalog '{item.Payload.Properties[Constants.CONTENT_ID]}'",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });

            relationship = new MultiRelationshipUpdate()
            {
                Relation = relation,
                UpdateStrategy = UpdateStrategy.Override,
                SourceId = sourceId,
                Targets = new List<TargetDetails>()
            };
        }

        await relationshipUpdateMessaging.Enqueue(relationship);

        return index;
    }

    private async Task<int> RegularMultiRelationship(SearchResult<Content> searchResult, Guid objectId,
        SmartCatalogItem item, UpdateStrategy updateStrategy, CatalogRelation relation, int index)
    {
        var relationship = new MultiRelationshipUpdate();
        if (searchResult.Data.Any())
        {
            var targets = new List<TargetDetails>();
            targets.AddRange(searchResult.Data.Select(d => new TargetDetails()
            {
                TargetId = d.Id.ToString(),
                Index = index++
            }));
            relationship = new MultiRelationshipUpdate()
            {
                Relation = relation,
                UpdateStrategy = updateStrategy,
                SourceId = objectId.ToString(),
                Targets = targets
            };
        }
        else
        {
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = objectId.ToString(),
                ObjectType = (int)ObjectType.Catalog,
                CorrelationId = objectId.ToString(),
                Description = $"Can't find children of catalog '{item.Payload.Properties[Constants.CONTENT_ID]}'",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });
            relationship = new MultiRelationshipUpdate()
            {
                Relation = relation,
                UpdateStrategy = UpdateStrategy.Override,
                SourceId = Guid.TryParse(item.Payload.GetId(), out var id)
                    ? id.ToString()
                    : throw new InvalidOperationException(
                        $"id is not guid: '{item.Payload.GetId()}'"),
                Targets = new List<TargetDetails>()
            };
        }

        await relationshipUpdateMessaging.Enqueue(relationship);

        return index;
    }

    private async Task<int> RegularMultiRelationshipAll(SearchResult<Content> searchResult, Guid objectId,
        SmartCatalogItem item, UpdateStrategy updateStrategy, CatalogRelation relation, int index)
    {
        var relationship = new MultiRelationshipUpdateAll();
        if (searchResult.Data.Any())
        {
            var targets = new List<TargetDetails>();
            targets.AddRange(searchResult.Data.Select(d => new TargetDetails()
            {
                TargetId = d.Id.ToString(),
                Index = index++
            }));
            relationship = new MultiRelationshipUpdateAll()
            {
                Relation = relation,
                UpdateStrategy = updateStrategy,
                SourceId = objectId.ToString(),
                Targets = targets
            };
        }
        else
        {
            await traceLog.Enqueue(new TraceLogMessage
            {
                ObjectId = objectId.ToString(),
                ObjectType = (int)ObjectType.Catalog,
                CorrelationId = objectId.ToString(),
                Description = $"Can't find children of catalog '{item.Payload.Properties[Constants.CONTENT_ID]}'",
                CreatedDate = DateTime.UtcNow,
                IpAddress = Environment.MachineName,
                Source = moduleInfo.Name,
                SourceVersion = moduleInfo.Version
            });
            relationship = new MultiRelationshipUpdateAll()
            {
                Relation = relation,
                UpdateStrategy = UpdateStrategy.Override,
                SourceId = Guid.TryParse(item.Payload.GetId(), out var id)
                    ? id.ToString()
                    : throw new InvalidOperationException(
                        $"id is not guid: '{item.Payload.GetId()}'"),
                Targets = new List<TargetDetails>()
            };
        }

        await relationshipUpdateAllMessaging.Enqueue(relationship);

        return index;
    }
}