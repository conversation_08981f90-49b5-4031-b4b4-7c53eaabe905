using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Services.V3;
using BlueGuava.ItemsProcessing;
using Microsoft.Extensions.Logging;
using Action = BlueGuava.ContentManagement.Common.Models.Action;

namespace BlueGuava.ContentManagement.Delivery.ItemProcessors;

public class ContentDeliveryProcessor : IItemProcessor<ContentReleaseTrigger>
{
    private readonly ILogger<ContentDeliveryProcessor> logger;
    private readonly IDeliveryService deliveryService;

    public ContentDeliveryProcessor(
        ILogger<ContentDeliveryProcessor> logger,
        IDeliveryService deliveryService)
    {
        this.logger = logger;
        this.deliveryService = deliveryService;
    }

    public string Name { get; } = nameof(ContentDeliveryProcessor);

    public async Task Process(WorkItemContext<ContentReleaseTrigger> workItemContext)
    {
        try
        {
            var item = workItemContext.WorkItem;

            switch (item.Action)
            {
                case Action.Release:
                    await deliveryService.ReleaseContent(item.Entity, item.RecalculateCatalogs);
                    break;
                case Action.UnRelease:
                    await deliveryService.UnreleaseContent(item.Entity);
                    break;
                default:
                    throw new ArgumentException("Invalid action.");
            }
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "{Service} {Method}", nameof(ContentDeliveryProcessor), nameof(Process));
        }
    }
}