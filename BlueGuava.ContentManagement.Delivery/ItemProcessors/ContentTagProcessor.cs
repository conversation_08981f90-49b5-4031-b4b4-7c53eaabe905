﻿using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V3;
using BlueGuava.ContentManagement.Delivery.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ItemsProcessing;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.Library.Common.Enums;
using Microsoft.Extensions.Logging;

namespace BlueGuava.ContentManagement.Delivery.ItemProcessors;

public class ContentTagProcessor : IItemProcessor<ContentTag>
{
    private readonly ILogger<ContentTagProcessor> logger;
    private readonly IOpenSearchService openSearchService;
    private readonly IContentTagService contentTagService;

    public ContentTagProcessor(
        ILogger<ContentTagProcessor> logger,
        IOpenSearchService openSearchService,
        IContentTagService contentTagService
    )
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.contentTagService = contentTagService;
    }

    public string Name { get; } = nameof(ContentTagProcessor);

    public async Task Process(WorkItemContext<ContentTag> workItemContext)
    {
        var objectId = Guid.Empty;
        try
        {
            //await Task.Delay(TimeSpan.FromSeconds(10));
            var contentTag = workItemContext.WorkItem;

            var decisionRules = new DecisionRules();
            decisionRules.Add(new DecisionRule()
            {
                PropertyName = contentTag.PropertyName,
                PropertyValue = contentTag.PropertyValue,
                Condition = PropertyCondition.Equal
            });

            var orderingRules = new OrderRules();
            var pageSize = 100;
            var index = 0;

            var searchResult = await openSearchService.Search(null, decisionRules, orderingRules, pageSize, index);
            List<Content> entity = new();
            if (searchResult.Hits?.Data?.Any() ?? false)
            {
                entity.AddRange(searchResult.Hits.Data.Where(s => s.Published).ToList());
                while (searchResult.Hits.TotalCount > pageSize)
                {
                    searchResult =
                        await openSearchService.Search(null, decisionRules, orderingRules, pageSize, index++);
                    pageSize += searchResult.Hits.Data.Count();

                    entity.AddRange(searchResult.Hits.Data.Where(s => s.Published).ToList());
                }
            }

            await contentTagService.ReleaseContentTag(entity, contentTag.PropertyValue);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error in  {nameof(ContentTagProcessor)} method {nameof(Process)}");
        }
    }
}