<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Common\BlueGuava.ContentManagement.Common.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="BlueGuava.Library.PropertyCollection.Extensions" Version="8.1.4" />
        <PackageReference Include="BlueGuava.Library.Repository.Http" Version="8.1.4" />
        <PackageReference Include="BlueGuava.Library.UrlTemplating" Version="8.1.4" />
        <PackageReference Include="murmurhash" Version="1.0.3"/>
    </ItemGroup>

</Project>
