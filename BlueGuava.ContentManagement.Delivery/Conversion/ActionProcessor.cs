﻿using System.Text;
using BlueGuava.Collections.Common;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.MarkerManagement.Models.Abstraction.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Action = BlueGuava.Library.Interop.v2.Action;
using Object = BlueGuava.Library.Interop.v2.Object;

namespace BlueGuava.ContentManagement.Delivery.Conversion;

public class ActionProcessor : IActions
{
    private const string OBJECT_REPOSITORY_PATH = "{0}/v6/{1}/{2}/entity.json";
    private readonly IConfiguration configuration;
    private readonly IUrlTemplating urlTemplating;
    private readonly string cdnUrl;

    public ActionProcessor(IConfiguration configuration,
        IOptionsMonitor<CdnSettings> cdnSettings,
        IUrlTemplating urlTemplating)
    {
        this.configuration = configuration;
        this.urlTemplating = urlTemplating;
        cdnUrl = cdnSettings.CurrentValue.DefaultUrl ?? configuration["Settings:CdnBaseUrl"] ?? "";
    }

    public void PropagateActions(Object target, Content content,
        List<BlueGuava.Library.Common.BusinessEntities.Action>? actions, Properties? filter = null)
    {
        if ((actions?.Count ?? 0) <= 0) return;

        var items = actions?.Where(x => !string.IsNullOrEmpty(x?.Name) && x?.Name?.ToLower() != "navigation")?.ToList();

        if ((items?.Count ?? 0) <= 0) return;

        foreach (var item in items)
        {
            if (!item.Visible) continue;
            if (item.DisplayingRules?.Validate(target) == false) continue;
            if (target.HasAction(item.Name)) continue;

            var action = item.ToV2Interop();
            if (string.IsNullOrEmpty(action.Url)) action.Url = GetDefaultUrl(item.Name, target);
            if (string.IsNullOrEmpty(action.Url)) action.Url = GetRelationUrl(item.Name);
            if (string.IsNullOrEmpty(action.Url)) action.Url = GetTagUrl(item.Name);
            if (string.IsNullOrEmpty(action.Url)) action.Url = GetMarkerUrl(item.Name);

            else if (string.Equals(action.Url, "<self>")) action.Url = GetSelfUrl(target);
            action.Url = FillPlaceholders(action.Url, content, action);
            target.SetAction(action);
        }
    }

    private string GetDefaultUrl(string name, Object target)
    {
        return name.ToLowerInvariant() switch
        {
            "menu" => "{urlScheme}://{catalogName}",
            "navigation" => GetSelfUrl(target),
            "children" => GetChildrenUrl(target),
            "watchlist:remove" => "{collectionUrl}/api/v2.0/Relation/Playlist/{contentId}",
            "watchlist:add" => "{collectionUrl}/api/v2.0/Relation/Playlist/{contentId}",
            "like" => "{collectionUrl}/api/v2.0/Relation/Like/{contentId}",
            "dislike" => "{collectionUrl}/api/v2.0/Relation/Dislike/{contentId}",
            "content:bookmark" => "{collectionUrl}/Interop/ContentBookmark",
            "collections:review" => "{collectionUrl}/Interop/Discussion",
            "authorize:playback" => "{authzApiUrl}api/v1.0/Authorize",
            "content:rating" => "{collectionUrl}/Interop/ContentRating",
            "content:purchase" => "{orderApiUrl}api/v1.0/OrderMerch",
            _ => string.Empty
        };
    }

    private static string GetSelfUrl(Object target)
    {
        //public/v6/catalog/{0}/entity.json
        return (target.Properties?[Constants.CONTENT_KIND] ?? string.Empty) switch
        {
            Constants.CONTENT_KIND_REF => // 'ref' only comes from content svc
                "{objectCacheUrl}/api/{vvvv}/content/{contentId}/entity.json",
            // all other are probably some kind of catalog
            _ => "{objectCacheUrl}/api/{vvvv}/catalog/{catalogId}/entity.json"
        };
    }

    private static string GetChildrenUrl(Object target)
    {
        return "{objectCacheUrl}/api/{vvvv}/catalog/children/{catalogId}/entity.json";
    }

    private static string GetRelationUrl(string name)
    {
        if (string.IsNullOrEmpty(name) || !name.Contains("Relationship:Content:") ||
            !Enum.TryParse(name.Split("Relationship:Content:")[1], out ContentRelation rel))
            return string.Empty;

        return
            "{objectCacheUrl}/api/{vvvv}/content/{contentId}/children/" + rel.ToString().ToLower() + "/entity.json";
    }

    private static string GetTagUrl(string name)
    {
        if (string.IsNullOrEmpty(name) || !name.Contains("Content:Genre:"))
            return string.Empty;

        var genre = name.Split("Content:Genre:")[1];
        return
            "{objectCacheUrl}/api/{vvvv}/content/labels/" +
            Convert.ToBase64String(Encoding.ASCII.GetBytes(genre.ToLower())) + "/children/content/entity.json";
    }

    private static string GetMarkerUrl(string name)
    {
        if (string.IsNullOrEmpty(name) || !name.Contains("Marker:Content:") ||
            !Enum.TryParse(name.Split("Marker:Content:")[1], out MarkerType markerType))
            return string.Empty;

        return
            "{objectCacheUrl}/api/{vvvv}/content/{contentId}/markers/" + markerType.ToString() + "/entity.json";
    }

    private string FillPlaceholders(string? url, Content entity, Action? action)
    {
        return urlTemplating.FillPlaceholders(url,
            new Dictionary<string, Func<string?>>(StringComparer.OrdinalIgnoreCase)
            {
                ["vvvv"] = () => "v6",
                ["contentId"] = () => entity.Id.ToString(),
                //["actionName"] = () => Normalize(action?.ActionName),
                ["urlScheme"] = () => configuration["Settings:UrlScheme"],
                ["collectionUrl"] = () => configuration["Console:Endpoints:Collections"],
                ["authzApiUrl"] = () => configuration["Services:AuthZ"],
                ["markersUrl"] = () => configuration["Services:MarkerManagement"],
                ["orderApiUrl"] = () => configuration["Services:Reward"],
                ["contentManagementApiUrl"] = () => configuration["Services:ContentManagement"],
                ["objectCacheUrl"] = () => cdnUrl
            });
    }

    public Object ConvertActionObjects(string[] contents)
    {
        var result = new Object();
        var index = 0;
        foreach (var content in contents)
        {
            var child = new Object();
            child.Actions.SetAction(new Action()
            {
                Url = string.Format(OBJECT_REPOSITORY_PATH, $"{cdnUrl}/api", "content", content),
                HttpMethod = "GET",
                ActionName = "Navigation"
            });
            child.SetProperty(Constants.CONTENT_ID, content);
            child.SetProperty(Constants.CONTENT_TYPE, "Content");
            child.SetProperty(Constants.CONTENT_INDEX, index++);
            result.Children.Add(child);
        }

        return result;
    }
}