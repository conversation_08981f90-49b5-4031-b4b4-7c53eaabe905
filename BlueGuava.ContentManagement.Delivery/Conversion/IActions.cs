﻿using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Library.Common.BusinessEntities;
using Object = BlueGuava.Library.Interop.v2.Object;

namespace BlueGuava.ContentManagement.Delivery.Conversion;

public interface IActions
{
    void PropagateActions(Object target, Content content,
        List<BlueGuava.Library.Common.BusinessEntities.Action>? actions, Properties? filter = null);

    Object ConvertActionObjects(string[] contents);
}