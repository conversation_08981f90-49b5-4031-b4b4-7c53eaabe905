using BlueGuava.Collections.Common;
using BlueGuava.ContentManagement.Delivery.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.Logging;
using BlueGuava.HttpRepository;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.MarkerManagement.Models.Abstraction.Enums;
using CorrelationId;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Constants = BlueGuava.Library.Constants;
using Object = BlueGuava.Library.Interop.v2.Object;

namespace BlueGuava.ContentManagement.Delivery.Conversion;

public sealed class ContentConverter : IConverter<Content>
{
    private readonly string serviceVersion;
    private readonly IHttpRepository countryRepository;
    private readonly IConfiguration configuration;
    private readonly IActions actionProcessor;
    private readonly ILogger<ContentConverter> logger;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    private Actions GeneralActions = new();
    private Actions LiveActions = new();

    public ContentConverter(
        IConfiguration configuration,
        IHttpRepositoryProvider httpRepositoryProvider,
        IActions actionProcessor,
        IOptions<ModuleInfo> moduleInfo,
        ILogger<ContentConverter> logger,
        ICorrelationContextAccessor correlationContextAccessor)
    {
        this.configuration = configuration;
        this.actionProcessor = actionProcessor;
        this.logger = logger;
        this.correlationContextAccessor = correlationContextAccessor;
        countryRepository = httpRepositoryProvider.CreateHttpRepository("CountryManagement");

        serviceVersion = moduleInfo?.Value?.Version ?? "1.0";
        AssembleGeneralActions();
        AssembleLiveActions();
    }

    private void AssembleGeneralActions()
    {
        foreach (var name in Enum.GetNames(typeof(ContentRelation)))
            GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
            {
                Name = $"Relationship:Content:{name}"
            });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Watchlist:Remove",
            HttpMethod = "DELETE"
        });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Watchlist:Add",
            HttpMethod = "POST"
        });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Like",
            HttpMethod = "POST"
        });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Dislike",
            HttpMethod = "POST"
        });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Collections:Review",
            Index = 0
        });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Content:Bookmark"
        });

        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Content:Rating"
        });
        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Name = $"Content:Purchase"
        });

        var markerTypes = Enum.GetValues(typeof(MarkerType))
            .Cast<MarkerType>()
            .Where(mt => !mt.ToString().Contains("Tag"));

        foreach (var name in markerTypes)
            GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
            {
                Name = $"Marker:Content:{name}"
            });
    }

    private void AssembleLiveActions()
    {
        GeneralActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
        {
            Url = "",
            Name = $"Authorize:Playback",
            HttpMethod = "POST"
        });
    }

    public async Task<Object> ConvertObject(Content? content)
    {
        try
        {
            var result = new Object();
            if (content == null) return result;

            ReInitializeLiveActions();

            ConvertBaseData(content, result);
            ConvertCategorization(content, result);
            ConvertCredits(content, result);

            ConvertAssets(content, result);
            ConvertDesign(content, result);

            await ConvertExhibition(content, result);

            var secret = configuration["ContentToken:Secret"];

            if (!string.IsNullOrEmpty(secret))
            {
                result.Properties.SetProperty(Constants.CONTENT_TOKEN, content.GetToken().Encode(secret));
                foreach (var item in GeneralActions)
                {
                    LiveActions.Add(item);
                }

                actionProcessor.PropagateActions(result, content, LiveActions);
            }

            result.Version = serviceVersion;
            result.TimeStamp = DateTime.UtcNow.ToString("o");

            return result;
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(ContentConverter), nameof(ConvertObject))
                .Log(LogLevel.Error, ex, "Content: {Content}", content.ToJson());
            throw;
        }
    }

    private void ReInitializeLiveActions()
    {
        LiveActions = new Actions();
    }

    /// <summary>
    /// Converts the base data into property bag properties of a <see cref="Content"/>
    /// </summary>
    /// <param name="content"><see cref="Content"/></param>
    /// <param name="result"><see cref="Object"/></param>
    private static void ConvertBaseData(Content content, Object result)
    {
        result.SetProperty(Constants.CONTENT_ID, content.Id);
        result.SetProperty(Constants.CONTENT_TITLE, content.OriginalTitle);
        result.SetProperty(Constants.CONTENT_COLOR, content.Color, true);

        result.SetProperty(Constants.CONTENT_TYPE, "Content");
        result.SetProperty(Constants.CONTENT_SUBTYPE, $"{content.Type}");

        result.SetProperty(Constants.CONTENT_DURATION, content.Duration, true);
        result.SetProperty(Constants.CONTENT_RELEASEDATE, content.ReleaseDate, true);
        result.SetProperty(Constants.CONTENT_CREATEDDATE, content.CreatedDate, true);
        result.SetProperty(Constants.CONTENT_LAST_MODIFIED, content.LastModifiedDate, true);
        result.SetProperty(Constants.CONTENT_PUBLISHEDDATE, content.PublishedDate, true);

        result.SetProperty(Constants.CONTENT_EXTERNALID, content.ExternalId, true);
        result.SetProperty(Constants.CONTENT_REFERENCEID, content.ReferenceId, true);
        result.SetProperty(Constants.CONTENT_ORIGINAL_LANGUAGE, content.OriginalLanguage, true);
        result.SetProperty(Constants.CONTENT_ORIGINAL_TITLE, content.OriginalTitle, true);
        result.SetProperty(Constants.CONTENT_OWNER_ID, content.OwnerId);

        result.SetProperty(Constants.CONTENT_ALLOW_DOWNLOAD, content.Downloadable, true);
        result.SetProperty(Constants.CONTENT_ALLOW_MINTING, content.AllowMinting, true);
        result.SetProperty(Constants.CONTENT_ALLOW_EMAIL_NOTIFICATION, content.AllowEmailNotification, true);
        result.SetProperty(Constants.CONTENT_ALLOW_REMIX, content.AllowRemix, true);
        result.SetProperty(Constants.CONTENT_ALLOW_COMMENT, content.AllowComments, true);
        result.SetProperty(Constants.CONTENT_ALLOW_RATINGS, content.AllowUserRating, true);
        result.SetProperty(Constants.CONTENT_ALLOW_CHAT, content.AllowChat, true);
        result.SetProperty(Constants.CONTENT_ALLOW_SIDESHOW, content.AllowSideshow, true);
        result.SetProperty(Constants.CONTENT_ALLOW_LYRICS, content.AllowLyrics, true);
        result.SetProperty(Constants.CONTENT_ALLOW_UPCOMING, content.AllowUpcoming, true);

        result.SetProperty("Point", Constants.PRICE_AMOUNT, content.InternalPrice);
        result.SetProperty("Point", Constants.PRICE_CURRENCY, "PTS");
        result.SetProperty("Coin", Constants.PRICE_AMOUNT, content.TokenPrice);
        result.SetProperty("Coin", Constants.PRICE_CURRENCY, content.TokenCurrency);

        result.SetProperty(Constants.CONTENT_VISIBILITY, $"{content.Visibility}");
        result.SetProperty(Constants.CONTENT_PUBLISHING_RULE, $"{content.PublishingRule}");
        result.SetProperty(Constants.CONTENT_NOTIFICATION, $"{content.Notification}");

        content.Properties ??= new Dictionary<string, string>();
        // add extra properties (do not overwrite)
        foreach (var entry in content.Properties)
            result.TryAddProperty(entry.Key, entry.Value, true);
    }

    /// <summary>
    /// Converts the assets into property bag properties of a <see cref="Content"/>
    /// </summary>
    /// <param name="content"><see cref="Content"/></param>
    /// <param name="result"><see cref="Object"/></param>
    private void ConvertAssets(Content content, Object result)
    {
        content.Assets ??= new List<Asset>();
        // image localizations (landscape/portrait/square)
        foreach (var entry in content.Assets.Where(x => !x.IsDeleted && !string.IsNullOrEmpty(x.PublicUrl)))
        {
            var propertyName = entry.SubType switch
            {
                SubType.ScrubbingImages => Constants.CONTENT_THUMBNAILS_VTT,
                SubType.Landscape => Constants.CONTENT_IMAGE_LANDSCAPE_URL,
                SubType.Portrait => Constants.CONTENT_IMAGE_PORTRAIT_URL,
                SubType.Square => Constants.CONTENT_IMAGE_SQUARE_URL,
                SubType.Spectogram_1080x2340 => entry.Type == AssetType.Video
                    ? Constants.CONTENT_IMAGE_SPECTOGRAM_URL
                    : Constants.CONTENT_IMAGE_SPECTOGRAM_ULTRAWIDE_URL,
                SubType.Spectogram_1920x1080 => entry.Type == AssetType.Video
                    ? Constants.CONTENT_IMAGE_SPECTOGRAM_URL
                    : Constants.CONTENT_IMAGE_SPECTOGRAM_HD_URL,
                SubType.Waveform => entry.Type == AssetType.Text
                    ? Constants.CONTENT_JSON_WAVEFORM_URL
                    : Constants.CONTENT_IMAGE_WAVEFORM_URL,
                _ => null
            };

            if (string.IsNullOrEmpty(propertyName)) continue;
            SetPublicUrl(result, entry, propertyName);
        }

        SetStreamTypes(content, result);
    }

    private void SetStreamTypes(Content content, Object result)
    {
        // available stream types (HLS, DASH)
        foreach (var entry in DetectExposedStreams(content.Assets))
        {
            if (entry.Value.Length == 0) continue;
            var prefix = GetGlobalizationPrefix(entry.Key);
            result.SetProperty(prefix + Constants.CONTENT_FORMATS, string.Join(",", entry.Value));
        }
    }

    private static void SetPublicUrl(Object result, Asset? entry, string? propertyName)
    {
        var prefix = GetGlobalizationPrefix(entry.Locale);
        result.SetProperty(prefix + propertyName, entry.PublicUrl, true);

        if (entry.SubType == SubType.Landscape) // add thumbnails url fallback value
            result.SetProperty(prefix + Constants.CONTENT_IMAGE_THUMBNAIL_URL, entry.PublicUrl, true);
    }

    /// <summary>
    /// Converts the design items into property bag properties of a <see cref="Content"/>:
    /// <list type="bullet">
    ///     <item>
    ///         <description><see cref="ConvertDesign"/></description>
    ///     </item>
    ///     <item>
    ///         <description><see cref="MusimapMood"/></description>
    ///     </item>
    /// </list>
    /// </summary>
    /// <param name="content"><see cref="Content"/></param>
    /// <param name="result"><see cref="Object"/></param>
    private static void ConvertDesign(Content content, Object result)
    {
        // content design
        var lightDesign = content.Themes?.GetValueOrDefault(DesignTypes.LightDesign);
        result.SetProperty(Constants.CONTENT_DESIGN_LIGHT_FONTCOLOR_MAIN, lightDesign?.MainTextColor, true);
        result.SetProperty(Constants.CONTENT_DESIGN_LIGHT_FONTCOLOR_SHORTINFO, lightDesign?.ShortInfoColor, true);
        result.SetProperty(Constants.CONTENT_DESIGN_LIGHT_FONTCOLOR_ARTIST, lightDesign?.ArtistTextColor, true);
        result.SetProperty(Constants.CONTENT_DESIGN_LIGHT_BACKGROUND_COLOR, lightDesign?.BackgroundColor, true);

        var darkDesign = content.Themes?.GetValueOrDefault(DesignTypes.LightDesign);
        result.SetProperty(Constants.CONTENT_DESIGN_DARK_FONTCOLOR_MAIN, darkDesign?.MainTextColor, true);
        result.SetProperty(Constants.CONTENT_DESIGN_DARK_FONTCOLOR_SHORTINFO, darkDesign?.ShortInfoColor, true);
        result.SetProperty(Constants.CONTENT_DESIGN_DARK_FONTCOLOR_ARTIST, darkDesign?.ArtistTextColor, true);
        result.SetProperty(Constants.CONTENT_DESIGN_DARK_BACKGROUND_COLOR, darkDesign?.BackgroundColor, true);
    }

    /// <summary>
    /// Converts the <see cref="Credit"/> into property bag properties of a <see cref="Content"/>
    /// </summary>
    /// <param name="content"><see cref="Content"/></param>
    /// <param name="result"><see cref="Object"/></param>
    private static void ConvertCredits(Content content, Object result)
    {
        if (content.Credits == null) return;
        // add Cast & Crew
        foreach (var credit in content.Credits)
        {
            foreach (var role in credit.Roles)
            {
                var propName = $"Content:Role:{role}";
                var property = new Library.Interop.v2.Property(propName, credit.Name);
                if (role == Role.Cast) property.CollectionName = credit.Character ?? string.Empty;
                // to add multiple values for the same name we must use 'List.Add'
                result.Properties.Add(property);
            }
        }
    }

    /// <summary>
    /// Converts the availability items into property bag properties of a <see cref="Content"/>:
    /// <list type="bullet">
    ///     <item>
    ///         <description><see cref="Availability"/></description>
    ///     </item>
    ///     <item>
    ///         <description><see cref="Localization"/></description>
    ///     </item>
    ///     <item>
    ///         <description><see cref="WhereToWatch"/></description>
    ///     </item>
    /// </list>
    /// </summary>
    /// <param name="content"><see cref="Content"/></param>
    /// <param name="result"><see cref="Object"/></param>
    private async Task ConvertExhibition(Content content, Object result)
    {
        // 'Regional:{CC}:Available:...' entries
        // 'Regional:{CC}:Content:Rating:...' entries
        content.ExhibitionWindow ??= new Dictionary<string, Availability>();
        foreach (var kvp in content.ExhibitionWindow)
        {
            var country = kvp.Key;
            var availability = kvp.Value;
            var prefix = $"Regional:{country}:";
            if (country == "--") prefix = string.Empty;

            result.SetProperty(prefix + Constants.CONTENT_AVAILABLE_FROM, availability.AvailableFrom, true);
            result.SetProperty(prefix + Constants.CONTENT_AVAILABLE_UNTIL, availability.AvailableUntil, true);

            var ratingKey = prefix + Constants.CONTENT_RATING;
            var parental = await FindParental(country, availability.AgeLimit);
            result.SetProperty($"{ratingKey}:Value", availability.AgeLimit, true);
            result.SetProperty($"{ratingKey}:MinAge", parental?.MinAge, true);
            result.SetProperty($"{ratingKey}:Name", parental?.Name, true);
            result.SetProperty($"{ratingKey}:IconUrl", parental?.IconUrl, true);
            result.SetProperty($"{ratingKey}:Advisory", availability.AdvisoryCodes, true);
        }

        // text localizations (title, info, description)
        content.Localizations ??= new Dictionary<string, Localization>();
        foreach (var entry in content.Localizations)
        {
            var prefix = GetGlobalizationPrefix(entry.Key);
            result.SetProperty(prefix + Constants.CONTENT_TITLE, entry.Value.Name, true);
            result.SetProperty(prefix + Constants.CONTENT_INFO, entry.Value.ShortInfo, true);
            result.SetProperty(prefix + Constants.CONTENT_DESCRIPTION, entry.Value.Description, true);
        }

        // where to watch: HBO Max -- https://hbomax.com/...
        content.WhereToWatch ??= new List<WhereToWatch>();
        foreach (var watch in content.WhereToWatch) // watch URLs collection is the provider's name
            result.SetProperty(watch.ProviderName ?? string.Empty, Constants.CONTENT_EXTERNAL_WATCHURL, watch.WatchUrl,
                true);
    }

    /// <summary>
    /// Converts the categorization items into property bag properties of a <see cref="Content"/>:
    /// <list type="bullet">
    ///     <item>
    ///         <description><see cref="CategorizationType"/></description>
    ///     </item>
    /// </list>
    /// </summary>
    /// <param name="content"><see cref="Content"/></param>
    /// <param name="result"><see cref="Object"/></param>
    private void ConvertCategorization(Content content, Object result)
    {
        if (content.Labels == null) return;

        // various categorizations (categories, genre, etc.)
        foreach (var cat in content.Labels)
        {
            var propertyName = cat.Key switch
            {
                LabelType.Album => Constants.CONTENT_LABEL_TITLE,
                LabelType.Category => Constants.CONTENT_LABEL_CATEGORY,
                LabelType.Genre => Constants.CONTENT_LABEL_GENRE,
                LabelType.Location => Constants.CONTENT_LABEL_LOCATION,
                LabelType.None => Constants.CONTENT_LABEL_NONE,
                LabelType.Place => Constants.CONTENT_LABEL_PLACE,
                LabelType.Person => Constants.CONTENT_LABEL_PERSON,
                LabelType.Keyword => Constants.CONTENT_LABEL_KEYWORD,
                LabelType.Mood => Constants.CONTENT_LABEL_MOOD,
                LabelType.Tag => Constants.CONTENT_LABEL_TAG,
                LabelType.CaptionTag => Constants.CONTENT_LABEL_CAPTIONTAG,
                LabelType.CelebrityTag => Constants.CONTENT_LABEL_CELEBRITYTAG,
                LabelType.ContentModerationTag => Constants.CONTENT_LABEL_CONTENTMODERATIONTAG,
                LabelType.FaceTag => Constants.CONTENT_LABEL_FACETAG,
                LabelType.LabelTag => Constants.CONTENT_LABEL_LABELTAG,
                LabelType.PersonTrackingTag => Constants.CONTENT_LABEL_PERSONTRACKINGTAG,
                LabelType.SubtitleTag => Constants.CONTENT_LABEL_SUBTITLETAG,
                LabelType.EmotionTag => Constants.CONTENT_LABEL_EMOTIONTAG,
                LabelType.CutTag => Constants.CONTENT_LABEL_CUTTAG,
                LabelType.NFTTag => Constants.CONTENT_LABEL_NFTTAG,
                LabelType.DynamicTag => Constants.CONTENT_LABEL_DYNAMICTAG,
                LabelType.Skill => Constants.CONTENT_LABEL_SKILL,
                LabelType.Experience => Constants.CONTENT_LABEL_EXPERIENCE,
                LabelType.Showreel => Constants.CONTENT_LABEL_SHOWREEL,
                LabelType.Type => Constants.CONTENT_LABEL_TYPE,
                LabelType.Style => Constants.CONTENT_LABEL_STYLE,
                LabelType.Attribute => Constants.CONTENT_LABEL_ATTRIBUTE,
                LabelType.Instruction => Constants.CONTENT_LABEL_INSTRUCTION,
                _ => null
            };

            if (string.IsNullOrEmpty(propertyName)) continue;
            result.SetProperty(propertyName, cat.Value, true);

            if (cat.Key == LabelType.Genre)
                cat.Value.ForEach(genre =>
                {
                    LiveActions.Add(new BlueGuava.Library.Common.BusinessEntities.Action
                    {
                        Name = $"Content:Genre:{genre}"
                    });
                });
        }
    }

    private async Task<RatingCategory> FindParental(string countryCode, int ageLimit)
    {
        RatingCategory retVal = null;
        try
        {
            //TODO: handle GLOBAL icon
            if (string.IsNullOrEmpty(countryCode) || countryCode is "--" or "-") return retVal;
            var country = await countryRepository.RetrieveAsync<Country>("api/v1.0/Country/" + countryCode);
            var ratingCategories = country?.Categories?.Where(r => r.MinAge <= ageLimit);

            if (ratingCategories != null)
                retVal = ratingCategories.MaxBy(r => r.MinAge);
        }
        catch (Exception ex)
        {
            logger.LogError("FindParental error", ex.Message + " - " + ex.StackTrace);
            //do nothing because we don't want to break the whole process
        }

        return retVal;
    }

    /// <summary>
    /// Supported playback <see cref="Asset"/> <see cref="SubType"/> values
    /// </summary>
    private static readonly SubType[] AllowedFormats = new[]
        { SubType.DASH, SubType.DASH_4K, SubType.HLS, SubType.HLS_4K };


    private Dictionary<string, SubType[]> DetectExposedStreams(List<Asset> assets)
    {
        return assets.Where(a => !a.IsDeleted).Where(a => a.Type == AssetType.Audio || a.Type == AssetType.Video)
            .Where(a => !string.IsNullOrEmpty(a.PublicUrl)).GroupBy(a => a.Locale ?? string.Empty)
            .Select(g => new { g.Key, Formats = AllowedFormats.Intersect(g.Select(a => a.SubType)).ToArray() })
            .Where(e => e.Formats.Length > 0).ToDictionary(g => g.Key, g => g.Formats);
    }

    private static string GetGlobalizationPrefix(string? lang)
    {
        return string.IsNullOrEmpty(lang) || lang == "--" ? string.Empty : $"Globalization:{lang}:";
    }
}