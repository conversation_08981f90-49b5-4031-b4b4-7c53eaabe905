using BlueGuava.ContentManagement.Delivery.Conversion;
using BlueGuava.Library.Interop;
using Microsoft.Extensions.DependencyInjection;

namespace BlueGuava.ContentManagement.Delivery;

public static class DependencyInjection
{
    public static IServiceCollection AddConverters(this IServiceCollection services)
    {
        // NOTE: register singleton (IConverter<TEntity>, {TEntity}Converter)
        typeof(IConverter<>).Assembly.GetTypes().Where(t => t.IsClass).Where(t => !t.IsAbstract)
            .Where(t => !t.IsGenericTypeDefinition)
            .Select(t => new
            {
                t,
                i = t.GetInterfaces().Where(i => i.IsGenericType)
                    .SingleOrDefault(i => i.GetGenericTypeDefinition() == typeof(IConverter<>))
            })
            .Where(e => e.i != null).Select(e => ServiceDescriptor.Scoped(e.i!, e.t)).ForEach(sd => services.Add(sd));

        services.AddScoped<IActions, ActionProcessor>();
        services.AddScoped<IUrlTemplating, UrlTemplatingService>();

        return services;
    }
}