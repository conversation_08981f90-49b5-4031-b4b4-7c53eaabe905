namespace BlueGuava.ContentManagement.Delivery;

public static class Extensions
{
    public static Library.Interop.v2.Object AddExternalLink(this Library.Interop.v2.Object entity, string title,
        string? linkUrl)
    {
        if (string.IsNullOrEmpty(linkUrl)) return entity;

        if (!linkUrl.StartsWith("http")) linkUrl = "https://" + linkUrl;

        _ = entity.SetAction(new Library.Interop.v2.Action
        {
            Url = linkUrl,
            HttpMethod = "GET",
            ActionName = title
        });

        return entity;
    }
}