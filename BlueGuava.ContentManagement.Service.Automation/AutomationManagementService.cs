﻿using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using BlueGuava.Collections.Common;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.Logging;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using CorrelationId;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;

namespace BlueGuava.ContentManagement.Service.Automation;

public class AutomationManagementService : IAutomationManagementService
{
    private const string AutomationOffMessage =
        "Automated reference updates are turned off with key FeatureManagement:{featureFlag}";


    private readonly ILogger<AutomationManagementService> logger;
    private readonly IFeatureManager featureManager;
    private readonly IContentIntegration integration;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging;
    private readonly IMessageQueue<UpdateMessage> updateMessageQueue;
    private readonly ICorrelationContextAccessor correlationContextAccessor;

    public AutomationManagementService(
        ILogger<AutomationManagementService> logger,
        IFeatureManager featureManager,
        IContentIntegration integration,
        IMessageQueue<RelationshipUpdate> relationshipUpdateMessaging,
        ICorrelationContextAccessor correlationContextAccessor,
        IMessageQueue<UpdateMessage> updateMessageQueue)
    {
        this.logger = logger;
        this.featureManager = featureManager;
        this.integration = integration;
        this.relationshipUpdateMessaging = relationshipUpdateMessaging;
        this.correlationContextAccessor = correlationContextAccessor;
        this.updateMessageQueue = updateMessageQueue;
    }


    public async Task OnContentCreated(Content? sourceContent, ClaimsPrincipal user)
    {
        logger.Standards(correlationContextAccessor, nameof(AutomationManagementService), nameof(OnContentCreated))
            .Log(LogLevel.Information, "SourceContent: {SourceContent}, User: {User}", sourceContent, user);
        try
        {
            if (sourceContent == null) return;

            await CreatorAssignment(sourceContent.Id, user);
            await UpdateContentTags(sourceContent.Id, sourceContent.CreatedDate);

            //prevents infinite loop or wrong call
            if (sourceContent.Type == ContentType.LiveChat) return;

            if (sourceContent.AllowChat ?? false)
            {
                var voiceChat = await CreateVoiceChat(sourceContent, user);
                await CreateChatRoom(voiceChat, user);
            }

            //await CreateWatchParty(sourceContent, user);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(AutomationManagementService),
                    nameof(OnContentCreated))
                .Log(LogLevel.Error, ex, "SourceContent: {SourceContent}, User: {User}", sourceContent, user);
        }
    }

    public async Task OnIVSLiveCreated(Content liveSessionContent, ClaimsPrincipal user)
    {
        if (!await AllowAutomation("AllowAutomation_CreateChat")) return;

        if (liveSessionContent.AllowChat ?? false)
            await CreateChatRoom(liveSessionContent, user);

        await CreatorAssignment(liveSessionContent.Id, user);

        var customerId = user.GetCustomerId();

        await UpdateRelations(customerId, liveSessionContent.Id, CustomerRelation.Chat);
        await UpdateRelations(customerId, liveSessionContent.Id, CustomerRelation.Activity);

#warning TODO: send notification about live start (followers of content creator)
    }

    public Task OnChatConnect(Guid contentId, Guid customerId)
    {
        return HandleChatroomEvent(contentId, customerId, ActionKind.Add);
    }

    public Task OnChatDisconnect(Guid contentId, Guid customerId)
    {
        return HandleChatroomEvent(contentId, customerId, ActionKind.Del);
    }

    public async Task UpdateContentTags(Guid contentId, DateTime createdDate)
    {
        if (!await AllowAutomation("AllowAutomation_AutoTagsDate")) return;

        await updateMessageQueue.Enqueue(new UpdateLabelsMessage()
        {
            Id = contentId.ToString(),
            ContentId = contentId.ToString(),
            UpdateStrategy = ListUpdateStrategy.Add,
            Labels = new Dictionary<LabelType, List<string>>()
            {
                {
                    LabelType.Category, new List<string>()
                    {
                        createdDate.Year.ToString(),
                        createdDate.ToString("MMMM")
                    }
                }
            }
        });
    }

    public async Task CreatorAssignment(Guid contentId, ClaimsPrincipal user)
    {
        if (!await AllowAutomation("AllowAutomation_CreatorAssignment")) return;
        var customerId = user.GetCustomerId();
        await UpdateRelations(contentId, customerId, ContentRelation.Creator);
    }

    private async Task HandleChatroomEvent(Guid contentId, Guid customerId, ActionKind connectAction)
    {
        logger.Standards(correlationContextAccessor, nameof(AutomationManagementService),
                nameof(HandleChatroomEvent))
            .Log(LogLevel.Information, "ContentId: {ContentId}, CustomerId: {CustomerId}", contentId, customerId);

        try
        {
            //if (!await AllowAutomation("AllowAutomation_CreateChat")) return;

            await UpdateRelations(customerId, contentId, CustomerRelation.Chat, connectAction);
            await UpdateRelations(customerId, contentId, CustomerRelation.Activity, connectAction);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(AutomationManagementService),
                    nameof(HandleChatroomEvent))
                .Log(LogLevel.Error, ex, "ContentId: {ContentId}, CustomerId: {CustomerId}", contentId, customerId);
        }
    }

    public Task OnChimeConnect(Guid contentId, Guid customerId)
    {
        return HandleWatchPartyEvent(contentId, customerId, ActionKind.Add);
    }

    public Task OnChimeDisconnect(Guid contentId, Guid customerId)
    {
        return HandleWatchPartyEvent(contentId, customerId, ActionKind.Add);
    }

    private async Task HandleWatchPartyEvent(Guid contentId, Guid customerId, ActionKind connectAction)
    {
        logger.Standards(correlationContextAccessor, nameof(AutomationManagementService),
                nameof(HandleWatchPartyEvent))
            .Log(LogLevel.Information, "ContentId: {ContentId}, CustomerId: {CustomerId}", contentId, customerId);

        try
        {
            //if (!await AllowAutomation("AllowAutomation_CreateWatchParty")) return;

            await UpdateRelations(customerId, contentId, CustomerRelation.Watchparty, connectAction);
            await UpdateRelations(customerId, contentId, CustomerRelation.Activity, connectAction);
        }
        catch (Exception ex)
        {
            logger.Standards(correlationContextAccessor, nameof(AutomationManagementService),
                    nameof(HandleWatchPartyEvent))
                .Log(LogLevel.Error, ex, "ContentId: {ContentId}, CustomerId: {CustomerId}", contentId, customerId);
        }
    }


    /// <summary>
    /// <list type="number">
    /// <listheader>
    ///     <term>Creates a chatroom</term>
    /// </listheader>
    ///     <item>
    ///         <term>Creates a <see cref="ContentType.LiveChat"/> content based on <paramref name="parentContent"/>
    ///               using <see cref="IContentIntegration.CreateIVSChatContent(Content, ClaimsPrincipal)"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links the created content to <paramref name="parentContent"/> via <see cref="ContentRelation.Playlist"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links the created content to <paramref name="parentContent"/> via <see cref="ContentRelation.Reference"/></term>
    ///     </item>
    ///     <item>
    ///         <term>Links <paramref name="parentContent"/> to the created content via <see cref="ContentRelation.Reference"/></term>
    ///     </item>
    /// </list>
    /// </summary>
    /// <param name="parentContent">Parent content of the new chatroom</param>
    /// <param name="user">Customer's <see cref="ClaimsPrincipal"/></param>
    private async Task CreateChatRoom(Content parentContent, ClaimsPrincipal user)
    {
        //if (!await AllowAutomation("AllowAutomation_CreateChat")) return;

        var content = await integration.CreateIVSChatContent(new Content
        {
            Type = ContentType.LiveChat,
            OriginalTitle = $"{parentContent.OriginalTitle} Chat",
            Visibility = Visibility.Private
        }, user);

        //await UpdateRelations(parentContent.Id, content.Id, ContentRelation.Playlist);
        await UpdateRelations(parentContent.Id, content.Id, ContentRelation.Reference);
        await UpdateRelations(content.Id, parentContent.Id, ContentRelation.Reference);

        _ = integration.TracelogAction(parentContent.Id, null, "Success: Create live chat");
    }

    /// <summary>
    /// Creates chime voice chat and returns the created content
    /// </summary>
    /// <param name="parentContent"></param>
    /// <param name="user"></param>
    private async Task<Content> CreateVoiceChat(Content parentContent, ClaimsPrincipal user)
    {
        var content = await integration.CreateChimeContent(new Content
        {
            Type = ContentType.CameraCapture,
            OriginalTitle = $"{parentContent.OriginalTitle} Voice chat",
            Visibility = Visibility.Private
        }, user);

        await UpdateRelations(parentContent.Id, content.Id, ContentRelation.Reference);
        await UpdateRelations(content.Id, parentContent.Id, ContentRelation.Reference);

        var meetingId = Guid.TryParse(content.Properties[Consts.CONTENT_SESSIONID], out var id) ? id : (Guid?)null;
        _ = integration.TracelogAction(parentContent.Id, meetingId, "Success: WatchParty Created");

        return content;
    }

    private async Task UpdateRelations(
        Guid ownerEntity,
        Guid relatedTo,
        Relationship relation,
        ActionKind actionKind = ActionKind.Add)
    {
        await relationshipUpdateMessaging.Enqueue(new SingleRelationshipUpdate()
        {
            Action = actionKind,
            Relation = relation,
            SourceId = ownerEntity.ToString(),
            TargetId = relatedTo.ToString()
        });

        await RefreshRelationship(ownerEntity.ToString(), relation);
    }

    private async Task RefreshRelationship(string sourceId, Relationship relation)
    {
        await relationshipUpdateMessaging.Enqueue(new RelationshipRefresh()
        {
            Relation = relation,
            SourceId = sourceId
        }, 60); // with delay because of the processing speed
    }

    private async Task<bool> AllowAutomation(string featureFlag)
    {
        if (await featureManager.IsEnabledAsync(featureFlag)) return true;
        logger.LogInformation(AutomationOffMessage, featureFlag);
        return false;
    }
}