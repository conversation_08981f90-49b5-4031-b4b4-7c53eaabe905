﻿using Amazon.Extensions.NETCore.Setup;
using Amazon.Scheduler;
using Amazon.Scheduler.Model;
using Amazon.SecurityToken;

namespace BlueGuava.ContentManagement.Service.EventBridge.Scheduler;

public class EventBridgeSchedulerService : IEventBridgeSchedulerService
{
    private readonly IAmazonScheduler scheduler;
    private readonly IAmazonSecurityTokenService stsService;
    private readonly AWSOptions options;

    public EventBridgeSchedulerService(IAmazonScheduler amazonScheduler, IAmazonSecurityTokenService stsService,
        AWSOptions options)
    {
        scheduler = amazonScheduler;
        this.stsService = stsService;
        this.options = options;
    }

    public async Task DeleteExistingRule(string id)
    {
        await scheduler.DeleteScheduleAsync(new DeleteScheduleRequest { Name = id });
    }

    public async Task<bool> ScheduleOrUpdateNewRule(string id, DateTime? scheduleDate, string payload)
    {
        var schedulingExpression = GetSchedulingExpression(scheduleDate);
        try
        {
            var existingScheduler = await scheduler.GetScheduleAsync(new GetScheduleRequest { Name = id });
            if (existingScheduler != null && existingScheduler.HttpStatusCode == System.Net.HttpStatusCode.OK &&
                !string.IsNullOrEmpty(existingScheduler.ScheduleExpression))
            {
                if (existingScheduler.ScheduleExpression.ToLower() ==
                    schedulingExpression.ToLower()) // same cron expression, do nothing
                    return false;

                await scheduler.DeleteScheduleAsync(new DeleteScheduleRequest { Name = id });
            }
        }
        catch (ResourceNotFoundException)
        {
            // do not block execution where´s no resources
        }

        if (schedulingExpression == DELETE) return false; // only Delete when DateTime is null

        await SetArns();

        if (scheduleDate.HasValue && scheduleDate.Value.ToUniversalTime() < DateTime.UtcNow) return false;

        var scheduleResult = await scheduler.CreateScheduleAsync(new CreateScheduleRequest
        {
            Name = id,
            ScheduleExpression = schedulingExpression,
            State = ScheduleState.ENABLED,
            FlexibleTimeWindow = new FlexibleTimeWindow { Mode = FlexibleTimeWindowMode.OFF },
            Target = new Target
            {
                Arn = TargetArn,
                Input = payload,
                RoleArn = RoleArn,
                SqsParameters = new SqsParameters { MessageGroupId = "unique" }
            }
        });

        if (scheduleResult.HttpStatusCode == System.Net.HttpStatusCode.OK) return true;

        return false;
    }

    private string GetSchedulingExpression(DateTime? date)
    {
        if (!date.HasValue || date.Value == DateTime.MinValue) return DELETE;

        var dateFormat = date.Value.ToString(DATE_FORMAT);
        var cron = $"at({dateFormat})";
        return cron;
    }

    private async Task SetArns()
    {
        if (!string.IsNullOrEmpty(TargetArn) && !string.IsNullOrEmpty(RoleArn)) return;
        var getCallerIdentity =
            await stsService.GetCallerIdentityAsync(new Amazon.SecurityToken.Model.GetCallerIdentityRequest { });
        TargetArn = SQS_ARN_TEMPLATE.Replace("{region}", options.Region.SystemName)
            .Replace("{account}", getCallerIdentity.Account);
        RoleArn = ROLE_ARN_TEMPLATE.Replace("{account}", getCallerIdentity.Account);
    }

    private const string DELETE = "DELETE_ONLY";
    private const string DATE_FORMAT = "yyyy-MM-ddThh:mm:ss";
    private const string SQS_ARN_TEMPLATE = "arn:aws:sqs:{region}:{account}:ContentPublisher.fifo";
    private const string ROLE_ARN_TEMPLATE = "arn:aws:iam::{account}:role/content-scheduler-role";
    private string TargetArn { get; set; } = "";
    private string RoleArn { get; set; } = "";
}