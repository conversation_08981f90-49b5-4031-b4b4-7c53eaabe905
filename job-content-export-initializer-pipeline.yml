resources:
  repositories:
    - repository: templates
      type: git
      name: JE_Backend/ent-infrastructure

trigger:
  branches:
    include:
      - master
      - develop
  paths:
    include:
      - BlueGuava.Job.Content.Export.Initializer

jobs:
- job: Build_serverless
  pool:
    vmImage: 'ubuntu-latest'
  steps:
  - template: Pipelines/ent-serverless-dotnet6-template.yml@templates

    parameters:
      solution_name: 'BlueGuava.Job.Content.Export.Initializer'
      lambda_name: 'job-content-export-initializer'
